import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy
} from 'firebase/firestore';
import { db } from '../firebase';

export interface Subject {
  id: string;
  name: string;
  code: string;
  gradeId: string; // Reference to grades collection document
  description?: string;
  color?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SubjectFormData {
  name: string;
  code: string;
  gradeId: string;
  description?: string;
  color?: string;
  isActive?: boolean;
}

class SubjectService {
  private collection = collection(db, 'subjects');

  // Get all subjects
  async getAllSubjects(): Promise<Subject[]> {
    try {
      const snapshot = await getDocs(
        query(this.collection, orderBy('name', 'asc'))
      );
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Subject[];
    } catch (error) {
      console.error('Error fetching subjects:', error);
      throw new Error('Failed to fetch subjects');
    }
  }

  // Get subjects by grade
  async getSubjectsByGrade(gradeId: string): Promise<Subject[]> {
    try {
      const snapshot = await getDocs(
        query(
          this.collection,
          where('gradeId', '==', gradeId),
          orderBy('name', 'asc')
        )
      );
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Subject[];
    } catch (error) {
      console.error('Error fetching subjects by grade:', error);
      throw new Error('Failed to fetch subjects by grade');
    }
  }

  // Get subject by ID
  async getSubjectById(id: string): Promise<Subject | null> {
    try {
      const docRef = doc(this.collection, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        } as Subject;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching subject:', error);
      throw new Error('Failed to fetch subject');
    }
  }

  // Create new subject
  async createSubject(subjectData: SubjectFormData): Promise<string> {
    try {
      const now = new Date().toISOString();
      const newSubject = {
        ...subjectData,
        isActive: subjectData.isActive ?? true,
        createdAt: now,
        updatedAt: now
      };

      const docRef = await addDoc(this.collection, newSubject);
      return docRef.id;
    } catch (error) {
      console.error('Error creating subject:', error);
      throw error;
    }
  }

  // Update subject
  async updateSubject(id: string, updates: Partial<SubjectFormData>): Promise<void> {
    try {
      const docRef = doc(this.collection, id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error updating subject:', error);
      throw error;
    }
  }

  // Delete subject
  async deleteSubject(id: string): Promise<void> {
    try {
      const docRef = doc(this.collection, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting subject:', error);
      throw error;
    }
  }
}

export const subjectService = new SubjectService();
