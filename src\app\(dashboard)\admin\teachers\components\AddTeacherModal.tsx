"use client";
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/Button';
import { useGradesWithSections } from '@/hooks/useGrades';
import { useSubjects } from '@/hooks/useSubjects';
import { useAddTeacherWithAssignments } from '@/hooks/useTeachers';
import { TeacherFormWithAssignments, TeacherAssignmentFormData } from '@/lib/services/teacherService';
import { XMarkIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';

interface AddTeacherModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface Assignment {
  id: number;
  gradeId: string;
  subjectId: string;
  sectionIds: string[];
}

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
};

const modalVariants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: -50
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.3
    }
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    y: -50,
    transition: {
      duration: 0.2
    }
  }
};

export default function AddTeacherModal({ isOpen, onClose, onSuccess }: AddTeacherModalProps) {
  const { data: gradesWithSections = [], isLoading: isLoadingGrades } = useGradesWithSections();
  const { data: subjects = [], isLoading: isLoadingSubjects } = useSubjects();
  const addTeacherMutation = useAddTeacherWithAssignments();

  const [teacherForm, setTeacherForm] = useState({
    firstName: '',
    middleName: '',
    lastName: '',
    email: '',
    status: 'active' as 'active' | 'inactive'
  });

  const [assignments, setAssignments] = useState<Assignment[]>([
    { id: 1, gradeId: '', subjectId: '', sectionIds: [] }
  ]);

  const [nextAssignmentId, setNextAssignmentId] = useState(2);

  const handleTeacherInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setTeacherForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAssignmentChange = (assignmentId: number, field: string, value: string | string[]) => {
    setAssignments(prev => prev.map(assignment => {
      if (assignment.id === assignmentId) {
        const updated = { ...assignment, [field]: value };
        
        // Reset sections when grade changes
        if (field === 'gradeId') {
          updated.sectionIds = [];
        }
        
        return updated;
      }
      return assignment;
    }));
  };

  const addAssignment = () => {
    setAssignments(prev => [...prev, {
      id: nextAssignmentId,
      gradeId: '',
      subjectId: '',
      sectionIds: []
    }]);
    setNextAssignmentId(prev => prev + 1);
  };

  const removeAssignment = (assignmentId: number) => {
    if (assignments.length > 1) {
      setAssignments(prev => prev.filter(assignment => assignment.id !== assignmentId));
    }
  };

  const getSubjectsForGrade = (gradeId: string) => {
    return subjects.filter(subject => subject.gradeId === gradeId);
  };

  const getSectionsForGrade = (gradeId: string) => {
    const grade = gradesWithSections.find(g => g.id === gradeId);
    return grade?.sections || [];
  };

  const handleSectionToggle = (assignmentId: number, sectionId: string) => {
    setAssignments(prev => prev.map(assignment => {
      if (assignment.id === assignmentId) {
        const currentSections = assignment.sectionIds;
        const newSections = currentSections.includes(sectionId)
          ? currentSections.filter(id => id !== sectionId)
          : [...currentSections, sectionId];
        
        return { ...assignment, sectionIds: newSections };
      }
      return assignment;
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate teacher form
    if (!teacherForm.firstName || !teacherForm.lastName || !teacherForm.email) {
      alert('Please fill in all required teacher information');
      return;
    }

    // Validate assignments
    const validAssignments = assignments.filter(assignment => 
      assignment.gradeId && assignment.subjectId && assignment.sectionIds.length > 0
    );

    if (validAssignments.length === 0) {
      alert('Please add at least one valid assignment (grade, subject, and sections)');
      return;
    }

    const formData: TeacherFormWithAssignments = {
      ...teacherForm,
      assignments: validAssignments.map(assignment => ({
        gradeId: assignment.gradeId,
        subjectId: assignment.subjectId,
        sectionIds: assignment.sectionIds
      }))
    };

    try {
      await addTeacherMutation.mutateAsync(formData);
      handleClose();
      onSuccess();
    } catch (error) {
      console.error('Error creating teacher:', error);
    }
  };

  const handleClose = () => {
    setTeacherForm({
      firstName: '',
      middleName: '',
      lastName: '',
      email: '',
      status: 'active'
    });
    setAssignments([{ id: 1, gradeId: '', subjectId: '', sectionIds: [] }]);
    setNextAssignmentId(2);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            onClick={handleClose}
          />

          {/* Modal */}
          <motion.div
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed left-[50%] top-[50%] z-50 w-full max-w-4xl max-h-[90vh] translate-x-[-50%] translate-y-[-50%] bg-white border-2 border-[#84a98c] shadow-lg p-6 overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="mb-6">
              <h2 className="text-lg font-semibold leading-none tracking-tight text-[#84a98c] mb-2">Add New Teacher</h2>
              <p className="text-sm text-gray-600">
                Create a new teacher account and assign teaching responsibilities.
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Teacher Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-[#84a98c] mb-4">Teacher Information</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-[#84a98c] mb-1">
                      First Name *
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={teacherForm.firstName}
                      onChange={handleTeacherInputChange}
                      className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#84a98c] mb-1">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={teacherForm.lastName}
                      onChange={handleTeacherInputChange}
                      className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#84a98c] mb-1">
                      Middle Name
                    </label>
                    <input
                      type="text"
                      name="middleName"
                      value={teacherForm.middleName}
                      onChange={handleTeacherInputChange}
                      className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#84a98c] mb-1">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={teacherForm.email}
                      onChange={handleTeacherInputChange}
                      className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#84a98c] mb-1">
                      Status
                    </label>
                    <select
                      name="status"
                      value={teacherForm.status}
                      onChange={handleTeacherInputChange}
                      className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Teaching Assignments */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-[#84a98c] mb-2">Teaching Assignments</h3>
                  <Button
                    type="button"
                    onClick={addAssignment}
                    variant="outlined"
                    theme="primary"
                    size="sm"
                    leftIcon={<PlusIcon className="w-4 h-4" />}
                  >
                    Add Assignment
                  </Button>
                </div>

                {/* Assignment List */}
                <div className="space-y-4">
                  {assignments.map((assignment, index) => (
                    <div key={assignment.id} className="bg-gray-50 border border-gray-300 p-4 rounded">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium text-[#84a98c]">Assignment {index + 1}</h4>
                        {assignments.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeAssignment(assignment.id)}
                            className="text-red-600 hover:text-red-800 transition-colors"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Grade Selection */}
                        <div>
                          <label className="block text-sm font-medium text-[#84a98c] mb-1">
                            Grade Level *
                          </label>
                          <select
                            value={assignment.gradeId}
                            onChange={(e) => handleAssignmentChange(assignment.id, 'gradeId', e.target.value)}
                            className="w-full px-3 py-3 bg-white border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                            disabled={isLoadingGrades}
                          >
                            <option value="">Select Grade</option>
                            {gradesWithSections.map((grade) => (
                              <option key={grade.id} value={grade.id}>
                                {grade.name}
                              </option>
                            ))}
                          </select>
                        </div>

                        {/* Subject Selection */}
                        <div>
                          <label className="block text-sm font-medium text-[#84a98c] mb-1">
                            Subject *
                          </label>
                          <select
                            value={assignment.subjectId}
                            onChange={(e) => handleAssignmentChange(assignment.id, 'subjectId', e.target.value)}
                            className="w-full px-3 py-3 bg-white border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                            disabled={!assignment.gradeId || isLoadingSubjects}
                          >
                            <option value="">Select Subject</option>
                            {getSubjectsForGrade(assignment.gradeId).map((subject) => (
                              <option key={subject.id} value={subject.id}>
                                {subject.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>

                      {/* Section Selection */}
                      {assignment.gradeId && (
                        <div className="mt-4">
                          <label className="block text-sm font-medium text-[#84a98c] mb-2">
                            Sections * (Select one or more)
                          </label>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            {getSectionsForGrade(assignment.gradeId).map((section) => (
                              <label
                                key={section.id}
                                className="flex items-center space-x-2 p-2 bg-white border border-gray-200 rounded hover:bg-gray-50 cursor-pointer"
                              >
                                <input
                                  type="checkbox"
                                  checked={assignment.sectionIds.includes(section.id)}
                                  onChange={() => handleSectionToggle(assignment.id, section.id)}
                                  className="rounded border-gray-300 text-[#84a98c] focus:ring-[#84a98c]"
                                />
                                <span className="text-sm text-gray-700">{section.name}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Footer */}
              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  type="button"
                  onClick={handleClose}
                  variant="outlined"
                  theme="neutral"
                  disabled={addTeacherMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="filled"
                  theme="success"
                  disabled={addTeacherMutation.isPending}
                  loading={addTeacherMutation.isPending}
                >
                  {addTeacherMutation.isPending ? 'Creating...' : 'Create Teacher'}
                </Button>
              </div>
            </form>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
