"use client";
import React from 'react';
import { cn } from '@/lib/utils';

interface BaseCardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  borderColor?: 'default' | 'primary' | 'student' | 'teacher' | 'admin';
  padding?: 'sm' | 'md' | 'lg';
}

const borderColorMap = {
  default: 'border-gray-200 hover:border-gray-300',
  primary: 'border-gray-200 hover:border-[#84a98c]',
  student: 'border-gray-200 hover:border-[#5a9cff]',
  teacher: 'border-gray-200 hover:border-[#b084ff]',
  admin: 'border-gray-200 hover:border-slate-800',
};

const paddingMap = {
  sm: 'p-3',
  md: 'p-4',
  lg: 'p-6',
};

export default function BaseCard({ 
  children, 
  className, 
  hover = true,
  borderColor = 'primary',
  padding = 'md'
}: BaseCardProps) {
  return (
    <div 
      className={cn(
        'border-2 bg-white shadow-sm',
        borderColorMap[borderColor],
        paddingMap[padding],
        hover && 'transition-colors duration-200',
        className
      )}
    >
      {children}
    </div>
  );
}
