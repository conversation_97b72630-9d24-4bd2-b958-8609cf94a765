interface FeatureItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const FeatureItem: React.FC<FeatureItemProps> = ({ icon, title, description }) => {
  return (
    <div className="bg-sage-50 p-6 border-l-4 border-sage-600 hover:bg-sage-100 transition-colors">
      <div className="flex items-start space-x-4">
        <div className="w-6 h-6 text-sage-600 mt-1 flex-shrink-0">
          {icon}
        </div>
        <div>
          <h3 className="text-sage-800 font-medium mb-1" style={{fontWeight: 300}}>{title}</h3>
          <p className="text-sage-700 text-sm" style={{fontWeight: 300}}>{description}</p>
        </div>
      </div>
    </div>
  );
};

export default FeatureItem; 