// Import functions dynamically to avoid circular dependencies

/**
 * Complete seeder for <PERSON>'s schema implementation
 * This will populate the database with the necessary data structure
 */
export async function seedClaudeSchema(): Promise<void> {
  try {
    console.log('🚀 Starting Claude Schema Migration...');
    console.log('');

    // Step 1: Seed Grades and Sections (already implemented)
    console.log('📚 Step 1: Seeding Grades and Sections...');
    const { gradeSeeder } = await import('./gradeSeeder');
    await gradeSeeder.seedGradesAndSections();
    console.log('✅ Grades and Sections seeded successfully!');
    console.log('');

    // Step 2: Seed Subjects
    console.log('📖 Step 2: Seeding Subjects...');
    const { seedSubjects } = await import('./subjectSeeder');
    await seedSubjects();
    console.log('✅ Subjects seeded successfully!');
    console.log('');

    console.log('🎉 Claude Schema Migration completed successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log('  ✅ Grades and Sections created');
    console.log('  ✅ Subjects created and linked to grades');
    console.log('  ✅ Ready for teacher assignments');
    console.log('');
    console.log('🔄 Next Steps:');
    console.log('  1. Create teachers using the new assignment-based form');
    console.log('  2. Teacher assignments will be stored in teacher_subject_assignments collection');
    console.log('  3. Students will reference grades and sections directly');
    console.log('');

  } catch (error) {
    console.error('❌ Error during Claude Schema Migration:', error);
    throw error;
  }
}

/**
 * Development seeder - clears and reseeds everything
 */
export async function seedClaudeSchemaForDevelopment(): Promise<void> {
  try {
    console.log('🔧 Starting Development Claude Schema Seeding...');
    console.log('⚠️  This will clear existing data and reseed everything!');
    console.log('');

    // Import clear functions will be done inline

    // Clear existing data
    console.log('🧹 Clearing existing data...');
    const { clearSubjects } = await import('./subjectSeeder');
    const { gradeSeeder } = await import('./gradeSeeder');
    await clearSubjects();
    await gradeSeeder.clearGradesAndSections();
    console.log('✅ Existing data cleared!');
    console.log('');

    // Seed new data
    await seedClaudeSchema();

    console.log('🎯 Development Claude Schema Seeding completed!');

  } catch (error) {
    console.error('❌ Error during development seeding:', error);
    throw error;
  }
}

/**
 * Check if Claude schema is already implemented
 */
export async function checkClaudeSchemaStatus(): Promise<{
  gradesExist: boolean;
  subjectsExist: boolean;
  isReady: boolean;
}> {
  try {
    const { gradeService } = await import('../services/gradeService');
    const { subjectService } = await import('../services/subjectService');

    const grades = await gradeService.getAllGrades();
    const subjects = await subjectService.getAllSubjects();

    const gradesExist = grades.length > 0;
    const subjectsExist = subjects.length > 0;
    const isReady = gradesExist && subjectsExist;

    return {
      gradesExist,
      subjectsExist,
      isReady
    };

  } catch (error) {
    console.error('Error checking Claude schema status:', error);
    return {
      gradesExist: false,
      subjectsExist: false,
      isReady: false
    };
  }
}

/**
 * Migration helper - migrates existing teachers to new schema
 * This is for existing installations that need to migrate
 */
export async function migrateExistingTeachersToClaudeSchema(): Promise<void> {
  try {
    console.log('🔄 Starting Teacher Migration to Claude Schema...');
    
    // This would need to be implemented based on existing teacher data
    // For now, we'll just log that manual migration is needed
    console.log('⚠️  Teacher migration requires manual intervention:');
    console.log('  1. Export existing teacher data');
    console.log('  2. Create new teacher records using basic info only');
    console.log('  3. Create teacher_subject_assignments for each teacher');
    console.log('  4. Verify all assignments are correct');
    console.log('');
    console.log('💡 Use the new Teacher Form to create teachers with assignments');

  } catch (error) {
    console.error('❌ Error during teacher migration:', error);
    throw error;
  }
}

/**
 * Validation helper - ensures Claude schema is properly implemented
 */
export async function validateClaudeSchema(): Promise<boolean> {
  try {
    console.log('🔍 Validating Claude Schema Implementation...');

    const status = await checkClaudeSchemaStatus();

    if (!status.gradesExist) {
      console.log('❌ Grades not found');
      return false;
    }

    if (!status.subjectsExist) {
      console.log('❌ Subjects not found');
      return false;
    }

    // Check if subjects are properly linked to grades
    const { subjectService } = await import('../services/subjectService');
    const { gradeService } = await import('../services/gradeService');

    const grades = await gradeService.getAllGrades();
    const subjects = await subjectService.getAllSubjects();

    let allSubjectsLinked = true;
    for (const grade of grades) {
      const gradeSubjects = subjects.filter(s => s.gradeId === grade.id);
      if (gradeSubjects.length === 0) {
        console.log(`❌ No subjects found for ${grade.name}`);
        allSubjectsLinked = false;
      } else {
        console.log(`✅ ${grade.name}: ${gradeSubjects.length} subjects`);
      }
    }

    if (!allSubjectsLinked) {
      return false;
    }

    console.log('✅ Claude Schema validation passed!');
    return true;

  } catch (error) {
    console.error('❌ Error during schema validation:', error);
    return false;
  }
}

// Export individual functions for flexibility
export async function exportedSeedGrades() {
  const { gradeSeeder } = await import('./gradeSeeder');
  return gradeSeeder.seedGradesAndSections();
}

export async function exportedSeedSubjects() {
  const { seedSubjects } = await import('./subjectSeeder');
  return seedSubjects();
}
