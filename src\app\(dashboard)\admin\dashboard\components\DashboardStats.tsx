"use client";
import React from 'react';
import { 
  AcademicCapIcon, 
  UserPlusIcon, 
  ClipboardDocumentListIcon, 
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon
} from "@heroicons/react/24/outline";

interface StatsCardProps {
  title: string;
  value: number;
  icon: React.ComponentType<any>;
  color: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'indigo';
  subtitle?: string;
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, icon: Icon, color, subtitle }) => {
  const colorClasses = {
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      icon: 'text-blue-600',
      text: 'text-blue-900',
      value: 'text-blue-700'
    },
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200', 
      icon: 'text-green-600',
      text: 'text-green-900',
      value: 'text-green-700'
    },
    yellow: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      icon: 'text-yellow-600', 
      text: 'text-yellow-900',
      value: 'text-yellow-700'
    },
    red: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      icon: 'text-red-600',
      text: 'text-red-900', 
      value: 'text-red-700'
    },
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      icon: 'text-purple-600',
      text: 'text-purple-900',
      value: 'text-purple-700'
    },
    indigo: {
      bg: 'bg-indigo-50',
      border: 'border-indigo-200',
      icon: 'text-indigo-600', 
      text: 'text-indigo-900',
      value: 'text-indigo-700'
    }
  };

  const classes = colorClasses[color];

  return (
    <div className={`${classes.bg} ${classes.border} border-2 p-6 shadow-sm hover:shadow-md transition-shadow duration-200`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className={`text-sm font-medium ${classes.text} mb-1`}>{title}</p>
          <p className={`text-3xl font-bold ${classes.value} mb-1`}>{value.toLocaleString()}</p>
          {subtitle && (
            <p className={`text-xs ${classes.text} opacity-75`}>{subtitle}</p>
          )}
        </div>
        <div className={`${classes.icon} ml-4`}>
          <Icon className="w-8 h-8" />
        </div>
      </div>
    </div>
  );
};

interface DashboardStatsProps {
  students: any[];
  teachers: any[];
  exams: any[];
  activities: any[];
}

const DashboardStats: React.FC<DashboardStatsProps> = ({ students, teachers, exams, activities }) => {
  const pendingExams = exams.filter(e => e.status === "pending").length;
  const approvedExams = exams.filter(e => e.status === "approved").length;
  const rejectedExams = exams.filter(e => e.status === "rejected").length;
  const activeTeachers = teachers.filter(t => t.status === "active" || !t.status).length;
  const recentActivities = activities.filter(a => {
    const activityDate = new Date(a.timestamp);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - activityDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 7;
  }).length;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
      <StatsCard
        title="Total Students"
        value={students.length}
        icon={AcademicCapIcon}
        color="blue"
        subtitle="Registered students"
      />
      
      <StatsCard
        title="Active Teachers"
        value={activeTeachers}
        icon={UserPlusIcon}
        color="green"
        subtitle={`${teachers.length} total teachers`}
      />
      
      <StatsCard
        title="Pending Exams"
        value={pendingExams}
        icon={ClockIcon}
        color="yellow"
        subtitle="Awaiting approval"
      />
      
      <StatsCard
        title="Approved Exams"
        value={approvedExams}
        icon={CheckCircleIcon}
        color="green"
        subtitle="Ready for students"
      />
      
      <StatsCard
        title="Rejected Exams"
        value={rejectedExams}
        icon={XCircleIcon}
        color="red"
        subtitle="Need revision"
      />
      
      <StatsCard
        title="Total Exams"
        value={exams.length}
        icon={ClipboardDocumentListIcon}
        color="indigo"
        subtitle="All exam submissions"
      />
      
      <StatsCard
        title="Recent Activities"
        value={recentActivities}
        icon={CheckCircleIcon}
        color="purple"
        subtitle="Last 7 days"
      />
      
      <StatsCard
        title="System Health"
        value={100}
        icon={CheckCircleIcon}
        color="green"
        subtitle="All systems operational"
      />
    </div>
  );
};

export default DashboardStats;
