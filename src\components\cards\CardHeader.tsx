"use client";
import React from 'react';
import { cn } from '@/lib/utils';

interface CardHeaderProps {
  title: string;
  subtitle?: string;
  badge?: React.ReactNode;
  className?: string;
  titleColor?: 'default' | 'primary' | 'student' | 'teacher' | 'admin';
}

const titleColorMap = {
  default: 'text-gray-900',
  primary: 'text-[#84a98c]',
  student: 'text-[#4285f4]',
  teacher: 'text-[#9c6bff]',
  admin: 'text-slate-800',
};

export default function CardHeader({ 
  title, 
  subtitle, 
  badge, 
  className,
  titleColor = 'primary'
}: CardHeaderProps) {
  return (
    <div className={cn('flex justify-between items-start mb-3', className)}>
      <div className="flex-1">
        <h3 className={cn(
          'font-semibold text-lg leading-tight',
          titleColorMap[titleColor]
        )}>
          {title}
        </h3>
        {subtitle && (
          <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
        )}
      </div>
      {badge && (
        <div className="ml-2 flex-shrink-0">
          {badge}
        </div>
      )}
    </div>
  );
}
