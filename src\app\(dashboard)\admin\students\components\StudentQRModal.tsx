"use client";
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { StudentCardTemplate } from '@/components/cards';
import { Button } from '@/components/ui/Button';

interface StudentQRModalProps {
  student: any;
  isOpen: boolean;
  onClose: () => void;
}

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
};

const modalVariants = {
  hidden: { 
    opacity: 0, 
    scale: 0.8,
    y: -50
  },
  visible: { 
    opacity: 1, 
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.3
    }
  },
  exit: { 
    opacity: 0, 
    scale: 0.8,
    y: -50,
    transition: {
      duration: 0.2
    }
  }
};

export default function StudentQRModal({ student, isOpen, onClose }: StudentQRModalProps) {
  if (!student || !student.qrUrl) return null;

  const getStudentDisplayName = () => {
    if (student.fullName && Array.isArray(student.fullName)) {
      // New structure: fullName is an array [firstName, middleName, lastName]
      const [firstName, middleName, lastName] = student.fullName;
      return {
        firstName: firstName || '',
        middleName: middleName || '',
        lastName: lastName || '',
        extension: student.extension || ''
      };
    } else if (student.firstName && student.lastName) {
      // Old structure: individual name fields
      return {
        firstName: student.firstName,
        middleName: student.middleName || '',
        lastName: student.lastName,
        extension: student.extension || ''
      };
    }
    return {
      firstName: 'Unknown',
      middleName: '',
      lastName: 'Student',
      extension: ''
    };
  };
  const studentName = getStudentDisplayName();
  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = student.qrUrl;
    link.download = `${studentName.lastName}_${studentName.firstName}_QR.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>QR Code - ${studentName.firstName} ${studentName.lastName}</title>
            <style>
              body { 
                font-family: Arial, sans-serif; 
                text-align: center; 
                padding: 20px; 
              }
              img { 
                max-width: 300px; 
                max-height: 300px; 
              }
              h2 { 
                color: #84a98c; 
                margin-bottom: 10px; 
              }
              p { 
                color: #666; 
                margin: 5px 0; 
              }
            </style>
          </head>
          <body>
            <h2>Student QR Code</h2>
            <p><strong>Name:</strong> ${studentName.firstName} ${studentName.lastName}</p>
            <p><strong>LRN:</strong> ${student.lrn}</p>
            <br>
            <img src="${student.qrUrl}" alt="QR Code" />
            <p>Scan this QR code for student identification</p>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed left-[50%] top-[50%] z-50 w-full max-w-lg max-h-[90vh] translate-x-[-50%] translate-y-[-50%] bg-white border-2 border-[#84a98c] shadow-lg p-6 overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
          {/* Header - matching View/Edit modal structure */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold leading-none tracking-tight text-[#84a98c] mb-2">Student QR Code</h2>
            <p className="text-sm text-gray-600">
              QR Code for {studentName.firstName} {studentName.lastName} 
            </p>
          </div>

          {/* Content - matching View modal structure */}
          <div className="space-y-6">
            {/* QR Code Display */}
            <div className="text-center">
              <label className="block text-sm font-medium text-[#84a98c] mb-3">QR Code</label>
              <div className="bg-gray-50 p-4 border rounded">
                <img
                  src={student.qrUrl}
                  alt={`QR Code for ${studentName.firstName} ${studentName.lastName}`}
                  className="mx-auto w-64 h-64 border border-gray-200 rounded shadow-sm"
                />
              </div>
            </div>

            {/* Student Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#84a98c] mb-1">Student Name</label>
                <p className="text-gray-700 bg-gray-50 p-3 border rounded">
                  {studentName.firstName} {studentName.middleName} {studentName.lastName}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-[#84a98c] mb-1">LRN</label>
                <p className="text-gray-700 bg-gray-50 p-3 border rounded">
                  {student.lrn}
                </p>
              </div>
            </div>
          </div>

          {/* Footer - matching View modal structure */}
          <div className="flex justify-end space-x-2 mt-6">
            <Button
              onClick={handleDownload}
              variant="outlined"
              theme="primary"
            >
              Download
            </Button>
            <Button
              onClick={handlePrint}
              variant="outlined"
              theme="primary"
            >
              Print
            </Button>
            <Button
              onClick={onClose}
              variant="outlined"
              theme="neutral"
            >
              Close
            </Button>
          </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
