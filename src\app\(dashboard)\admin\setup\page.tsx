"use client";
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import {
  seedClaudeSchema,
  seedClaudeSchemaForDevelopment,
  checkClaudeSchemaStatus,
  validateClaudeSchema
} from '@/lib/seeders/claudeSchemaSeeder';
import toast from 'react-hot-toast';

interface SchemaStatus {
  gradesExist: boolean;
  subjectsExist: boolean;
  isReady: boolean;
}

export default function SetupPage() {
  const [status, setStatus] = useState<SchemaStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const checkStatus = async () => {
    setIsChecking(true);
    try {
      const currentStatus = await checkClaudeSchemaStatus();
      setStatus(currentStatus);
      addLog('Schema status checked');
    } catch (error) {
      addLog(`Error checking status: ${error}`);
    } finally {
      setIsChecking(false);
    }
  };

  const runSeeder = async (isDev = false) => {
    setIsLoading(true);
    setLogs([]);

    try {
      addLog(`Starting ${isDev ? 'development' : 'production'} seeder...`);

      if (isDev) {
        await seedClaudeSchemaForDevelopment();
        addLog('Development seeder completed successfully!');
      } else {
        await seedClaudeSchema();
        addLog('Production seeder completed successfully!');
      }

      // Refresh status
      await checkStatus();

    } catch (error) {
      addLog(`Seeder failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const runValidation = async () => {
    setIsLoading(true);
    try {
      addLog('Starting schema validation...');
      const isValid = await validateClaudeSchema();
      addLog(`Schema validation ${isValid ? 'passed' : 'failed'}!`);
    } catch (error) {
      addLog(`Validation error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkStatus();
  }, []);

  return (
    <div className="min-h-screen bg-[#FAFAF6] p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white border-2 border-[#84a98c] p-6 mb-6">
          <h1 className="text-2xl font-bold text-[#84a98c] mb-2">
            Database Setup & Migration
          </h1>
          <p className="text-gray-600">
            Set up Claude's recommended schema structure for GREENTECH
          </p>
        </div>

        {/* Status Card */}
        <div className="bg-white border-2 border-[#84a98c] p-6 mb-6">
          <h2 className="text-lg font-semibold text-[#84a98c] mb-4">
            Current Schema Status
          </h2>

          {isChecking ? (
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-[#84a98c] border-t-transparent rounded-full animate-spin"></div>
              <span className="text-gray-600">Checking status...</span>
            </div>
          ) : status ? (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <span className={`w-3 h-3 rounded-full ${status.gradesExist ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span className="text-gray-700">Grades & Sections: {status.gradesExist ? 'Ready' : 'Missing'}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`w-3 h-3 rounded-full ${status.subjectsExist ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span className="text-gray-700">Subjects: {status.subjectsExist ? 'Ready' : 'Missing'}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`w-3 h-3 rounded-full ${status.isReady ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
                <span className="text-gray-700 font-medium">
                  Overall Status: {status.isReady ? 'Ready for Use' : 'Setup Required'}
                </span>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">Unable to check status</p>
          )}

          <div className="mt-4">
            <Button
              onClick={checkStatus}
              disabled={isChecking}
              variant="outline"
              className="text-[#84a98c] border-[#84a98c]"
            >
              {isChecking ? 'Checking...' : 'Refresh Status'}
            </Button>
          </div>
        </div>

        {/* Actions Card */}
        <div className="bg-white border-2 border-[#84a98c] p-6 mb-6">
          <h2 className="text-lg font-semibold text-[#84a98c] mb-4">
            Setup Actions
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border border-gray-200 p-4 rounded">
              <h3 className="font-medium text-gray-900 mb-2">Production Setup</h3>
              <p className="text-sm text-gray-600 mb-3">
                Safely add missing data without affecting existing records
              </p>
              <Button
                onClick={() => runSeeder(false)}
                disabled={isLoading}
                className="w-full bg-[#84a98c] hover:bg-[#6b8a6e] text-white"
              >
                {isLoading ? 'Running...' : 'Run Production Seeder'}
              </Button>
            </div>

            <div className="border border-gray-200 p-4 rounded">
              <h3 className="font-medium text-gray-900 mb-2">Development Setup</h3>
              <p className="text-sm text-gray-600 mb-3">
                ⚠️ Clears existing data and reseeds everything
              </p>
              <Button
                onClick={() => runSeeder(true)}
                disabled={isLoading}
                className="w-full bg-orange-600 hover:bg-orange-700 text-white"
              >
                {isLoading ? 'Running...' : 'Run Dev Seeder'}
              </Button>
            </div>

            <div className="border border-gray-200 p-4 rounded">
              <h3 className="font-medium text-gray-900 mb-2">Validate Schema</h3>
              <p className="text-sm text-gray-600 mb-3">
                Check if the schema is properly implemented
              </p>
              <Button
                onClick={runValidation}
                disabled={isLoading}
                variant="outline"
                className="w-full text-[#84a98c] border-[#84a98c]"
              >
                {isLoading ? 'Validating...' : 'Validate Schema'}
              </Button>
            </div>
          </div>
        </div>
        {/* Logs Card */}
        {logs.length > 0 && (
          <div className="bg-white border-2 border-[#84a98c] p-6 mb-6">
            <h2 className="text-lg font-semibold text-[#84a98c] mb-4">
              Activity Log
            </h2>
            <div className="bg-gray-50 p-4 rounded max-h-64 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index} className="text-sm text-gray-700 font-mono mb-1">
                  {log}
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Button
                onClick={() => setLogs([])}
                variant="outline"
                className="text-gray-600 border-gray-300"
              >
                Clear Logs
              </Button>
            </div>
          </div>
        )}

        {/* Information Card */}
        <div className="bg-blue-50 border border-blue-200 p-6">
          <h2 className="text-lg font-semibold text-blue-800 mb-4">
            About Claude's Schema
          </h2>
          <div className="text-sm text-blue-700 space-y-2">
            <p>
              <strong>What this does:</strong> Implements Claude's recommended database structure for better organization and scalability.
            </p>
            <p>
              <strong>Key Changes:</strong>
            </p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Separates subjects into their own collection linked to grades</li>
              <li>Creates teacher_subject_assignments for flexible teacher management</li>
              <li>Maintains normalized data structure for better performance</li>
              <li>Supports the Philippine K-12 curriculum structure</li>
            </ul>
            <p>
              <strong>After setup:</strong> Use the new Teacher Form to create teachers with subject assignments.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
