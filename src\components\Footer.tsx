const Footer: React.FC = () => {
  return (
    <footer className="bg-[#57735d] text-white py-12 px-6 lg:px-8 border-t-4 border-[#84a98c]">
      <div className="max-w-7xl mx-auto">
        <div className="space-y-8">
          {/* Main Footer Content */}
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-6 lg:space-y-0">
            {/* Logo and School Name */}
            <div className="flex items-center space-x-4">
              <img src="/logos/greentech-white.png" alt="GreenTech Logo" className="w-12 h-12 object-contain" />
              <div className="flex flex-col">
                <span className="text-2xl text-white font-semibold leading-tight">GreenTech</span>
                <span className="text-sm text-white/80 font-medium">Sustainable Digital Assessment Platform</span>
              </div>
            </div>

            {/* School Information */}
            <div className="text-center lg:text-right">
              <div className="text-white/90 text-sm font-medium mb-2">
                Pinagbarilan Elementary School
              </div>
              <div className="text-white/70 text-sm">
                © 2024 All rights reserved.
              </div>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="border-t border-white/20 pt-6">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              {/* Legal Links */}
              <div className="flex flex-wrap items-center justify-center md:justify-start gap-6">
                <a href="#privacy" className="text-white/70 hover:text-white transition-colors text-sm font-medium">
                  Privacy Policy
                </a>
                <a href="#terms" className="text-white/70 hover:text-white transition-colors text-sm font-medium">
                  Terms & Conditions
                </a>
                <a href="#data-security" className="text-white/70 hover:text-white transition-colors text-sm font-medium">
                  Data Security
                </a>
                <a href="#accessibility" className="text-white/70 hover:text-white transition-colors text-sm font-medium">
                  Accessibility
                </a>
              </div>

              {/* Social Media */}
              <div className="flex items-center space-x-3">
                <a
                  href="https://www.facebook.com/104755PinagbarilanElementarySchool"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-white/70 hover:text-white transition-colors flex items-center space-x-2 bg-white/10 hover:bg-white/20 px-3 py-2"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                  <span className="text-sm font-medium">Follow Us</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 