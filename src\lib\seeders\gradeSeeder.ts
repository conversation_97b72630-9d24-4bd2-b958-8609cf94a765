/**
 * Grade Seeder
 * Creates grade levels following <PERSON>'s schema
 *
 * Structure: Grades 4, 5, 6 (Elementary focus for Pinagbarilan Elementary School)
 */

import { db } from '@/lib/firebase';
import { collection, addDoc, getDocs, deleteDoc, writeBatch, doc, updateDoc } from 'firebase/firestore';
import { Grade } from '@/lib/types/school';

/**
 * Clear all grades from the database
 */
export async function clearGrades() {
  console.log('🧹 Clearing existing grades...');

  try {
    const gradesSnapshot = await getDocs(collection(db, 'grades'));
    const batch = writeBatch(db);

    gradesSnapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    if (gradesSnapshot.docs.length > 0) {
      await batch.commit();
      console.log(`✅ Cleared ${gradesSnapshot.docs.length} grades`);
    } else {
      console.log('ℹ️ No grades to clear');
    }
  } catch (error) {
    console.error('❌ Error clearing grades:', error);
    throw error;
  }
}

/**
 * Create grade levels for elementary school
 */
export async function seedGrades(): Promise<Grade[]> {
  console.log('📚 Creating grade levels...');

  const grades: Grade[] = [];
  const now = new Date().toISOString();

  // Elementary grades for Pinagbarilan Elementary School
  const gradeDefinitions = [
    { name: 'Grade 4', level: 4 },
    { name: 'Grade 5', level: 5 },
    { name: 'Grade 6', level: 6 }
  ];

  try {
    for (const gradeDef of gradeDefinitions) {
      const gradeData = {
        name: gradeDef.name,
        level: gradeDef.level,
        isActive: true,
        createdAt: now,
        updatedAt: now
      };

      const docRef = await addDoc(collection(db, 'grades'), gradeData);

      const grade: Grade = {
        id: docRef.id,
        ...gradeData
      };

      grades.push(grade);
      console.log(`   ✅ Created ${grade.name} (Level ${grade.level})`);
    }

    console.log(`✅ Successfully created ${grades.length} grades`);
    return grades;

  } catch (error) {
    console.error('❌ Error creating grades:', error);
    throw error;
  }
}

/**
 * Get all grades
 */
export async function getAllGrades(): Promise<Grade[]> {
  try {
    const gradesSnapshot = await getDocs(collection(db, 'grades'));
    const grades: Grade[] = [];

    gradesSnapshot.docs.forEach((doc) => {
      grades.push({
        id: doc.id,
        ...doc.data()
      } as Grade);
    });

    return grades.sort((a, b) => a.level - b.level);
  } catch (error) {
    console.error('❌ Error getting grades:', error);
    throw error;
  }
}

/**
 * Get grade by ID
 */
export async function getGradeById(gradeId: string): Promise<Grade | null> {
  try {
    const gradesSnapshot = await getDocs(collection(db, 'grades'));

    for (const gradeDoc of gradesSnapshot.docs) {
      if (gradeDoc.id === gradeId) {
        return {
          id: gradeDoc.id,
          ...gradeDoc.data()
        } as Grade;
      }
    }

    return null;
  } catch (error) {
    console.error('❌ Error getting grade by ID:', error);
    throw error;
  }
}

/**
 * Create a single grade
 */
export async function createGrade(name: string, level: number): Promise<Grade> {
  const now = new Date().toISOString();

  const gradeData = {
    name,
    level,
    isActive: true,
    createdAt: now,
    updatedAt: now
  };

  try {
    const docRef = await addDoc(collection(db, 'grades'), gradeData);

    return {
      id: docRef.id,
      ...gradeData
    };
  } catch (error) {
    console.error('❌ Error creating grade:', error);
    throw error;
  }
}

/**
 * Update grade
 */
export async function updateGrade(gradeId: string, updates: Partial<Omit<Grade, 'id' | 'createdAt'>>): Promise<void> {
  try {
    const gradeRef = doc(db, 'grades', gradeId);
    await updateDoc(gradeRef, {
      ...updates,
      updatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Error updating grade:', error);
    throw error;
  }
}

/**
 * Delete grade
 */
export async function deleteGrade(gradeId: string): Promise<void> {
  try {
    await deleteDoc(doc(db, 'grades', gradeId));
  } catch (error) {
    console.error('❌ Error deleting grade:', error);
    throw error;
  }
}

/**
 * Check if grade has sections (for deletion validation)
 */
export async function gradeHasSections(gradeId: string): Promise<boolean> {
  try {
    const sectionsSnapshot = await getDocs(collection(db, 'sections'));

    for (const sectionDoc of sectionsSnapshot.docs) {
      const sectionData = sectionDoc.data();
      if (sectionData.gradeId === gradeId) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('❌ Error checking if grade has sections:', error);
    return true; // Err on the side of caution
  }
}

/**
 * Check if grade has subjects (for deletion validation)
 */
export async function gradeHasSubjects(gradeId: string): Promise<boolean> {
  try {
    const subjectsSnapshot = await getDocs(collection(db, 'subjects'));

    for (const subjectDoc of subjectsSnapshot.docs) {
      const subjectData = subjectDoc.data();
      if (subjectData.gradeId === gradeId) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('❌ Error checking if grade has subjects:', error);
    return true; // Err on the side of caution
  }
}

/**
 * Check if grade has students (for deletion validation)
 */
export async function gradeHasStudents(gradeId: string): Promise<boolean> {
  try {
    const studentsSnapshot = await getDocs(collection(db, 'students'));

    for (const studentDoc of studentsSnapshot.docs) {
      const studentData = studentDoc.data();
      if (studentData.gradeId === gradeId) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('❌ Error checking if grade has students:', error);
    return true; // Err on the side of caution
  }
}
