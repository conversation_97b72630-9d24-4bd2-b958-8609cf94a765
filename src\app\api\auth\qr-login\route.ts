import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/firebase";
import { collection, getDocs } from "firebase/firestore";
import { admin } from "@/lib/firebaseAdmin";

export async function POST(req: NextRequest) {
  try {
    const { hash } = await req.json();
    if (!hash || typeof hash !== "string") {
      return NextResponse.json({ message: "Hash is required." }, { status: 400 });
    }
    // Search for student by hash
    const studentsSnapshot = await getDocs(collection(db, "students"));
    let found: any = null;
    studentsSnapshot.forEach(docSnap => {
      const data = docSnap.data();
      if (data.hash === hash) {
        found = data;
      }
    });
    if (!found) {
      return NextResponse.json({ message: "Student not found." }, { status: 404 });
    }
    // Create a custom token for the student using their LRN
    const customToken = await admin.auth().createCustomToken(found.lrn);
    return NextResponse.json({ student: found, token: customToken }, { status: 200 });
  } catch (err) {
    console.error("QR LOGIN ERROR:", err);
    return NextResponse.json({ message: "Invalid request." }, { status: 400 });
  }
} 