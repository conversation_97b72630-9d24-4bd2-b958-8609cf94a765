import { NextRequest, NextResponse } from 'next/server';
import { gradeService } from '@/lib/services/gradeService';
import { seedCompleteAcademicStructure, resetAndSeedAcademicStructure } from '@/lib/seeders/completeSchemaSeeder';
import { seedGrades, createGrade, updateGrade, deleteGrade } from '@/lib/seeders/gradeSeeder';

// GET /api/admin/grades - Get all grades with sections
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const withSections = searchParams.get('withSections') === 'true';
    const activeOnly = searchParams.get('activeOnly') === 'true';

    let grades;
    
    if (withSections) {
      grades = await gradeService.getGradesWithSections();
    } else if (activeOnly) {
      grades = await gradeService.getActiveGrades();
    } else {
      grades = await gradeService.getAllGrades();
    }

    return NextResponse.json({
      success: true,
      data: grades,
      message: 'Grades retrieved successfully'
    });

  } catch (error: any) {
    console.error('Error fetching grades:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch grades',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// POST /api/admin/grades - Create new grade or seed data
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, ...gradeData } = body;

    // Handle seeding action
    if (action === 'seed') {
      const result = await seedCompleteAcademicStructure();
      return NextResponse.json({
        success: true,
        message: 'Complete academic structure seeded successfully',
        data: result
      });
    }

    // Handle reset action
    if (action === 'reset') {
      const result = await resetAndSeedAcademicStructure();
      return NextResponse.json({
        success: true,
        message: 'Academic structure reset and reseeded successfully',
        data: result
      });
    }

    // Handle create action
    if (action === 'create') {
      const grade = await createGrade(gradeData.name, gradeData.level);
      return NextResponse.json({
        success: true,
        message: 'Grade created successfully',
        data: grade
      });
    }

    // Create new grade
    const newGrade = await gradeService.createGrade(gradeData);

    return NextResponse.json({
      success: true,
      data: newGrade,
      message: 'Grade created successfully'
    }, { status: 201 });

  } catch (error: any) {
    console.error('Error creating grade:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create grade',
        details: error.message
      },
      { status: 500 }
    );
  }
}
