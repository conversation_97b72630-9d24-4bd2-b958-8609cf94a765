const { initializeApp } = require('firebase/app');
const { getFirestore, collection, doc, setDoc, getDocs, query, where } = require('firebase/firestore');
const { getAuth, createUserWithEmailAndPassword } = require('firebase/auth');

// Firebase configuration (you may need to adjust this based on your config)
const firebaseConfig = {
  // Add your Firebase config here
  // You can get this from your Firebase console
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

// Teacher seed data
const teacherSeeds = [
  {
    id: 'teacher1',
    email: '<EMAIL>',
    password: 'Teacher123!',
    firstName: 'Maria',
    lastName: 'Santos',
    middleName: 'Cruz',
    employeeId: 'EMP001',
    department: 'Mathematics',
    position: 'Senior Teacher',
    status: 'active',
    subjects: ['Mathematics', 'Algebra', 'Geometry'],
    gradeLevel: ['Grade 5', 'Grade 6'],
    phoneNumber: '+63 ************',
    address: 'Pinagbarilan, Baguio City',
    dateHired: '2020-06-15',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'teacher2',
    email: '<EMAIL>',
    password: 'Teacher123!',
    firstName: 'Juan',
    lastName: 'Dela Cruz',
    middleName: 'Reyes',
    employeeId: 'EMP002',
    department: 'Science',
    position: 'Teacher II',
    status: 'active',
    subjects: ['Science', 'Biology', 'Chemistry'],
    gradeLevel: ['Grade 4', 'Grade 5'],
    phoneNumber: '+63 ************',
    address: 'Pinagbarilan, Baguio City',
    dateHired: '2021-08-20',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'teacher3',
    email: '<EMAIL>',
    password: 'Teacher123!',
    firstName: 'Ana',
    lastName: 'Garcia',
    middleName: 'Lopez',
    employeeId: 'EMP003',
    department: 'English',
    position: 'Teacher I',
    status: 'active',
    subjects: ['English', 'Literature', 'Grammar'],
    gradeLevel: ['Grade 1', 'Grade 2', 'Grade 3'],
    phoneNumber: '+63 ************',
    address: 'Pinagbarilan, Baguio City',
    dateHired: '2022-01-10',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Admin seed data
const adminSeeds = [
  {
    id: 'admin1',
    email: '<EMAIL>',
    password: 'Admin123!',
    firstName: 'Roberto',
    lastName: 'Mendoza',
    middleName: 'Torres',
    employeeId: 'ADM001',
    position: 'School Principal',
    department: 'Administration',
    status: 'active',
    role: 'principal',
    permissions: ['user_management', 'exam_approval', 'system_settings', 'reports'],
    phoneNumber: '+63 ************',
    address: 'Pinagbarilan, Baguio City',
    dateHired: '2018-03-01',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'admin2',
    email: '<EMAIL>',
    password: 'Principal123!',
    firstName: 'Carmen',
    lastName: 'Villanueva',
    middleName: 'Ramos',
    employeeId: 'ADM002',
    position: 'Assistant Principal',
    department: 'Administration',
    status: 'active',
    role: 'assistant_principal',
    permissions: ['user_management', 'exam_approval', 'reports'],
    phoneNumber: '+63 ************',
    address: 'Pinagbarilan, Baguio City',
    dateHired: '2019-07-15',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Student seed data (for testing)
const studentSeeds = [
  {
    id: 'student1',
    lrn: '123456789012',
    firstName: 'Jose',
    lastName: 'Rizal',
    middleName: 'Protacio',
    gradeLevel: 'Grade 5',
    section: 'Mabini',
    status: 'active',
    guardianName: 'Francisco Rizal',
    guardianContact: '+63 ************',
    address: 'Pinagbarilan, Baguio City',
    birthDate: '2014-06-19',
    enrollmentDate: '2023-08-15',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'student2',
    lrn: '123456789013',
    firstName: 'Andres',
    lastName: 'Bonifacio',
    middleName: 'De Castro',
    gradeLevel: 'Grade 6',
    section: 'Rizal',
    status: 'active',
    guardianName: 'Santiago Bonifacio',
    guardianContact: '+63 ************',
    address: 'Pinagbarilan, Baguio City',
    birthDate: '2013-11-30',
    enrollmentDate: '2023-08-15',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

async function createAuthUser(email, password) {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    console.log(`✅ Created auth user: ${email}`);
    return userCredential.user.uid;
  } catch (error) {
    if (error.code === 'auth/email-already-in-use') {
      console.log(`⚠️  Auth user already exists: ${email}`);
      return null;
    }
    throw error;
  }
}

async function seedTeachers() {
  console.log('🌱 Seeding teachers...');
  
  for (const teacher of teacherSeeds) {
    try {
      // Check if teacher already exists
      const teacherQuery = query(collection(db, 'teachers'), where('email', '==', teacher.email));
      const existingTeacher = await getDocs(teacherQuery);
      
      if (!existingTeacher.empty) {
        console.log(`⚠️  Teacher already exists: ${teacher.email}`);
        continue;
      }

      // Create auth user
      const authUid = await createAuthUser(teacher.email, teacher.password);
      
      // Create teacher document
      const { password, ...teacherData } = teacher;
      if (authUid) {
        teacherData.authUid = authUid;
      }
      
      await setDoc(doc(db, 'teachers', teacher.id), teacherData);
      console.log(`✅ Created teacher: ${teacher.firstName} ${teacher.lastName} (${teacher.email})`);
    } catch (error) {
      console.error(`❌ Error creating teacher ${teacher.email}:`, error);
    }
  }
}

async function seedAdmins() {
  console.log('🌱 Seeding admins...');
  
  for (const admin of adminSeeds) {
    try {
      // Check if admin already exists
      const adminQuery = query(collection(db, 'admins'), where('email', '==', admin.email));
      const existingAdmin = await getDocs(adminQuery);
      
      if (!existingAdmin.empty) {
        console.log(`⚠️  Admin already exists: ${admin.email}`);
        continue;
      }

      // Create auth user
      const authUid = await createAuthUser(admin.email, admin.password);
      
      // Create admin document
      const { password, ...adminData } = admin;
      if (authUid) {
        adminData.authUid = authUid;
      }
      
      await setDoc(doc(db, 'admins', admin.id), adminData);
      console.log(`✅ Created admin: ${admin.firstName} ${admin.lastName} (${admin.email})`);
    } catch (error) {
      console.error(`❌ Error creating admin ${admin.email}:`, error);
    }
  }
}

async function seedStudents() {
  console.log('🌱 Seeding students...');
  
  for (const student of studentSeeds) {
    try {
      // Check if student already exists
      const studentQuery = query(collection(db, 'students'), where('lrn', '==', student.lrn));
      const existingStudent = await getDocs(studentQuery);
      
      if (!existingStudent.empty) {
        console.log(`⚠️  Student already exists: ${student.lrn}`);
        continue;
      }

      await setDoc(doc(db, 'students', student.id), student);
      console.log(`✅ Created student: ${student.firstName} ${student.lastName} (${student.lrn})`);
    } catch (error) {
      console.error(`❌ Error creating student ${student.lrn}:`, error);
    }
  }
}

async function runSeeders() {
  try {
    console.log('🚀 Starting user seeding process...');
    
    await seedTeachers();
    await seedAdmins();
    await seedStudents();
    
    console.log('✅ User seeding completed successfully!');
    console.log('\n📋 Test Accounts Created:');
    console.log('\n👨‍🏫 TEACHERS:');
    teacherSeeds.forEach(teacher => {
      console.log(`   • ${teacher.email} / ${teacher.password}`);
    });
    console.log('\n👨‍💼 ADMINS:');
    adminSeeds.forEach(admin => {
      console.log(`   • ${admin.email} / ${admin.password}`);
    });
    
  } catch (error) {
    console.error('❌ Error during seeding:', error);
  }
}

// Export for use in other scripts
module.exports = {
  runSeeders,
  seedTeachers,
  seedAdmins,
  seedStudents,
  teacherSeeds,
  adminSeeds,
  studentSeeds
};

// Run if called directly
if (require.main === module) {
  runSeeders();
}
