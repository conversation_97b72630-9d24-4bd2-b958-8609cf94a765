import { NextRequest, NextResponse } from 'next/server';
import { 
  seedCompleteAcademicStructure, 
  resetAndSeedAcademicStructure,
  seedDevelopmentData,
  clearAllAcademicData
} from '@/lib/seeders/completeSchemaSeeder';

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();

    switch (action) {
      case 'seed':
        console.log('🌱 Starting complete academic structure seeding...');
        const seedResult = await seedCompleteAcademicStructure();
        return NextResponse.json({
          success: true,
          message: 'Complete academic structure seeded successfully',
          data: seedResult
        });

      case 'reset':
        console.log('🔄 Resetting and reseeding academic structure...');
        const resetResult = await resetAndSeedAcademicStructure();
        return NextResponse.json({
          success: true,
          message: 'Academic structure reset and reseeded successfully',
          data: resetResult
        });

      case 'development':
        console.log('🛠️ Seeding development data...');
        const devResult = await seedDevelopmentData();
        return NextResponse.json({
          success: true,
          message: 'Development data seeded successfully',
          data: devResult
        });

      case 'clear':
        console.log('🧹 Clearing all academic data...');
        await clearAllAcademicData();
        return NextResponse.json({
          success: true,
          message: 'All academic data cleared successfully'
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use: seed, reset, development, or clear'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Error in seed-complete API:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Complete Academic Structure Seeder API',
    actions: {
      seed: 'Seed complete academic structure (grades, sections, subjects, students, teachers)',
      reset: 'Clear all data and reseed complete structure',
      development: 'Seed development data with sample content',
      clear: 'Clear all academic data from database'
    },
    usage: {
      method: 'POST',
      body: '{ "action": "seed" | "reset" | "development" | "clear" }'
    }
  });
}
