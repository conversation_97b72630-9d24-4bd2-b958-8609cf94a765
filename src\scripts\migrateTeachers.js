const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  const serviceAccount = require('../serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

async function migrateTeachers() {
  try {
    console.log('🔄 Starting Teacher Data Migration...\n');
    
    // Get all teachers
    const teachersSnapshot = await db.collection('teachers').get();
    console.log(`📊 Found ${teachersSnapshot.size} teachers to migrate\n`);
    
    const batch = db.batch();
    let migratedCount = 0;
    
    for (const doc of teachersSnapshot.docs) {
      const teacherData = doc.data();
      const teacherId = doc.id;
      
      console.log(`🔄 Migrating teacher: ${teacherId}`);
      console.log(`   Name: ${teacherData.firstName} ${teacherData.lastName}`);
      
      // Create standardized teacher data
      const migratedData = {
        // Basic Information (required)
        firstName: teacherData.firstName || '',
        middleName: teacherData.middleName || '',
        lastName: teacherData.lastName || '',
        email: teacherData.email || '',
        
        // New standardized fields
        gradeIds: [], // Will be populated later when grades are assigned
        sectionIds: [], // Will be populated later when sections are assigned
        subjects: teacherData.subjects || [], // Keep existing subjects if any
        examIds: [], // Will be populated when exams are assigned
        status: teacherData.status || 'active',
        
        // Timestamps
        createdAt: teacherData.createdAt || admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        
        // Keep legacy fields for reference (can be removed later)
        _legacy: {
          gradeLevel: teacherData.gradeLevel || null,
          department: teacherData.department || null,
          position: teacherData.position || null,
          employeeId: teacherData.employeeId || null,
          phoneNumber: teacherData.phoneNumber || null,
          dateHired: teacherData.dateHired || null,
          address: teacherData.address || null,
          extension: teacherData.extension || null,
          uid: teacherData.uid || null
        }
      };
      
      // Update the document
      batch.update(doc.ref, migratedData);
      migratedCount++;
      
      console.log(`   ✅ Prepared migration for ${teacherData.firstName} ${teacherData.lastName}`);
    }
    
    // Commit all updates
    await batch.commit();
    
    console.log(`\n🎉 Successfully migrated ${migratedCount} teachers!`);
    console.log('📋 Migration Summary:');
    console.log('   ✅ Standardized firstName, middleName, lastName, email');
    console.log('   ✅ Added gradeIds, sectionIds, subjects, examIds arrays');
    console.log('   ✅ Set status to active');
    console.log('   ✅ Added/updated timestamps');
    console.log('   ✅ Preserved legacy data in _legacy field');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

migrateTeachers().then(() => {
  console.log('\n✅ Migration script completed!');
  process.exit(0);
}).catch(error => {
  console.error('❌ Migration script failed:', error);
  process.exit(1);
});
