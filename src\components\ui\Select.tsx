"use client";
import React from 'react';
import { cn } from '@/lib/utils';

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'primary';
}

export default function Select({
  children,
  className,
  variant = 'default',
  ...props
}: SelectProps) {
  const baseClasses = "w-full py-2 pl-4 pr-10 border focus:outline-none focus:ring-2 appearance-none bg-white text-gray-900";

  const variantClasses = {
    default: "border-gray-300 focus:ring-[#84a98c] focus:border-[#84a98c]",
    primary: "border-[#84a98c] focus:ring-[#84a98c] focus:border-[#84a98c]"
  };

  return (
    <div className="relative">
      <select
        className={cn(
          baseClasses,
          variantClasses[variant],
          className
        )}
        {...props}
      >
        {children}
      </select>
      {/* Custom dropdown arrow */}
      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
        <svg
          className="w-4 h-4 text-gray-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </div>
    </div>
  );
}
