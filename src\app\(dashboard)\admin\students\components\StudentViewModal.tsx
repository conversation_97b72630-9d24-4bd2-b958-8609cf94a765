"use client";
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/Button';
import { CardBadge } from '@/components/cards';
import { useGradeAndSectionNames } from '@/hooks/useGrades';

interface StudentViewModalProps {
  student: any;
  isOpen: boolean;
  onClose: () => void;
}

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
};

const modalVariants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: -50
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.3
    }
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    y: -50,
    transition: {
      duration: 0.2
    }
  }
};

export default function StudentViewModal({ student, isOpen, onClose }: StudentViewModalProps) {
  const { getGradeName, getSectionName } = useGradeAndSectionNames();

  if (!student) return null;

  // Get student display name - handle both old and new data structures
  const getStudentDisplayName = () => {
    if (student.fullName && Array.isArray(student.fullName)) {
      // New structure: fullName is an array [firstName, middleName, lastName]
      const [firstName, middleName, lastName] = student.fullName;
      return {
        firstName: firstName || '',
        middleName: middleName || '',
        lastName: lastName || '',
        extension: student.extension || ''
      };
    } else if (student.firstName && student.lastName) {
      // Old structure: individual name fields
      return {
        firstName: student.firstName,
        middleName: student.middleName || '',
        lastName: student.lastName,
        extension: student.extension || ''
      };
    }
    return {
      firstName: 'Unknown',
      middleName: '',
      lastName: 'Student',
      extension: ''
    };
  };

  const nameData = getStudentDisplayName();
  const lrnDisplay = student.lrn || student.studentId || 'No LRN';

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed left-[50%] top-[50%] z-50 w-full max-w-2xl max-h-[90vh] translate-x-[-50%] translate-y-[-50%] bg-white border-2 border-[#84a98c] shadow-lg p-6 overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="mb-6">
              <h2 className="text-lg font-semibold leading-none tracking-tight text-[#84a98c] mb-2">Student Details</h2>
              <p className="text-sm text-gray-600">
                View complete student information and status.
              </p>
            </div>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">Full Name</label>
              <p className="text-gray-700 bg-gray-50 p-3 border rounded">
                {nameData.lastName}, {nameData.firstName}
                {nameData.middleName && ` ${nameData.middleName}`}
                {nameData.extension && ` ${nameData.extension}`}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">LRN</label>
              <p className="text-gray-700 bg-gray-50 p-3 border rounded font-mono">{lrnDisplay}</p>
            </div>
          </div>

          {/* Academic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">Grade Level</label>
              <div className="bg-gray-50 p-3 border rounded">
                {student.gradeId ? (
                  <CardBadge variant="info" size="md">
                    {getGradeName(student.gradeId)}
                  </CardBadge>
                ) : (
                  <span className="text-gray-400">Not assigned</span>
                )}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">Section</label>
              <div className="bg-gray-50 p-3 border rounded">
                {student.sectionId ? (
                  <CardBadge variant="success" size="md">
                    {getSectionName(student.sectionId)}
                  </CardBadge>
                ) : (
                  <span className="text-gray-400">Not assigned</span>
                )}
              </div>
            </div>
          </div>

          {/* Status and QR Code */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">Status</label>
              <div className="bg-gray-50 p-3 border rounded">
                <CardBadge 
                  variant={(student.status || 'active') === 'active' ? 'success' : 'error'}
                  size="md"
                >
                  {(student.status || 'active').charAt(0).toUpperCase() + (student.status || 'active').slice(1)}
                </CardBadge>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">QR Code</label>
              <div className="bg-gray-50 p-3 border rounded">
                {student.qrUrl ? (
                  <CardBadge variant="success" size="md">
                    Generated
                  </CardBadge>
                ) : (
                  <CardBadge variant="warning" size="md">
                    Not Generated
                  </CardBadge>
                )}
              </div>
            </div>
          </div>



          {/* Timestamps */}
          {(student.createdAt || student.updatedAt) && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {student.createdAt && (
                <div>
                  <label className="block text-sm font-medium text-[#84a98c] mb-1">Created</label>
                  <p className="text-gray-700 bg-gray-50 p-3 border rounded text-sm">
                    {student.createdAt?.seconds
                      ? new Date(student.createdAt.seconds * 1000).toLocaleString()
                      : student.createdAt
                        ? new Date(student.createdAt).toLocaleString()
                        : 'N/A'
                    }
                  </p>
                </div>
              )}
              {student.updatedAt && (
                <div>
                  <label className="block text-sm font-medium text-[#84a98c] mb-1">Last Updated</label>
                  <p className="text-gray-700 bg-gray-50 p-3 border rounded text-sm">
                    {student.updatedAt?.seconds
                      ? new Date(student.updatedAt.seconds * 1000).toLocaleString()
                      : student.updatedAt
                        ? new Date(student.updatedAt).toLocaleString()
                        : 'N/A'
                    }
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

            <div className="flex justify-end mt-6">
              <Button
                onClick={onClose}
                variant="outlined"
                theme="neutral"
              >
                Close
              </Button>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
