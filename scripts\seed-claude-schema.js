#!/usr/bin/env node

/**
 * <PERSON>
 * 
 * This script seeds the database with <PERSON>'s recommended schema structure.
 * Run this after setting up your Firebase project to populate initial data.
 * 
 * Usage:
 *   node scripts/seed-claude-schema.js
 *   node scripts/seed-claude-schema.js --dev (clears existing data first)
 *   node scripts/seed-claude-schema.js --check (checks current status)
 *   node scripts/seed-claude-schema.js --validate (validates schema)
 */

const path = require('path');

// Add the src directory to the module path
require('module').globalPaths.push(path.join(__dirname, '../src'));

async function main() {
  const args = process.argv.slice(2);
  const isDev = args.includes('--dev');
  const isCheck = args.includes('--check');
  const isValidate = args.includes('--validate');

  try {
    // Dynamic import to handle ES modules
    const { 
      seedClaudeSchema, 
      seedClaudeSchemaForDevelopment,
      checkClaudeSchemaStatus,
      validateClaudeSchema
    } = await import('../src/lib/seeders/claudeSchemaSeeder.ts');

    if (isCheck) {
      console.log('🔍 Checking Claude Schema Status...\n');
      const status = await checkClaudeSchemaStatus();
      
      console.log('📊 Current Status:');
      console.log(`  Grades: ${status.gradesExist ? '✅ Exist' : '❌ Missing'}`);
      console.log(`  Subjects: ${status.subjectsExist ? '✅ Exist' : '❌ Missing'}`);
      console.log(`  Ready: ${status.isReady ? '✅ Yes' : '❌ No'}`);
      
      if (!status.isReady) {
        console.log('\n💡 Run the seeder to set up the schema:');
        console.log('   node scripts/seed-claude-schema.js');
      }
      
      return;
    }

    if (isValidate) {
      console.log('🔍 Validating Claude Schema...\n');
      const isValid = await validateClaudeSchema();
      
      if (isValid) {
        console.log('\n🎉 Schema validation passed!');
      } else {
        console.log('\n❌ Schema validation failed!');
        process.exit(1);
      }
      
      return;
    }

    if (isDev) {
      console.log('🔧 Running Development Seeder...\n');
      await seedClaudeSchemaForDevelopment();
    } else {
      console.log('🚀 Running Production Seeder...\n');
      await seedClaudeSchema();
    }

    console.log('\n✅ Seeding completed successfully!');
    console.log('\n🔄 Next steps:');
    console.log('  1. Start your development server');
    console.log('  2. Navigate to Admin > Teachers');
    console.log('  3. Create teachers using the new assignment-based form');
    console.log('  4. Create students and assign them to sections');

  } catch (error) {
    console.error('\n❌ Seeding failed:', error.message);
    console.error('\nFull error:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run the script
main();
