"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import { db } from "@/lib/firebase";
import { collection, addDoc, getDocs, query, where, updateDoc, doc, getDoc } from "firebase/firestore";
import { AcademicCapIcon, ClipboardDocumentListIcon, CheckCircleIcon, XCircleIcon, ArrowLeftOnRectangleIcon, PhotoIcon, XMarkIcon } from "@heroicons/react/24/outline";

import React, { useRef } from "react";

const navy = "#1A2340";
const lightCream = "#FAFAF6";

const navItems = [
  { key: "overview", label: "Dashboard", icon: ClipboardDocumentListIcon },
  { key: "create", label: "Create Exam", icon: AcademicCapIcon },
  { key: "createQuiz", label: "Create Quiz", icon: AcademicCapIcon },
  { key: "exams", label: "My Exams", icon: CheckCircleIcon },
  { key: "quizzes", label: "My Quizzes", icon: CheckCircleIcon },
  { key: "grading", label: "Grading", icon: XCircleIcon },
  { key: "performance", label: "Performance", icon: AcademicCapIcon },
];

const TeacherNavBar = ({ active, setActive, handleLogout }: { active: string, setActive: (key: string) => void, handleLogout: () => void }) => (
  <nav className="fixed bottom-0 left-0 w-full border-t border-[#1A2340] bg-[#1A2340] flex justify-around items-center h-16 z-50" style={{ borderRadius: 0 }} aria-label="Teacher Navigation Bar">
    {navItems.map(({ key, label, icon: Icon }) => (
      <button
        key={key}
        className={`flex flex-col items-center justify-center flex-1 h-full text-xs font-semibold focus:outline-none ${active === key ? 'text-[#FAFAF6]' : 'text-[#FAFAF6]/70'}`}
        onClick={() => setActive(key)}
        aria-label={label}
        tabIndex={0}
        style={{ borderRadius: 0 }}
      >
        <Icon className="w-6 h-6 mb-1" aria-hidden="true" />
        {label}
      </button>
    ))}
    <button
      className="flex flex-col items-center justify-center flex-1 h-full text-xs font-semibold text-[#FAFAF6]/70 focus:outline-none"
      onClick={handleLogout}
      aria-label="Logout"
      tabIndex={0}
      style={{ borderRadius: 0 }}
    >
      <ArrowLeftOnRectangleIcon className="w-6 h-6 mb-1" aria-hidden="true" />
      Logout
    </button>
  </nav>
);

const TeacherDashboardPage = () => {
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [exams, setExams] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [gradeSections, setGradeSections] = useState<any[]>([]);
  const [selectedSectionIds, setSelectedSectionIds] = useState<string[]>([]);
  const [examForm, setExamForm] = useState({
    title: "",
    type: "LOTS",
    subjectId: "",
    questions: [{ question: "", answer: "", type: "Multiple Choice", options: ["", "", "", ""], imageUrl: "" }],
  });
  const [examError, setExamError] = useState("");
  const [examSuccess, setExamSuccess] = useState("");
  const [grading, setGrading] = useState<any[]>([]);
  const [studentResults, setStudentResults] = useState<any[]>([]);
  const [activeSection, setActiveSection] = useState("overview");
  const [teacherSectionIds, setTeacherSectionIds] = useState<string[]>([]);
  const [showResultModal, setShowResultModal] = useState(false);
  const [selectedResult, setSelectedResult] = useState<any>(null);
  const [quizForm, setQuizForm] = useState({
    title: "",
    type: "LOTS",
    subjectId: "",
    questions: [{ question: "", answer: "", type: "Multiple Choice", options: ["", "", "", ""], imageUrl: "" }],
  });
  const [quizError, setQuizError] = useState("");
  const [quizSuccess, setQuizSuccess] = useState("");
  const [quizzes, setQuizzes] = useState<any[]>([]);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  const examFileInputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const quizFileInputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Auth check
  useEffect(() => {
    const auth = getAuth();
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      if (!firebaseUser) {
        router.replace("/login");
      } else {
        setUser(firebaseUser);
      }
      setLoading(false);
    });
    return () => unsubscribe();
  }, [router]);

  // Fetch exams created by this teacher
  useEffect(() => {
    if (!user) return;
    const fetchExams = async () => {
      const q = query(collection(db, "exams"), where("createdBy", "==", user.uid));
      const snap = await getDocs(q);
      const list: any[] = [];
      snap.forEach((doc) => list.push({ id: doc.id, ...doc.data() }));
      setExams(list);
    };
    fetchExams();
  }, [user]);

  // Fetch subjects and gradeSections for teacher
  useEffect(() => {
    const fetchSubjects = async () => {
      const snap = await getDocs(collection(db, "subjects"));
      const list: any[] = [];
      snap.forEach((doc) => list.push({ id: doc.id, ...doc.data() }));
      setSubjects(list);
    };
    const fetchGradeSections = async () => {
      const snap = await getDocs(collection(db, "gradeSections"));
      const list: any[] = [];
      snap.forEach((doc) => list.push({ id: doc.id, ...doc.data() }));
      setGradeSections(list);
    };
    fetchSubjects();
    fetchGradeSections();
  }, []);

  // Fetch manual grading needed
  useEffect(() => {
    if (!user) return;
    const fetchGrading = async () => {
      const q = query(collection(db, "essayResponses"), where("teacherId", "==", user.uid), where("graded", "==", false));
      const snap = await getDocs(q);
      const list: any[] = [];
      snap.forEach((doc) => list.push({ id: doc.id, ...doc.data() }));
      setGrading(list);
    };
    fetchGrading();
  }, [user]);

  // Fetch student results for this teacher's exams
  useEffect(() => {
    if (!user) return;
    const fetchResults = async () => {
      const q = query(collection(db, "results"), where("teacherId", "==", user.uid));
      const snap = await getDocs(q);
      const list: any[] = [];
      snap.forEach((doc) => list.push({ id: doc.id, ...doc.data() }));
      setStudentResults(list);
    };
    fetchResults();
  }, [user]);

  // Fetch quizzes created by this teacher
  useEffect(() => {
    if (!user) return;
    const fetchQuizzes = async () => {
      const q = query(collection(db, "quizzes"), where("createdBy", "==", user.uid));
      const snap = await getDocs(q);
      const list: any[] = [];
      snap.forEach((doc) => list.push({ id: doc.id, ...doc.data() }));
      setQuizzes(list);
    };
    fetchQuizzes();
  }, [user]);

  // Fetch teacher's assigned sectionIds
  useEffect(() => {
    if (!user) return;
    const fetchTeacher = async () => {
      const teacherDoc = await getDoc(doc(db, "teachers", user.uid));
      if (teacherDoc.exists()) {
        const data = teacherDoc.data();
        setTeacherSectionIds(data.sectionIds || []);
      }
    };
    fetchTeacher();
  }, [user]);

  // Exam form handlers
  const handleExamInput = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setExamForm({ ...examForm, [e.target.id]: e.target.value });
  };
  const handleQuestionChange = (idx: number, field: string, value: string | boolean) => {
    setExamForm((prev) => {
      const questions = [...prev.questions];
      (questions[idx] as any)[field] = value;
      return { ...prev, questions };
    });
  };
  const handleOptionChange = (idx: number, oidx: number, value: string) => {
    setExamForm((prev) => {
      const questions = [...prev.questions];
      questions[idx].options[oidx] = value;
      return { ...prev, questions };
    });
  };
  const handleAddQuestion = () => {
    setExamForm((prev) => {
      const newOptions = ["", "", "", ""];
      return {
        ...prev,
        questions: [
          ...prev.questions,
          { question: "", answer: newOptions[0], type: prev.type === "LOTS" ? "Multiple Choice" : "Essay", options: newOptions, imageUrl: "" },
        ],
      };
    });
  };
  const handleRemoveQuestion = (idx: number) => {
    setExamForm((prev) => ({ ...prev, questions: prev.questions.filter((_, i) => i !== idx) }));
  };
  const handleQuestionTypeChange = (idx: number, value: string) => {
    setExamForm((prev) => {
      const questions = [...prev.questions];
      questions[idx].type = value;
      // Reset options/answer for new type
      if (value === "Multiple Choice") {
        questions[idx].options = ["", "", "", ""];
        questions[idx].answer = "";
      } else if (value === "True or False") {
        questions[idx].options = [];
        questions[idx].answer = "True";
      } else if (value === "Fill in the Blanks") {
        questions[idx].options = [];
        questions[idx].answer = "";
      } else {
        questions[idx].options = [];
        questions[idx].answer = "";
      }
      return { ...prev, questions };
    });
  };
  const handleCreateExam = async (e: React.FormEvent) => {
    e.preventDefault();
    setExamError("");
    setExamSuccess("");
    console.log("[DEBUG] handleCreateExam called", examForm);
    if (examForm.questions.some(q => !q.question.trim() || (q.imageUrl === undefined ? false : q.imageUrl === 'uploading'))) {
      setExamError("Please wait for all images to finish uploading and ensure all questions are filled.");
      console.log("[DEBUG] Blocked by image uploading or missing question");
      return;
    }
    if (isUploadingImage) {
      setExamError("Please wait for all images to finish uploading.");
      console.log("[DEBUG] Blocked by isUploadingImage");
      return;
    }
    if (!examForm.title.trim() || !examForm.subjectId || selectedSectionIds.length === 0 || examForm.questions.some(q => !q.question.trim())) {
      setExamError("Title, subject, at least one section, and all questions are required.");
      console.log("[DEBUG] Blocked by required fields");
      return;
    }
    // Validation: Ensure all Multiple Choice questions have a non-empty answer that matches one of the options
    for (const q of examForm.questions) {
      if (q.type === "Multiple Choice") {
        if (q.answer === '' || !q.options.includes(q.answer)) {
          setExamError("All multiple choice questions must have a correct answer selected.");
          return;
        }
      }
    }
    try {
      console.log("[DEBUG] Submitting to Firestore", {
        title: examForm.title,
        type: examForm.type,
        subjectId: examForm.subjectId,
        visibleToSectionIds: selectedSectionIds,
        questions: examForm.questions,
        createdBy: user.uid,
        status: "pending",
        feedback: "",
        createdAt: new Date().toISOString(),
      });
      await addDoc(collection(db, "exams"), {
        title: examForm.title,
        type: examForm.type,
        subjectId: examForm.subjectId,
        visibleToSectionIds: selectedSectionIds,
        questions: examForm.questions,
        createdBy: user.uid, // always set the creator
        status: "pending",
        feedback: "",
        createdAt: new Date().toISOString(),
      });
      setExamSuccess("Exam created and submitted for approval.");
      setExamForm({ title: "", type: "LOTS", subjectId: "", questions: [{ question: "", answer: "", type: "Multiple Choice", options: ["", "", "", ""], imageUrl: "" }] });
      setSelectedSectionIds([]);
      // Refresh exams
      const q = query(collection(db, "exams"), where("createdBy", "==", user.uid));
      const snap = await getDocs(q);
      const list: any[] = [];
      snap.forEach((doc) => list.push({ id: doc.id, ...doc.data() }));
      setExams(list);
    } catch (err: any) {
      setExamError(err.message || "Failed to create exam.");
      console.log("[DEBUG] Firestore addDoc error", err);
    }
  };

  // Exam edit/resubmit
  const handleEditExam = async (id: string, updatedExam: any) => {
    try {
      await updateDoc(doc(db, "exams", id), updatedExam);
      // Refresh exams
      const q = query(collection(db, "exams"), where("createdBy", "==", user.uid));
      const snap = await getDocs(q);
      const list: any[] = [];
      snap.forEach((doc) => list.push({ id: doc.id, ...doc.data() }));
      setExams(list);
    } catch (err: any) {
      alert("Failed to update exam: " + err.message);
    }
  };

  // Manual grading
  const handleGradeEssay = async (id: string, grade: string) => {
    try {
      await updateDoc(doc(db, "essayResponses", id), { grade, graded: true });
      setGrading((prev) => prev.filter((g) => g.id !== id));
    } catch (err: any) {
      alert("Failed to submit grade: " + err.message);
    }
  };

  // Quiz form handlers (same as exam, but for quizForm)
  const handleQuizInput = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setQuizForm({ ...quizForm, [e.target.id]: e.target.value });
  };
  const handleQuizQuestionChange = (idx: number, field: string, value: string | boolean) => {
    setQuizForm((prev) => {
      const questions = [...prev.questions];
      (questions[idx] as any)[field] = value;
      return { ...prev, questions };
    });
  };
  const handleQuizOptionChange = (idx: number, oidx: number, value: string) => {
    setQuizForm((prev) => {
      const questions = [...prev.questions];
      questions[idx].options[oidx] = value;
      return { ...prev, questions };
    });
  };
  const handleAddQuizQuestion = () => {
    setQuizForm((prev) => {
      const newOptions = ["", "", "", ""];
      return {
        ...prev,
        questions: [
          ...prev.questions,
          { question: "", answer: newOptions[0], type: prev.type === "LOTS" ? "Multiple Choice" : "Essay", options: newOptions, imageUrl: "" },
        ],
      };
    });
  };
  const handleRemoveQuizQuestion = (idx: number) => {
    setQuizForm((prev) => ({ ...prev, questions: prev.questions.filter((_, i) => i !== idx) }));
  };
  const handleQuizQuestionTypeChange = (idx: number, value: string) => {
    setQuizForm((prev) => {
      const questions = [...prev.questions];
      questions[idx].type = value;
      // Reset options/answer for new type
      if (value === "Multiple Choice") {
        questions[idx].options = ["", "", "", ""];
        questions[idx].answer = "";
      } else if (value === "True or False") {
        questions[idx].options = [];
        questions[idx].answer = "True";
      } else if (value === "Fill in the Blanks") {
        questions[idx].options = [];
        questions[idx].answer = "";
      } else {
        questions[idx].options = [];
        questions[idx].answer = "";
      }
      return { ...prev, questions };
    });
  };
  const handleCreateQuiz = async (e: React.FormEvent) => {
    e.preventDefault();
    setQuizError("");
    setQuizSuccess("");
    if (quizForm.questions.some(q => !q.question.trim() || (q.imageUrl === undefined ? false : q.imageUrl === 'uploading'))) {
      setQuizError("Please wait for all images to finish uploading and ensure all questions are filled.");
      return;
    }
    if (isUploadingImage) {
      setQuizError("Please wait for all images to finish uploading.");
      return;
    }
    if (!quizForm.title.trim() || !quizForm.subjectId || selectedSectionIds.length === 0 || quizForm.questions.some(q => !q.question.trim())) {
      setQuizError("Title, subject, at least one section, and all questions are required.");
      return;
    }
    // Validation: Ensure all Multiple Choice questions have a non-empty answer that matches one of the options
    for (const q of quizForm.questions) {
      if (q.type === "Multiple Choice") {
        if (q.answer === '' || !q.options.includes(q.answer)) {
          setQuizError("All multiple choice questions must have a correct answer selected.");
          return;
        }
      }
    }
    try {
      await addDoc(collection(db, "quizzes"), {
        title: quizForm.title,
        type: quizForm.type,
        subjectId: quizForm.subjectId,
        visibleToSectionIds: selectedSectionIds,
        questions: quizForm.questions,
        createdBy: user.uid,
        status: "approved", // Automatically approved
        feedback: "",
        createdAt: new Date().toISOString(),
      });
      setQuizSuccess("Quiz created and is now available to students.");
      setQuizForm({ title: "", type: "LOTS", subjectId: "", questions: [{ question: "", answer: "", type: "Multiple Choice", options: ["", "", "", ""], imageUrl: "" }] });
      setSelectedSectionIds([]);
      // Refresh quizzes
      const q = query(collection(db, "quizzes"), where("createdBy", "==", user.uid));
      const snap = await getDocs(q);
      const list: any[] = [];
      snap.forEach((doc) => list.push({ id: doc.id, ...doc.data() }));
      setQuizzes(list);
    } catch (err: any) {
      setQuizError(err.message || "Failed to create quiz.");
    }
  };

  const handleSectionCheckbox = (sectionId: string) => {
    setSelectedSectionIds(prev =>
      prev.includes(sectionId) ? prev.filter(id => id !== sectionId) : [...prev, sectionId]
    );
  };

  const handleLogout = () => {
    const auth = getAuth();
    auth.signOut();
    window.location.href = "/login";
  };

  // Helper to format ms to mm:ss
  const formatMs = (ms: number) => {
    const sec = Math.floor(ms / 1000);
    const min = Math.floor(sec / 60);
    const remSec = sec % 60;
    return min > 0 ? `${min}m ${remSec}s` : `${remSec}s`;
  };

  // Helper: upload image to imgbb
  const uploadToImgbb = async (file: File) => {
    const apiKey = '2066735c3882c3226885611a36b58c7d';
    const formData = new FormData();
    formData.append('image', file);
    const res = await fetch(`https://api.imgbb.com/1/upload?key=${apiKey}`, {
      method: 'POST',
      body: formData,
    });
    const data = await res.json();
    if (data.success) return data.data.url;
    throw new Error('Image upload failed');
  };

  if (loading) return <div className="flex items-center justify-center min-h-screen text-[#1A2340]">Loading...</div>;

  return (
    <div className="min-h-screen bg-[#FAFAF6] flex flex-col pb-16">
      <header className="w-full border-b border-[#1A2340] bg-white px-8 py-4 flex items-center" style={{ borderRadius: 0 }}>
        <AcademicCapIcon className="w-8 h-8 text-[#1A2340] mr-3" aria-hidden="true" />
        <h1 className="text-2xl font-bold text-[#1A2340]">Teacher Dashboard</h1>
      </header>
      <main className="flex-1 p-8 flex flex-col gap-8">
        {activeSection === "overview" && (
          // Dashboard Overview
          <section className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="flex flex-col items-center p-6 border border-[#1A2340] bg-white shadow-sm" style={{ borderRadius: 0 }}>
              <ClipboardDocumentListIcon className="w-8 h-8 text-[#1A2340] mb-2" aria-hidden="true" />
              <span className="text-lg font-bold text-[#1A2340]">Exams Created</span>
              <span className="text-2xl font-bold text-[#1A2340]">{exams.length}</span>
            </div>
            <div className="flex flex-col items-center p-6 border border-[#1A2340] bg-white shadow-sm" style={{ borderRadius: 0 }}>
              <CheckCircleIcon className="w-8 h-8 text-[#1A2340] mb-2" aria-hidden="true" />
              <span className="text-lg font-bold text-[#1A2340]">Approved</span>
              <span className="text-2xl font-bold text-[#1A2340]">{exams.filter(e => e.status === "approved").length}</span>
            </div>
            <div className="flex flex-col items-center p-6 border border-[#1A2340] bg-white shadow-sm" style={{ borderRadius: 0 }}>
              <XCircleIcon className="w-8 h-8 text-[#1A2340] mb-2" aria-hidden="true" />
              <span className="text-lg font-bold text-[#1A2340]">Rejected</span>
              <span className="text-2xl font-bold text-[#1A2340]">{exams.filter(e => e.status === "rejected").length}</span>
            </div>
            <div className="flex flex-col items-center p-6 border border-[#1A2340] bg-white shadow-sm" style={{ borderRadius: 0 }}>
              <AcademicCapIcon className="w-8 h-8 text-[#1A2340] mb-2" aria-hidden="true" />
              <span className="text-lg font-bold text-[#1A2340]">Manual Grading</span>
              <span className="text-2xl font-bold text-[#1A2340]">{grading.length}</span>
            </div>
          </section>
        )}
        {activeSection === "create" && (
          <section className="w-full max-w-6xl mx-auto bg-white border border-[#1A2340] shadow-sm px-8 py-10 grid grid-cols-1 md:grid-cols-2 gap-8" style={{ borderRadius: 0 }}>
            <div>
              <h2 className="text-xl font-semibold text-[#1A2340] mb-6 flex items-center gap-2"><ClipboardDocumentListIcon className="w-6 h-6" /> Create Exam</h2>
              <form className="flex flex-col gap-4 w-full" onSubmit={handleCreateExam} aria-label="Create Exam">
                <input id="title" type="text" value={examForm.title} onChange={handleExamInput} className="p-3 border border-[#1A2340] bg-white text-[#1A2340] text-base focus:outline-none focus:ring-2 focus:ring-[#1A2340] w-full" required aria-label="Exam Title" tabIndex={0} placeholder="Exam Title" />
                <select id="subjectId" value={examForm.subjectId} onChange={handleExamInput} className="p-3 border border-[#1A2340] bg-white text-[#1A2340] text-base focus:outline-none focus:ring-2 focus:ring-[#1A2340] w-full" required aria-label="Subject" tabIndex={0}>
                  <option value="">Select Subject</option>
                  {subjects.map((subject: any) => (
                    <option key={subject.id} value={subject.id}>{subject.name}</option>
                  ))}
                </select>
                <select id="type" value={examForm.type} onChange={handleExamInput} className="p-3 border border-[#1A2340] bg-white text-[#1A2340] text-base focus:outline-none focus:ring-2 focus:ring-[#1A2340] w-full" aria-label="Exam Type" tabIndex={0}>
                  <option value="LOTS">LOTS (Lower Order Thinking Skills)</option>
                  <option value="HOTS">HOTS (Higher Order Thinking Skills)</option>
                </select>
                {/* Section checkboxes */}
                <div className="flex flex-wrap gap-2 mb-2">
                  {gradeSections.filter(gs => teacherSectionIds.includes(gs.id)).map(gs => (
                    <label key={gs.id} className="flex items-center gap-2 text-[#1A2340]">
                      <input
                        type="checkbox"
                        checked={selectedSectionIds.includes(gs.id)}
                        onChange={() => handleSectionCheckbox(gs.id)}
                        aria-label={`Assign ${gs.gradeLevel} ${gs.section}`}
                        tabIndex={0}
                      />
                      {gs.gradeLevel} {gs.section}
                    </label>
                  ))}
                </div>
                {/* Dynamic question builder */}
                {examForm.questions.map((q, idx) => (
                  <div key={idx} className="flex flex-col gap-2 border-t border-[#1A2340] pt-4 mt-4">
                    <div className="flex gap-2 items-center">
                      <input type="text" value={q.question} onChange={e => handleQuestionChange(idx, "question", e.target.value)} className="flex-1 p-3 border border-[#1A2340] bg-white text-[#1A2340] text-base focus:outline-none focus:ring-2 focus:ring-[#1A2340]" required aria-label={`Question ${idx + 1}`} tabIndex={0} placeholder={`Question ${idx + 1}`} />
                      <select value={q.type} onChange={e => handleQuestionTypeChange(idx, e.target.value)} className="p-3 border border-[#1A2340] bg-white text-[#1A2340] text-base" aria-label="Question Type" tabIndex={0}>
                        {examForm.type === "LOTS" ? (
                          <>
                            <option value="Multiple Choice">Multiple Choice</option>
                            <option value="True or False">True or False</option>
                            <option value="Fill in the Blanks">Fill in the Blanks</option>
                          </>
                        ) : (
                          <>
                            <option value="Essay">Essay Question</option>
                            <option value="Problem-Solving">Problem-Solving Question</option>
                            <option value="Open-ended">Open-ended Question</option>
                          </>
                        )}
                      </select>
                      <button type="button" className="text-[#1A2340] font-bold px-2" aria-label="Remove Question" tabIndex={0} onClick={() => handleRemoveQuestion(idx)}>×</button>
                    </div>
                    {/* Image upload */}
                    <div className="flex items-center gap-2">
                    
                      <button
                        type="button"
                        className="flex items-center gap-1 px-3 py-1 border border-[#1A2340] bg-white text-[#1A2340] text-sm font-bold"
                        style={{ borderRadius: 0 }}
                        onClick={() => examFileInputRefs.current[idx]?.click()}
                      >
                        <PhotoIcon className="w-4 h-4" aria-hidden="true" />
                      
                      </button>
                      <input
                        type="file"
                        accept="image/*"
                        ref={el => { examFileInputRefs.current[idx] = el; }}
                        style={{ display: "none" }}
                        onChange={async e => {
                          if (e.target.files && e.target.files[0]) {
                            setIsUploadingImage(true);
                            try {
                              const url = await uploadToImgbb(e.target.files[0]);
                              handleQuestionChange(idx, "imageUrl", url);
                            } catch (err) {
                              alert("Image upload failed");
                            }
                            setIsUploadingImage(false);
                          }
                        }}
                      />
              
                      {q.imageUrl && (
                        <button
                          type="button"
                          className="flex items-center gap-1 px-3 py-1 border border-[#1A2340] bg-white text-[#1A2340] text-xs font-bold ml-2"
                          style={{ borderRadius: 0 }}
                          onClick={() => handleQuestionChange(idx, "imageUrl", "")}
                        >
                          <XMarkIcon className="w-4 h-4" aria-hidden="true" />
                          Remove
                        </button>
                      )}
                    </div>
                    {/* Dynamic fields based on question type */}
                    {q.type === "Multiple Choice" && (
                      <div className="flex flex-col gap-2">
                        {q.options.map((opt: string, oidx: number) => (
                          <div key={oidx} className="flex gap-2 items-center">
                            <input type="text" value={opt} onChange={e => handleOptionChange(idx, oidx, e.target.value)} className="flex-1 p-2 border border-[#1A2340] bg-white text-[#1A2340]" placeholder={`Option ${oidx + 1}`} aria-label={`Option ${oidx + 1}`} tabIndex={0} />
                          </div>
                        ))}
                        <div className="flex gap-2 items-center">
                          <span className="text-[#1A2340]">Correct Answer:</span>
                          <select
                            value={q.answer}
                            onChange={e => handleQuestionChange(idx, "answer", e.target.value)}
                            className="p-2 border border-[#1A2340] bg-white text-[#1A2340]"
                            aria-label="Correct Answer"
                            tabIndex={0}
                            disabled={q.options.some((opt: string) => !opt.trim())}
                          >
                            <option value="" disabled>Select correct answer</option>
                            {q.options.map((opt: string, oidx: number) => (
                              <option key={oidx} value={opt}>{opt || `Option ${oidx + 1}`}</option>
                            ))}
                          </select>
                          {q.options.some((opt: string) => !opt.trim()) && (
                            <span className="text-xs text-red-500 ml-2">Fill all choices first</span>
                          )}
                        </div>
                      </div>
                    )}
                    {q.type === "True or False" && (
                      <div className="flex gap-2 items-center">
                        <span className="text-[#1A2340]">Correct Answer:</span>
                        <select value={q.answer} onChange={e => handleQuestionChange(idx, "answer", e.target.value)} className="p-2 border border-[#1A2340] bg-white text-[#1A2340]" aria-label="Correct Answer" tabIndex={0}>
                          <option value="True">True</option>
                          <option value="False">False</option>
                        </select>
                      </div>
                    )}
                    {q.type === "Fill in the Blanks" && (
                      <input type="text" value={q.answer} onChange={e => handleQuestionChange(idx, "answer", e.target.value)} className="p-2 border border-[#1A2340] bg-white text-[#1A2340]" placeholder="Correct Answer" aria-label="Correct Answer" tabIndex={0} />
                    )}
                    {/* HOTS types: Essay, Problem-Solving, Open-ended just need the question */}
                  </div>
                ))}
                <button type="button" onClick={handleAddQuestion} className="px-4 py-2 border border-[#1A2340] bg-white text-[#1A2340] font-bold w-fit" tabIndex={0} aria-label="Add Question">+ Add Question</button>
                {examError && <div className="text-red-600 text-sm" role="alert">{examError}</div>}
                {examSuccess && <div className="text-green-700 text-sm" role="status">{examSuccess}</div>}
                {isUploadingImage && <div className="text-[#1A2340] text-sm">Uploading image, please wait...</div>}
                <button type="submit" className="mt-2 flex items-center justify-center gap-2 px-6 py-3 border border-[#1A2340] bg-[#1A2340] text-white text-base font-bold focus:outline-none focus:ring-2 focus:ring-[#1A2340] w-full" tabIndex={0} aria-label="Create Exam" disabled={isUploadingImage}>
                  <ClipboardDocumentListIcon className="w-5 h-5" aria-hidden="true" />
                  Create Exam
                </button>
              </form>
            </div>
            <div>
              <QuizForm title={examForm.title || "Exam Preview"} questions={examForm.questions} subject={subjects.find(s => s.id === examForm.subjectId)?.name || ""} />
            </div>
          </section>
        )}
        {activeSection === "createQuiz" && (
          <section className="w-full max-w-6xl mx-auto bg-white border border-[#1A2340] shadow-sm px-8 py-10 grid grid-cols-1 md:grid-cols-2 gap-8" style={{ borderRadius: 0 }}>
            <div>
              <h2 className="text-xl font-semibold text-[#1A2340] mb-6 flex items-center gap-2"><ClipboardDocumentListIcon className="w-6 h-6" /> Create Quiz</h2>
              <form className="flex flex-col gap-4 w-full" onSubmit={handleCreateQuiz} aria-label="Create Quiz">
                <input id="title" type="text" value={quizForm.title} onChange={handleQuizInput} className="p-3 border border-[#1A2340] bg-white text-[#1A2340] text-base focus:outline-none focus:ring-2 focus:ring-[#1A2340] w-full" required aria-label="Quiz Title" tabIndex={0} placeholder="Quiz Title" />
                <select id="subjectId" value={quizForm.subjectId} onChange={handleQuizInput} className="p-3 border border-[#1A2340] bg-white text-[#1A2340] text-base focus:outline-none focus:ring-2 focus:ring-[#1A2340] w-full" required aria-label="Subject" tabIndex={0}>
                  <option value="">Select Subject</option>
                  {subjects.map((subject: any) => (
                    <option key={subject.id} value={subject.id}>{subject.name}</option>
                  ))}
                </select>
                <select id="type" value={quizForm.type} onChange={handleQuizInput} className="p-3 border border-[#1A2340] bg-white text-[#1A2340] text-base focus:outline-none focus:ring-2 focus:ring-[#1A2340] w-full" aria-label="Quiz Type" tabIndex={0}>
                  <option value="LOTS">LOTS (Lower Order Thinking Skills)</option>
                  <option value="HOTS">HOTS (Higher Order Thinking Skills)</option>
                </select>
                {/* Section checkboxes */}
                <div className="flex flex-wrap gap-2 mb-2">
                  {gradeSections.filter(gs => teacherSectionIds.includes(gs.id)).map(gs => (
                    <label key={gs.id} className="flex items-center gap-2 text-[#1A2340]">
                      <input
                        type="checkbox"
                        checked={selectedSectionIds.includes(gs.id)}
                        onChange={() => handleSectionCheckbox(gs.id)}
                        aria-label={`Assign ${gs.gradeLevel} ${gs.section}`}
                        tabIndex={0}
                      />
                      {gs.gradeLevel} {gs.section}
                    </label>
                  ))}
                </div>
                {/* Dynamic question builder */}
                {quizForm.questions.map((q, idx) => (
                  <div key={idx} className="flex flex-col gap-2 border-t border-[#1A2340] pt-4 mt-4">
                    <div className="flex gap-2 items-center">
                      <input type="text" value={q.question} onChange={e => handleQuizQuestionChange(idx, "question", e.target.value)} className="flex-1 p-3 border border-[#1A2340] bg-white text-[#1A2340] text-base focus:outline-none focus:ring-2 focus:ring-[#1A2340]" required aria-label={`Question ${idx + 1}`} tabIndex={0} placeholder={`Question ${idx + 1}`} />
                      <select value={q.type} onChange={e => handleQuizQuestionTypeChange(idx, e.target.value)} className="p-3 border border-[#1A2340] bg-white text-[#1A2340] text-base" aria-label="Question Type" tabIndex={0}>
                        {quizForm.type === "LOTS" ? (
                          <>
                            <option value="Multiple Choice">Multiple Choice</option>
                            <option value="True or False">True or False</option>
                            <option value="Fill in the Blanks">Fill in the Blanks</option>
                          </>
                        ) : (
                          <>
                            <option value="Essay">Essay Question</option>
                            <option value="Problem-Solving">Problem-Solving Question</option>
                            <option value="Open-ended">Open-ended Question</option>
                          </>
                        )}
                      </select>
                      <button type="button" className="text-[#1A2340] font-bold px-2" aria-label="Remove Question" tabIndex={0} onClick={() => handleRemoveQuizQuestion(idx)}>×</button>
                    </div>
                    {/* Image upload */}
                    <div className="flex items-center gap-2">

                      <button
                        type="button"
                        className="flex items-center gap-1 px-3 py-1 border border-[#1A2340] bg-white text-[#1A2340] text-sm font-bold"
                        style={{ borderRadius: 0 }}
                        onClick={() => quizFileInputRefs.current[idx]?.click()}
                      >
                        <PhotoIcon className="w-4 h-4" aria-hidden="true" />
                        Attach Image
                      </button>
                      <input
                        type="file"
                        accept="image/*"
                        ref={el => { quizFileInputRefs.current[idx] = el; }}
                        style={{ display: "none" }}
                        onChange={async e => {
                          if (e.target.files && e.target.files[0]) {
                            setIsUploadingImage(true);
                            try {
                              const url = await uploadToImgbb(e.target.files[0]);
                              handleQuizQuestionChange(idx, "imageUrl", url);
                            } catch (err) {
                              alert("Image upload failed");
                            }
                            setIsUploadingImage(false);
                          }
                        }}
                      />
           
                      {q.imageUrl && (
                        <button
                          type="button"
                          className="flex items-center gap-1 px-3 py-1 border border-[#1A2340] bg-white text-[#1A2340] text-xs font-bold ml-2"
                          style={{ borderRadius: 0 }}
                          onClick={() => handleQuizQuestionChange(idx, "imageUrl", "")}
                        >
                          <XMarkIcon className="w-4 h-4" aria-hidden="true" />
                          Remove
                        </button>
                      )}
                    </div>
                    {/* Dynamic fields based on question type */}
                    {q.type === "Multiple Choice" && (
                      <div className="flex flex-col gap-2">
                        {q.options.map((opt: string, oidx: number) => (
                          <div key={oidx} className="flex gap-2 items-center">
                            <input type="text" value={opt} onChange={e => handleQuizOptionChange(idx, oidx, e.target.value)} className="flex-1 p-2 border border-[#1A2340] bg-white text-[#1A2340]" placeholder={`Option ${oidx + 1}`} aria-label={`Option ${oidx + 1}`} tabIndex={0} />
                          </div>
                        ))}
                        <div className="flex gap-2 items-center">
                          <span className="text-[#1A2340]">Correct Answer:</span>
                          <select
                            value={q.answer}
                            onChange={e => handleQuizQuestionChange(idx, "answer", e.target.value)}
                            className="p-2 border border-[#1A2340] bg-white text-[#1A2340]"
                            aria-label="Correct Answer"
                            tabIndex={0}
                            disabled={q.options.some((opt: string) => !opt.trim())}
                          >
                            <option value="" disabled>Select correct answer</option>
                            {q.options.map((opt: string, oidx: number) => (
                              <option key={oidx} value={opt}>{opt || `Option ${oidx + 1}`}</option>
                            ))}
                          </select>
                          {q.options.some((opt: string) => !opt.trim()) && (
                            <span className="text-xs text-red-500 ml-2">Fill all choices first</span>
                          )}
                        </div>
                      </div>
                    )}
                    {q.type === "True or False" && (
                      <div className="flex gap-2 items-center">
                        <span className="text-[#1A2340]">Correct Answer:</span>
                        <select value={q.answer} onChange={e => handleQuizQuestionChange(idx, "answer", e.target.value)} className="p-2 border border-[#1A2340] bg-white text-[#1A2340]" aria-label="Correct Answer" tabIndex={0}>
                          <option value="True">True</option>
                          <option value="False">False</option>
                        </select>
                      </div>
                    )}
                    {q.type === "Fill in the Blanks" && (
                      <input type="text" value={q.answer} onChange={e => handleQuizQuestionChange(idx, "answer", e.target.value)} className="p-2 border border-[#1A2340] bg-white text-[#1A2340]" placeholder="Correct Answer" aria-label="Correct Answer" tabIndex={0} />
                    )}
                  </div>
                ))}
                <button type="button" onClick={handleAddQuizQuestion} className="px-4 py-2 border border-[#1A2340] bg-white text-[#1A2340] font-bold w-fit" tabIndex={0} aria-label="Add Question">+ Add Question</button>
                {quizError && <div className="text-red-600 text-sm" role="alert">{quizError}</div>}
                {quizSuccess && <div className="text-green-700 text-sm" role="status">{quizSuccess}</div>}
                {isUploadingImage && <div className="text-[#1A2340] text-sm">Uploading image, please wait...</div>}
                <button type="submit" className="mt-2 flex items-center justify-center gap-2 px-6 py-3 border border-[#1A2340] bg-[#1A2340] text-white text-base font-bold focus:outline-none focus:ring-2 focus:ring-[#1A2340] w-full" tabIndex={0} aria-label="Create Quiz" disabled={isUploadingImage}>
                  <ClipboardDocumentListIcon className="w-5 h-5" aria-hidden="true" />
                  Create Quiz
                </button>
              </form>
            </div>
            <div>
              <QuizForm title={quizForm.title || "Quiz Preview"} questions={quizForm.questions} subject={subjects.find(s => s.id === quizForm.subjectId)?.name || ""} />
            </div>
          </section>
        )}
        {activeSection === "exams" && (
          // Exam Management
          <section className="w-full max-w-5xl mx-auto flex flex-col gap-8">
            <h2 className="text-xl font-semibold text-[#1A2340] mb-6 flex items-center gap-2"><ClipboardDocumentListIcon className="w-6 h-6" /> My Exams</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full border border-[#1A2340] text-[#1A2340] bg-white" style={{ borderRadius: 0 }}>
                <thead>
                  <tr className="bg-[#FAFAF6]">
                    <th className="border border-[#1A2340] px-4 py-2">Title</th>
                    <th className="border border-[#1A2340] px-4 py-2">Type</th>
                    <th className="border border-[#1A2340] px-4 py-2">Status</th>
                    <th className="border border-[#1A2340] px-4 py-2">Feedback</th>
                    <th className="border border-[#1A2340] px-4 py-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {exams.map((exam) => (
                    <tr key={exam.id} className="bg-white">
                      <td className="border border-[#1A2340] px-4 py-2">{exam.title}</td>
                      <td className="border border-[#1A2340] px-4 py-2">{exam.type}</td>
                      <td className="border border-[#1A2340] px-4 py-2">
                        {exam.status === "pending" && <span className="text-yellow-700 font-semibold">Pending</span>}
                        {exam.status === "approved" && <span className="text-green-700 font-semibold">Approved</span>}
                        {exam.status === "rejected" && <span className="text-red-700 font-semibold">Rejected</span>}
                      </td>
                      <td className="border border-[#1A2340] px-4 py-2">{exam.feedback || <span className="italic text-gray-500">N/A</span>}</td>
                      <td className="border border-[#1A2340] px-4 py-2">
                        {exam.status === "rejected" && (
                          <button className="px-3 py-1 border border-[#1A2340] bg-white text-[#1A2340] text-sm font-bold focus:outline-none focus:ring-2 focus:ring-[#1A2340]" tabIndex={0} aria-label="Edit Exam" onClick={() => handleEditExam(exam.id, { ...exam, status: "pending" })}>Edit & Resubmit</button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </section>
        )}
        {activeSection === "quizzes" && (
          <section className="w-full max-w-5xl mx-auto flex flex-col gap-8">
            <h2 className="text-xl font-semibold text-[#1A2340] mb-6 flex items-center gap-2"><ClipboardDocumentListIcon className="w-6 h-6" /> My Quizzes</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full border border-[#1A2340] text-[#1A2340] bg-white" style={{ borderRadius: 0 }}>
                <thead>
                  <tr className="bg-[#FAFAF6]">
                    <th className="border border-[#1A2340] px-4 py-2">Title</th>
                    <th className="border border-[#1A2340] px-4 py-2">Type</th>
                    <th className="border border-[#1A2340] px-4 py-2">Status</th>
                    <th className="border border-[#1A2340] px-4 py-2">Feedback</th>
                  </tr>
                </thead>
                <tbody>
                  {quizzes.map((quiz) => (
                    <tr key={quiz.id} className="bg-white">
                      <td className="border border-[#1A2340] px-4 py-2">{quiz.title}</td>
                      <td className="border border-[#1A2340] px-4 py-2">{quiz.type}</td>
                      <td className="border border-[#1A2340] px-4 py-2">
                        {quiz.status === "pending" && <span className="text-yellow-700 font-semibold">Pending</span>}
                        {quiz.status === "approved" && <span className="text-green-700 font-semibold">Approved</span>}
                        {quiz.status === "rejected" && <span className="text-red-700 font-semibold">Rejected</span>}
                      </td>
                      <td className="border border-[#1A2340] px-4 py-2">{quiz.feedback || <span className="italic text-gray-500">N/A</span>}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </section>
        )}
        {activeSection === "grading" && (
          <section className="w-full max-w-4xl mx-auto flex flex-col gap-8">
            <h2 className="text-xl font-semibold text-[#1A2340] mb-6 flex items-center gap-2"><AcademicCapIcon className="w-6 h-6" /> Manual Grading</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full border border-[#1A2340] text-[#1A2340] bg-white" style={{ borderRadius: 0 }}>
                <thead>
                  <tr className="bg-[#FAFAF6]">
                    <th className="border border-[#1A2340] px-4 py-2">Student</th>
                    <th className="border border-[#1A2340] px-4 py-2">Exam</th>
                    <th className="border border-[#1A2340] px-4 py-2">Answers</th>
                    <th className="border border-[#1A2340] px-4 py-2">Grade</th>
                    <th className="border border-[#1A2340] px-4 py-2">Action</th>
                  </tr>
                </thead>
                <tbody>
                  {studentResults.filter(r => !r.graded).map((r) => (
                    <tr key={r.id} className="bg-white">
                      <td className="border border-[#1A2340] px-4 py-2">{r.studentName || r.studentId}</td>
                      <td className="border border-[#1A2340] px-4 py-2">{r.examTitle || r.examId}</td>
                      <td className="border border-[#1A2340] px-4 py-2">
                        <ul className="text-xs">
                          {r.questions && r.questions.map((q: any, idx: number) => (
                            <li key={idx} className="mb-2">
                              <div><span className="font-bold">Q{idx + 1}:</span> {q.question}</div>
                              <div><span className="font-bold">Type:</span> {q.type}</div>
                              <div><span className="font-bold">Student Answer:</span> {r.answers && r.answers[idx]}</div>
                            </li>
                          ))}
                        </ul>
                      </td>
                      <td className="border border-[#1A2340] px-4 py-2">
                        <form onSubmit={async e => {
                          e.preventDefault();
                          const grade = (e.target as any).grade.value;
                          await updateDoc(doc(db, "results", r.id), { grade, graded: true });
                          setStudentResults(prev => prev.map(res => res.id === r.id ? { ...res, grade, graded: true } : res));
                        }} className="flex gap-2 items-center">
                          <input name="grade" type="text" className="p-2 border border-[#1A2340] w-16 text-[#1A2340]" required aria-label="Grade" tabIndex={0} />
                          <button type="submit" className="px-3 py-1 border border-[#1A2340] bg-[#1A2340] text-white text-sm font-bold" tabIndex={0} aria-label="Submit Grade">Submit</button>
                        </form>
                      </td>
                      <td className="border border-[#1A2340] px-4 py-2">
                        {r.graded ? <span className="text-green-700 font-semibold">Graded</span> : <span className="text-yellow-700 font-semibold">Pending</span>}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </section>
        )}
        {activeSection === "performance" && (
          <section className="w-full max-w-5xl mx-auto flex flex-col gap-8">
            <h2 className="text-xl font-semibold text-[#1A2340] mb-6 flex items-center gap-2"><CheckCircleIcon className="w-6 h-6" /> Student Performance</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full border border-[#1A2340] text-[#1A2340] bg-white" style={{ borderRadius: 0 }}>
                <thead>
                  <tr className="bg-[#FAFAF6]">
                    <th className="border border-[#1A2340] px-4 py-2">Student</th>
                    <th className="border border-[#1A2340] px-4 py-2">Exam</th>
                    <th className="border border-[#1A2340] px-4 py-2">Score</th>
                    <th className="border border-[#1A2340] px-4 py-2">Status</th>
                    <th className="border border-[#1A2340] px-4 py-2">Details</th>
                  </tr>
                </thead>
                <tbody>
                  {studentResults.map((r) => (
                    <tr key={r.id} className="bg-white">
                      <td className="border border-[#1A2340] px-4 py-2">{r.studentName || r.studentId}</td>
                      <td className="border border-[#1A2340] px-4 py-2">{r.examTitle || r.examId}</td>
                      <td className="border border-[#1A2340] px-4 py-2">{r.score}</td>
                      <td className="border border-[#1A2340] px-4 py-2">{r.graded ? "Graded" : "Completed"}</td>
                      <td className="border border-[#1A2340] px-4 py-2">
                        <button className="px-3 py-1 border border-[#1A2340] bg-white text-[#1A2340] text-sm font-bold focus:outline-none focus:ring-2 focus:ring-[#1A2340]" tabIndex={0} aria-label="View Details" onClick={() => { setSelectedResult(r); setShowResultModal(true); }}>View Details</button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            {/* Modal for exam details */}
            {showResultModal && selectedResult && (
              <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
                <div className="bg-white border border-[#1A2340] p-8 max-w-2xl w-full" style={{ borderRadius: 0 }}>
                  <h3 className="text-xl font-bold text-[#1A2340] mb-4">Exam Details: {selectedResult.examTitle}</h3>
                  <div className="mb-2 text-[#1A2340] font-semibold">Student: {selectedResult.studentName || selectedResult.studentId}</div>
                  <div className="mb-2 text-[#1A2340]">Score: {selectedResult.score}</div>
                  {/* Tab switch info */}
                  <div className="mb-2 text-[#1A2340]">Tab switches: {selectedResult.tabSwitchCount ?? 0}</div>
                  <div className="mb-2 text-[#1A2340]">Total time away: {formatMs(selectedResult.tabTotalAwayTime ?? 0)}</div>
                  {Array.isArray(selectedResult.tabAwayDurations) && selectedResult.tabAwayDurations.length > 0 && (
                    <details className="mb-2 text-[#1A2340]">
                      <summary className="cursor-pointer">Show all away durations</summary>
                      <ul className="ml-4">
                        {selectedResult.tabAwayDurations.map((ms: number, i: number) => (
                          <li key={i}>Away {i + 1}: {formatMs(ms)}</li>
                        ))}
                      </ul>
                    </details>
                  )}
                  <ul className="mb-4 flex flex-col gap-2">
                    {selectedResult.questions && selectedResult.questions.map((q: any, idx: number) => (
                      <li key={idx} className="border-b border-[#1A2340] pb-2">
                        <div className="font-bold text-[#1A2340]">Q{idx + 1}: {q.question}</div>
                        <div className="text-[#1A2340]">Type: {q.type}</div>
                        <div className="text-[#1A2340]">Your answer: <span className="font-semibold">{selectedResult.answers && selectedResult.answers[idx]}</span></div>
                        <div className="text-[#1A2340]">Correct answer: <span className="font-semibold">{q.answer}</span></div>
                      </li>
                    ))}
                  </ul>
                  {selectedResult.feedback && <div className="mb-2 text-[#1A2340]">Feedback: {selectedResult.feedback}</div>}
                  <button className="mt-4 px-6 py-2 border border-[#1A2340] bg-[#1A2340] text-white font-bold" style={{ borderRadius: 0 }} onClick={() => setShowResultModal(false)} aria-label="Close Modal">Close</button>
                </div>
              </div>
            )}
          </section>
        )}
      </main>
      <TeacherNavBar active={activeSection} setActive={setActiveSection} handleLogout={handleLogout} />
    </div>
  );
};

export default TeacherDashboardPage; 