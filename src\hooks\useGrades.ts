import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Grade, Section, GradeWithSections } from '@/lib/types/school';
import { db } from '@/lib/firebase';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';

// Fetch all grades with sections
export const useGradesWithSections = () => {
  return useQuery({
    queryKey: ['grades', 'withSections'],
    queryFn: async (): Promise<GradeWithSections[]> => {
      // Fetch grades
      const gradesSnapshot = await getDocs(
        query(collection(db, 'grades'), orderBy('level', 'asc'))
      );

      const grades = gradesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Grade[];

      // Fetch all sections
      const sectionsSnapshot = await getDocs(
        query(collection(db, 'sections'), orderBy('name', 'asc'))
      );

      const sections = sectionsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Section[];

      // Group sections by grade
      const gradesWithSections: GradeWithSections[] = grades.map(grade => ({
        ...grade,
        sections: sections.filter(section => section.gradeId === grade.id)
      }));

      return gradesWithSections;
    },
  });
};

// Fetch active grades only
export const useActiveGrades = () => {
  return useQuery({
    queryKey: ['grades', 'active'],
    queryFn: async (): Promise<Grade[]> => {
      const gradesSnapshot = await getDocs(
        query(
          collection(db, 'grades'),
          where('isActive', '==', true)
        )
      );

      const grades = gradesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Grade[];

      // Sort by level in JavaScript to avoid Firestore index requirement
      return grades.sort((a, b) => a.level - b.level);
    },
  });
};

// Fetch sections for a specific grade
export const useSectionsByGrade = (gradeId: string | null) => {
  return useQuery({
    queryKey: ['sections', 'byGrade', gradeId],
    queryFn: async (): Promise<Section[]> => {
      if (!gradeId) return [];

      const sectionsSnapshot = await getDocs(
        query(
          collection(db, 'sections'),
          where('gradeId', '==', gradeId),
          orderBy('name', 'asc')
        )
      );

      return sectionsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Section[];
    },
    enabled: !!gradeId,
  });
};

// Fetch all sections
export const useAllSections = () => {
  return useQuery({
    queryKey: ['sections', 'all'],
    queryFn: async (): Promise<Section[]> => {
      const sectionsSnapshot = await getDocs(
        query(collection(db, 'sections'), orderBy('name', 'asc'))
      );

      return sectionsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Section[];
    },
  });
};

// Create grade mutation
export const useCreateGrade = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (gradeData: any) => {
      const response = await fetch('/api/admin/grades', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'create', data: gradeData }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to create grade');
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['grades'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
    },
  });
};

// Update grade mutation
export const useUpdateGrade = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ gradeId, updates }: { gradeId: string; updates: any }) => {
      const response = await fetch('/api/admin/grades', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ gradeId, updates }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to update grade');
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['grades'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
    },
  });
};

// Delete grade mutation
export const useDeleteGrade = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (gradeId: string) => {
      const response = await fetch('/api/admin/grades', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ gradeId }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to delete grade');
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['grades'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
    },
  });
};

// Create section mutation
export const useCreateSection = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (sectionData: any) => {
      const response = await fetch('/api/admin/sections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sectionData),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to create section');
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['grades'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
    },
  });
};

// Update section mutation
export const useUpdateSection = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      const response = await fetch('/api/admin/sections', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, data }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to update section');
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['grades'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
    },
  });
};

// Delete section mutation
export const useDeleteSection = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch('/api/admin/sections', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to delete section');
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['grades'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
    },
  });
};

// Seed grades and sections
export const useSeedGrades = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/admin/grades', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'seed' }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to seed grades');
      }

      return result;
    },
    onSuccess: () => {
      // Invalidate and refetch grades data
      queryClient.invalidateQueries({ queryKey: ['grades'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
    },
  });
};

// Reset grades and sections
export const useResetGrades = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/admin/grades', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'reset' }),
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to reset grades');
      }
      
      return result;
    },
    onSuccess: () => {
      // Invalidate and refetch grades data
      queryClient.invalidateQueries({ queryKey: ['grades'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
    },
  });
};

// Helper function to get grade and section names by IDs
export const useGradeAndSectionNames = () => {
  const { data: grades } = useGradesWithSections();
  
  const getGradeName = (gradeId: string): string => {
    const grade = grades?.find(g => g.id === gradeId);
    return grade?.name || 'Unknown Grade';
  };
  
  const getSectionName = (sectionId: string): string => {
    if (!grades) return 'Unknown Section';
    
    for (const grade of grades) {
      const section = grade.sections?.find(s => s.id === sectionId);
      if (section) {
        return section.name;
      }
    }
    
    return 'Unknown Section';
  };
  
  const getGradeAndSection = (gradeId: string, sectionId: string): string => {
    const gradeName = getGradeName(gradeId);
    const sectionName = getSectionName(sectionId);
    return `${gradeName} - ${sectionName}`;
  };
  
  return {
    getGradeName,
    getSectionName,
    getGradeAndSection,
    grades: grades || [],
  };
};
