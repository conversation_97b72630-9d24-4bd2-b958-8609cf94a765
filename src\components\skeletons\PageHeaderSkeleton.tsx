"use client";
import React from 'react';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

export default function PageHeaderSkeleton() {
  return (
    <SkeletonTheme baseColor="#f3f4f6" highlightColor="#e5e7eb">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Skeleton height={32} width={32} />
          <div>
            <Skeleton height={28} width={200} className="mb-1" />
            <Skeleton height={16} width={250} />
          </div>
        </div>
        <Skeleton height={40} width={120} />
      </div>
    </SkeletonTheme>
  );
}
