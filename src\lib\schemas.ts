import { z } from "zod";

// Student Registration Schema
export const studentSchema = z.object({
  firstName: z.string().min(1, "First name is required").max(50, "First name must be less than 50 characters"),
  middleName: z.string().max(50, "Middle name must be less than 50 characters").optional(),
  lastName: z.string().min(1, "Last name is required").max(50, "Last name must be less than 50 characters"),
  extension: z.string().max(10, "Extension must be less than 10 characters").optional(),
  lrn: z.string().min(1, "LRN is required").regex(/^\d{12}$/, "LRN must be exactly 12 digits"),
  gradeId: z.string().min(1, "Grade is required"),
  sectionId: z.string().min(1, "Section is required"),
  status: z.enum(["active", "inactive", "transferred", "graduated"]).default("active"),
  hash: z.string().optional(), // For QR authentication
});

// Teacher Registration Schema (<PERSON> - <PERSON>'s Schema)
export const teacherSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  middleName: z.string().optional(),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  status: z.enum(["active", "inactive"]).default("active"),
});

// Teacher Assignment Schema (Claude's Schema)
export const teacherAssignmentSchema = z.object({
  gradeId: z.string().min(1, "Grade is required"),
  subjectId: z.string().min(1, "Subject is required"),
  sectionIds: z.array(z.string()).min(1, "At least one section is required"),
});

// Teacher with Assignments Schema (Claude's Schema)
export const teacherWithAssignmentsSchema = teacherSchema.extend({
  assignments: z.array(teacherAssignmentSchema).min(1, "At least one assignment is required"),
});

// Login Schema
export const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

// Sign Up Schema
export const signUpSchema = z.object({
  fullName: z.string().min(1, "Full name is required").max(100, "Full name must be less than 100 characters"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  confirmPassword: z.string().min(6, "Please confirm your password"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Exam Creation Schema
export const examSchema = z.object({
  title: z.string().min(1, "Exam title is required").max(100, "Title must be less than 100 characters"),
  subject: z.string().min(1, "Subject is required"),
  gradeLevel: z.string().min(1, "Grade level is required"),
  type: z.enum(["quiz", "exam", "activity"]).default("exam"),
  timeLimit: z.number().min(1, "Time limit must be at least 1 minute").max(300, "Time limit cannot exceed 5 hours"),
  instructions: z.string().max(1000, "Instructions must be less than 1000 characters").optional(),
  questions: z.array(z.object({
    question: z.string().min(1, "Question is required"),
    type: z.enum(["multiple-choice", "identification", "true-false"]),
    options: z.array(z.string()).optional(),
    answer: z.string().min(1, "Answer is required"),
    points: z.number().min(1, "Points must be at least 1").default(1),
  })).min(1, "At least one question is required"),
});

// Type exports for TypeScript
export type StudentFormData = z.infer<typeof studentSchema>;
export type TeacherFormData = z.infer<typeof teacherSchema>;
export type TeacherAssignmentFormData = z.infer<typeof teacherAssignmentSchema>;
export type TeacherWithAssignmentsFormData = z.infer<typeof teacherWithAssignmentsSchema>;
export type LoginFormData = z.infer<typeof loginSchema>;
export type SignUpFormData = z.infer<typeof signUpSchema>;
export type ExamFormData = z.infer<typeof examSchema>;
