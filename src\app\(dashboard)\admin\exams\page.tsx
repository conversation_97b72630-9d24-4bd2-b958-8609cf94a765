"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';

// Mock data for demonstration
const mockExams = [
  {
    id: 'exam1',
    title: 'Mathematics Quarter 1 Exam',
    subject: 'Mathematics',
    grade: 'Grade 4',
    section: 'Section A',
    teacher: '<PERSON>',
    teacherId: 'teacher1',
    status: 'pending_approval',
    submittedAt: '2024-01-15T10:30:00Z',
    questions: 25,
    duration: 60,
    description: 'Comprehensive exam covering basic arithmetic, fractions, and word problems.'
  },
  {
    id: 'exam2',
    title: 'Science Chapter 3 Assessment',
    subject: 'Science',
    grade: 'Grade 5',
    section: 'Section B',
    teacher: '<PERSON>',
    teacherId: 'teacher2',
    status: 'pending_approval',
    submittedAt: '2024-01-14T14:20:00Z',
    questions: 20,
    duration: 45,
    description: 'Assessment on plant life cycles and photosynthesis.'
  },
  {
    id: 'exam3',
    title: 'English Reading Comprehension',
    subject: 'English',
    grade: 'Grade 6',
    section: 'Section C',
    teacher: '<PERSON> <PERSON>',
    teacherId: 'teacher3',
    status: 'approved',
    submittedAt: '2024-01-13T09:15:00Z',
    approvedAt: '2024-01-13T16:45:00Z',
    questions: 15,
    duration: 40,
    description: 'Reading comprehension test with short stories and questions.'
  }
];

type ExamStatus = 'all' | 'pending_approval' | 'approved' | 'returned';

export default function ExamsPage() {
  const [statusFilter, setStatusFilter] = useState<ExamStatus>('all');
  const [selectedExam, setSelectedExam] = useState<string | null>(null);

  const filteredExams = mockExams.filter(exam =>
    statusFilter === 'all' || exam.status === statusFilter
  );

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending_approval':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            Pending Approval
          </span>
        );
      case 'approved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            Approved
          </span>
        );
      case 'returned':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            Returned
          </span>
        );
      default:
        return null;
    }
  };

  const handleApprove = (examId: string) => {
    console.log('Approving exam:', examId);
    // TODO: Implement approval logic
  };

  const handleReturn = (examId: string) => {
    console.log('Returning exam:', examId);
    // TODO: Implement return logic
  };

  const pendingCount = mockExams.filter(e => e.status === 'pending_approval').length;
  const approvedCount = mockExams.filter(e => e.status === 'approved').length;
  const totalCount = mockExams.length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-none flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Exam Approval</h2>
            <p className="text-gray-600">Review and approve exams submitted by teachers</p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-none flex items-center justify-center">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Exams</p>
              <p className="text-2xl font-bold text-gray-900">{totalCount}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-none flex items-center justify-center">
              <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending Approval</p>
              <p className="text-2xl font-bold text-gray-900">{pendingCount}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-none flex items-center justify-center">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Approved</p>
              <p className="text-2xl font-bold text-gray-900">{approvedCount}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-red-100 rounded-none flex items-center justify-center">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Returned</p>
              <p className="text-2xl font-bold text-gray-900">0</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Exam Submissions</h3>
          <div className="flex space-x-2">
            {(['all', 'pending_approval', 'approved', 'returned'] as ExamStatus[]).map((status) => (
              <button
                key={status}
                onClick={() => setStatusFilter(status)}
                className={`px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer ${
                  statusFilter === status
                    ? 'bg-[#84a98c] text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {status === 'all' ? 'All' :
                 status === 'pending_approval' ? 'Pending' :
                 status === 'approved' ? 'Approved' : 'Returned'}
              </button>
            ))}
          </div>
        </div>

        {/* Exam List */}
        <div className="space-y-4">
          {filteredExams.length === 0 ? (
            <div className="text-center py-8">
              <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-gray-500">No exams found for the selected filter</p>
            </div>
          ) : (
            filteredExams.map((exam) => (
              <div key={exam.id} className="bg-white border border-gray-200 rounded-none p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="text-lg font-semibold text-gray-900">{exam.title}</h4>
                      {getStatusBadge(exam.status)}
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">Subject:</span> {exam.subject}
                      </div>
                      <div>
                        <span className="font-medium">Grade:</span> {exam.grade}
                      </div>
                      <div>
                        <span className="font-medium">Section:</span> {exam.section}
                      </div>
                      <div>
                        <span className="font-medium">Teacher:</span> {exam.teacher}
                      </div>
                      <div>
                        <span className="font-medium">Questions:</span> {exam.questions}
                      </div>
                      <div>
                        <span className="font-medium">Duration:</span> {exam.duration} minutes
                      </div>
                      <div>
                        <span className="font-medium">Submitted:</span> {new Date(exam.submittedAt).toLocaleDateString()}
                      </div>
                      {exam.status === 'approved' && exam.approvedAt && (
                        <div>
                          <span className="font-medium">Approved:</span> {new Date(exam.approvedAt).toLocaleDateString()}
                        </div>
                      )}
                    </div>

                    <p className="text-gray-700 mb-4">{exam.description}</p>
                  </div>

                  {exam.status === 'pending_approval' && (
                    <div className="flex space-x-2 ml-4">
                      <Button
                        variant="outlined"
                        theme="error"
                        size="sm"
                        onClick={() => handleReturn(exam.id)}
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        Return
                      </Button>
                      <Button
                        variant="filled"
                        theme="success"
                        size="sm"
                        onClick={() => handleApprove(exam.id)}
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Approve
                      </Button>
                    </div>
                  )}

                  {exam.status === 'approved' && (
                    <div className="ml-4">
                      <Button
                        variant="outlined"
                        theme="neutral"
                        size="sm"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        View Details
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </Card>
    </div>
  );
}
