/**
 * Academic Statistics Service
 * Provides aggregated statistics for the academic structure
 * Works with the seeded data structure
 */

import { db } from '@/lib/firebase';
import { collection, getDocs } from 'firebase/firestore';

export interface AcademicStats {
  totalGrades: number;
  totalSections: number;
  totalSubjects: number;
  totalStudents: number;
  totalTeachers: number;
  gradeStats: GradeStats[];
}

export interface GradeStats {
  gradeId: string;
  gradeName: string;
  gradeLevel: number;
  sectionCount: number;
  subjectCount: number;
  studentCount: number;
  isActive: boolean;
  sections: SectionStats[];
}

export interface SectionStats {
  sectionId: string;
  sectionName: string;
  studentCount: number;
  isActive: boolean;
}

/**
 * Get comprehensive academic statistics
 */
export async function getAcademicStats(): Promise<AcademicStats> {
  try {
    // Fetch all data in parallel
    const [gradesSnapshot, sectionsSnapshot, subjectsSnapshot, studentsSnapshot, teachersSnapshot] = await Promise.all([
      getDocs(collection(db, 'grades')),
      getDocs(collection(db, 'sections')),
      getDocs(collection(db, 'subjects')),
      getDocs(collection(db, 'students')),
      getDocs(collection(db, 'teachers'))
    ]);

    // Convert to arrays
    const grades = gradesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    const sections = sectionsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    const subjects = subjectsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    const students = studentsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    const teachers = teachersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    // Calculate grade statistics
    const gradeStats: GradeStats[] = grades.map(grade => {
      const gradeSections = sections.filter(section => section.gradeId === grade.id);
      const gradeSubjects = subjects.filter(subject => subject.gradeId === grade.id);
      const gradeStudents = students.filter(student => student.gradeId === grade.id);

      const sectionStats: SectionStats[] = gradeSections.map(section => ({
        sectionId: section.id,
        sectionName: section.name,
        studentCount: students.filter(student => student.sectionId === section.id).length,
        isActive: section.isActive
      }));

      return {
        gradeId: grade.id,
        gradeName: grade.name,
        gradeLevel: grade.level,
        sectionCount: gradeSections.length,
        subjectCount: gradeSubjects.length,
        studentCount: gradeStudents.length,
        isActive: grade.isActive,
        sections: sectionStats
      };
    });

    return {
      totalGrades: grades.length,
      totalSections: sections.length,
      totalSubjects: subjects.length,
      totalStudents: students.length,
      totalTeachers: teachers.length,
      gradeStats
    };

  } catch (error) {
    console.error('Error fetching academic stats:', error);
    throw new Error('Failed to fetch academic statistics');
  }
}

/**
 * Get student count by section
 */
export async function getStudentCountBySection(sectionId: string): Promise<number> {
  try {
    const studentsSnapshot = await getDocs(collection(db, 'students'));
    const students = studentsSnapshot.docs.map(doc => doc.data());
    return students.filter(student => student.sectionId === sectionId).length;
  } catch (error) {
    console.error('Error getting student count by section:', error);
    return 0;
  }
}

/**
 * Get student count by grade
 */
export async function getStudentCountByGrade(gradeId: string): Promise<number> {
  try {
    const studentsSnapshot = await getDocs(collection(db, 'students'));
    const students = studentsSnapshot.docs.map(doc => doc.data());
    return students.filter(student => student.gradeId === gradeId).length;
  } catch (error) {
    console.error('Error getting student count by grade:', error);
    return 0;
  }
}

/**
 * Get subject count by grade
 */
export async function getSubjectCountByGrade(gradeId: string): Promise<number> {
  try {
    const subjectsSnapshot = await getDocs(collection(db, 'subjects'));
    const subjects = subjectsSnapshot.docs.map(doc => doc.data());
    return subjects.filter(subject => subject.gradeId === gradeId).length;
  } catch (error) {
    console.error('Error getting subject count by grade:', error);
    return 0;
  }
}
