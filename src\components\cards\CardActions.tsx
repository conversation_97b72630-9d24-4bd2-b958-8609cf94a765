"use client";
import React from 'react';
import { cn } from '@/lib/utils';

interface CardActionsProps {
  children: React.ReactNode;
  className?: string;
  layout?: 'horizontal' | 'vertical';
  spacing?: 'sm' | 'md' | 'lg';
  justify?: 'start' | 'center' | 'end' | 'between';
}

const layoutMap = {
  horizontal: 'flex flex-wrap',
  vertical: 'flex flex-col',
};

const spacingMap = {
  horizontal: {
    sm: 'gap-1',
    md: 'gap-2',
    lg: 'gap-3',
  },
  vertical: {
    sm: 'space-y-1',
    md: 'space-y-2',
    lg: 'space-y-3',
  },
};

const justifyMap = {
  start: 'justify-start',
  center: 'justify-center',
  end: 'justify-end',
  between: 'justify-between',
};

export default function CardActions({ 
  children, 
  className,
  layout = 'horizontal',
  spacing = 'md',
  justify = 'start'
}: CardActionsProps) {
  return (
    <div className={cn(
      layoutMap[layout],
      spacingMap[layout][spacing],
      justifyMap[justify],
      className
    )}>
      {children}
    </div>
  );
}
