"use client";
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/Button';
import { useGradesWithSections } from '@/hooks/useGrades';
import { useSubjects } from '@/hooks/useSubjects';
import { useUpdateTeacher } from '@/hooks/useTeachers';
import { TeacherFormData } from '@/lib/schemas';
import type { Teacher } from '@/lib/services/teacherService';

interface TeacherEditModalProps {
  teacher: Teacher | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
};

const modalVariants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: -50
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.3
    }
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    y: -50,
    transition: {
      duration: 0.2
    }
  }
};

export default function TeacherEditModal({ teacher, isOpen, onClose, onSuccess }: TeacherEditModalProps) {
  const [teacherForm, setTeacherForm] = useState<TeacherFormData>({
    firstName: '',
    middleName: '',
    lastName: '',
    email: '',
    gradeIds: [],
    sectionIds: [],
    subjects: [],
    status: 'active'
  });

  const [selectedGradeIds, setSelectedGradeIds] = useState<string[]>([]);
  const [selectedSectionIds, setSelectedSectionIds] = useState<string[]>([]);
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]);

  const { data: gradesWithSections = [] } = useGradesWithSections();
  const { data: subjects = [] } = useSubjects();
  const updateTeacherMutation = useUpdateTeacher();

  // Get available sections based on selected grades
  const getAvailableSections = () => {
    if (selectedGradeIds.length === 0) return [];
    
    const sections: any[] = [];
    selectedGradeIds.forEach(gradeId => {
      const grade = gradesWithSections.find(g => g.id === gradeId);
      if (grade?.sections) {
        sections.push(...grade.sections.map(section => ({
          ...section,
          gradeName: grade.name
        })));
      }
    });
    
    return sections;
  };

  // Populate form when teacher changes
  useEffect(() => {
    if (teacher) {
      const formData = {
        firstName: teacher.firstName || '',
        middleName: teacher.middleName || '',
        lastName: teacher.lastName || '',
        email: teacher.email || '',
        gradeIds: teacher.gradeIds || [],
        sectionIds: teacher.sectionIds || [],
        subjects: teacher.subjects || [],
        status: teacher.status || 'active'
      };
      
      setTeacherForm(formData);
      setSelectedGradeIds(teacher.gradeIds || []);
      setSelectedSectionIds(teacher.sectionIds || []);
      setSelectedSubjects(teacher.subjects || []);
    }
  }, [teacher]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setTeacherForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleGradeSelection = (gradeId: string) => {
    setSelectedGradeIds(prev => {
      const newSelection = prev.includes(gradeId) 
        ? prev.filter(id => id !== gradeId)
        : [...prev, gradeId];
      
      // Update form
      setTeacherForm(prev => ({ ...prev, gradeIds: newSelection }));
      
      // Remove sections that are no longer available
      const availableSectionIds = getAvailableSections().map(s => s.id);
      const validSectionIds = selectedSectionIds.filter(id => availableSectionIds.includes(id));
      setSelectedSectionIds(validSectionIds);
      setTeacherForm(prev => ({ ...prev, sectionIds: validSectionIds }));
      
      return newSelection;
    });
  };

  const handleSectionSelection = (sectionId: string) => {
    setSelectedSectionIds(prev => {
      const newSelection = prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId];
      
      // Update form
      setTeacherForm(prev => ({ ...prev, sectionIds: newSelection }));
      
      return newSelection;
    });
  };

  const handleSubjectSelection = (subjectName: string) => {
    setSelectedSubjects(prev => {
      const newSelection = prev.includes(subjectName) 
        ? prev.filter(s => s !== subjectName)
        : [...prev, subjectName];
      
      // Update form
      setTeacherForm(prev => ({ ...prev, subjects: newSelection }));
      
      return newSelection;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!teacher) return;

    try {
      await updateTeacherMutation.mutateAsync({
        id: teacher.id,
        updates: teacherForm
      });
      onSuccess();
    } catch (error) {
      // Error handled by mutation
    }
  };

  if (!teacher) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed left-[50%] top-[50%] z-50 w-full max-w-4xl max-h-[90vh] translate-x-[-50%] translate-y-[-50%] bg-white border-2 border-[#84a98c] shadow-lg p-6 overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="mb-6">
              <h2 className="text-lg font-semibold leading-none tracking-tight text-[#84a98c] mb-2">Edit Teacher</h2>
              <p className="text-sm text-gray-600">
                Update teacher information and assignments.
              </p>
            </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">First Name *</label>
              <input
                type="text"
                name="firstName"
                value={teacherForm.firstName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-[#84a98c] bg-white text-gray-900 placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-[#84a98c]"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">Middle Name</label>
              <input
                type="text"
                name="middleName"
                value={teacherForm.middleName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-[#84a98c] bg-white text-gray-900 placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-[#84a98c]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">Last Name *</label>
              <input
                type="text"
                name="lastName"
                value={teacherForm.lastName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-[#84a98c] bg-white text-gray-900 placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-[#84a98c]"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">Email *</label>
              <input
                type="email"
                name="email"
                value={teacherForm.email}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-[#84a98c] bg-white text-gray-900 placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-[#84a98c]"
                required
              />
            </div>
          </div>

          {/* Status */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">Status</label>
              <select
                name="status"
                value={teacherForm.status}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-[#84a98c] bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-[#84a98c]"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          {/* Grade Selection */}
          <div>
            <label className="block text-sm font-medium text-[#84a98c] mb-2">
              Grade Levels * (Select multiple)
            </label>
            <div className="grid grid-cols-3 gap-2 border p-3 rounded">
              {gradesWithSections.map(grade => (
                <label key={grade.id} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedGradeIds.includes(grade.id)}
                    onChange={() => handleGradeSelection(grade.id)}
                    className="rounded border-gray-300 text-[#84a98c] focus:ring-[#84a98c]"
                  />
                  <span className="text-sm text-gray-900">{grade.name}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Section Selection */}
          {selectedGradeIds.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-2">
                Sections * (Select multiple)
              </label>
              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto border p-3 rounded">
                {getAvailableSections().map(section => (
                  <label key={section.id} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectedSectionIds.includes(section.id)}
                      onChange={() => handleSectionSelection(section.id)}
                      className="rounded border-gray-300 text-[#84a98c] focus:ring-[#84a98c]"
                    />
                    <span className="text-sm text-gray-900">{section.gradeName} - {section.name}</span>
                  </label>
                ))}
              </div>
            </div>
          )}

          {/* Subject Selection */}
          <div>
            <label className="block text-sm font-medium text-[#84a98c] mb-2">
              Subjects * (Select multiple)
            </label>
            <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto border p-3 rounded">
              {subjects.map(subject => (
                <label key={subject.id} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedSubjects.includes(subject.name)}
                    onChange={() => handleSubjectSelection(subject.name)}
                    className="rounded border-gray-300 text-[#84a98c] focus:ring-[#84a98c]"
                  />
                  <span
                    className="text-sm px-2 py-1 rounded text-gray-900"
                    style={{ backgroundColor: subject.color || '#E5E7EB' }}
                  >
                    {subject.name}
                  </span>
                </label>
              ))}
            </div>
          </div>

            <div className="flex justify-end space-x-2 mt-6">
            <Button
              type="button"
              onClick={onClose}
              variant="outlined"
              theme="neutral"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="filled"
              theme="primary"
              loading={updateTeacherMutation.isPending}
              disabled={updateTeacherMutation.isPending}
            >
              {updateTeacherMutation.isPending ? 'Updating...' : 'Update Teacher'}
            </Button>
            </div>
          </form>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
