/**
 * Complete Schema Seeder
 * Populates the database with a complete academic structure following <PERSON>'s schema
 * 
 * Structure:
 * - Grades: 4, 5, 6 (Elementary focus)
 * - Sections: A, B, C, D per grade (4 sections each)
 * - Subjects: 8 K-12 subjects per grade (English, Math, Science, Filipino, AP, ESP, MAPEH, TLE)
 * - Students: 25-30 per section (realistic distribution)
 * - Teachers: 6-8 teachers with subject assignments
 */

import { db } from '@/lib/firebase';
import { collection, getDocs, deleteDoc, writeBatch, doc } from 'firebase/firestore';

// Individual seeders
import { seedGrades, clearGrades } from './gradeSeeder';
import { seedSections, clearSections } from './sectionSeeder';
import { seedSubjects, clearSubjects } from './subjectSeeder';
import { seedStudents, clearStudents } from './studentSeeder';
import { seedTeachers, clearTeachers } from './teacherSeeder';

/**
 * Clear all academic data from the database
 */
export async function clearAllAcademicData() {
  console.log('🧹 Clearing all academic data...');
  
  try {
    // Clear in reverse dependency order
    await clearTeachers();
    await clearStudents();
    await clearSubjects();
    await clearSections();
    await clearGrades();
    
    console.log('✅ All academic data cleared successfully!');
  } catch (error) {
    console.error('❌ Error clearing academic data:', error);
    throw error;
  }
}

/**
 * Seed complete academic structure
 */
export async function seedCompleteAcademicStructure() {
  console.log('🌱 Starting complete academic structure seeding...');
  console.log('📚 Creating academic structure for Pinagbarilan Elementary School');
  console.log('');

  try {
    // Step 1: Seed Grades (4, 5, 6)
    console.log('📚 Step 1: Creating Grades...');
    const grades = await seedGrades();
    console.log(`✅ Created ${grades.length} grades: ${grades.map(g => g.name).join(', ')}`);
    console.log('');

    // Step 2: Seed Sections (A, B, C, D for each grade)
    console.log('🏫 Step 2: Creating Sections...');
    const sections = await seedSections(grades);
    console.log(`✅ Created ${sections.length} sections across all grades`);
    console.log('');

    // Step 3: Seed Subjects (8 K-12 subjects per grade)
    console.log('📖 Step 3: Creating Subjects...');
    const subjects = await seedSubjects(grades);
    console.log(`✅ Created ${subjects.length} subjects (8 per grade)`);
    console.log('');

    // Step 4: Seed Students (25-30 per section)
    console.log('👥 Step 4: Creating Students...');
    const students = await seedStudents(grades, sections);
    console.log(`✅ Created ${students.length} students distributed across sections`);
    console.log('');

    // Step 5: Seed Teachers (with subject assignments)
    console.log('👨‍🏫 Step 5: Creating Teachers...');
    const teachers = await seedTeachers(grades, subjects);
    console.log(`✅ Created ${teachers.length} teachers with subject assignments`);
    console.log('');

    // Summary
    console.log('🎉 Complete Academic Structure Created Successfully!');
    console.log('');
    console.log('📊 Summary:');
    console.log(`   • ${grades.length} Grade Levels (${grades.map(g => g.level).join(', ')})`);
    console.log(`   • ${sections.length} Sections (4 per grade)`);
    console.log(`   • ${subjects.length} Subjects (8 per grade)`);
    console.log(`   • ${students.length} Students (distributed across sections)`);
    console.log(`   • ${teachers.length} Teachers (with subject assignments)`);
    console.log('');
    console.log('🚀 Database is now ready for dashboard development!');

    return {
      grades,
      sections,
      subjects,
      students,
      teachers,
      summary: {
        gradeCount: grades.length,
        sectionCount: sections.length,
        subjectCount: subjects.length,
        studentCount: students.length,
        teacherCount: teachers.length
      }
    };

  } catch (error) {
    console.error('❌ Error seeding academic structure:', error);
    throw error;
  }
}

/**
 * Reset and reseed complete academic structure
 */
export async function resetAndSeedAcademicStructure() {
  console.log('🔄 Resetting and reseeding complete academic structure...');
  console.log('');

  try {
    // Clear existing data
    await clearAllAcademicData();
    console.log('');

    // Seed new data
    const result = await seedCompleteAcademicStructure();
    
    return result;
  } catch (error) {
    console.error('❌ Error resetting academic structure:', error);
    throw error;
  }
}

/**
 * Development seeder - includes sample data for testing
 */
export async function seedDevelopmentData() {
  console.log('🛠️ Seeding development data...');
  
  try {
    const result = await resetAndSeedAcademicStructure();
    
    console.log('');
    console.log('🛠️ Development environment ready!');
    console.log('   • All academic data populated');
    console.log('   • Ready for dashboard development');
    console.log('   • Sample students and teachers created');
    
    return result;
  } catch (error) {
    console.error('❌ Error seeding development data:', error);
    throw error;
  }
}

/**
 * Production seeder - minimal data for production setup
 */
export async function seedProductionData() {
  console.log('🏭 Seeding production data...');
  
  try {
    // For production, we might want different logic
    // For now, use the same structure but could be customized
    const result = await seedCompleteAcademicStructure();
    
    console.log('');
    console.log('🏭 Production environment ready!');
    console.log('   • Academic structure created');
    console.log('   • Ready for real student/teacher data');
    
    return result;
  } catch (error) {
    console.error('❌ Error seeding production data:', error);
    throw error;
  }
}

// Export individual functions for flexibility
export {
  seedGrades,
  seedSections,
  seedSubjects,
  seedStudents,
  seedTeachers,
  clearGrades,
  clearSections,
  clearSubjects,
  clearStudents,
  clearTeachers
};
