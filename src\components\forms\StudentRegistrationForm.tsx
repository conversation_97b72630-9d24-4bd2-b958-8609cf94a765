"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { studentSchema, type StudentFormData } from "@/lib/schemas";
import { Button } from "../ui/Button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { AcademicCapIcon } from "@heroicons/react/24/outline";
import toast from "react-hot-toast";

interface StudentRegistrationFormProps {
  onSubmit: (data: StudentFormData) => Promise<void>;
  isSubmitting?: boolean;
  onCancel?: () => void;
}

export default function StudentRegistrationForm({
  onSubmit,
  isSubmitting = false,
  onCancel,
}: StudentRegistrationFormProps) {
  const form = useForm<StudentFormData>({
    resolver: zodResolver(studentSchema),
    defaultValues: {
      firstName: "",
      middleName: "",
      lastName: "",
      extension: "",
      lrn: "",
      gradeId: "",
      sectionId: "",
      status: "active",
    },
  });

  const handleSubmit = async (data: StudentFormData) => {
    try {
      await onSubmit(data);
      form.reset();
      toast.success("Student registered successfully!");
    } catch (error: any) {
      toast.error(error.message || "Failed to register student");
    }
  };

  return (
    <div className="bg-white border-2 border-[#84a98c] p-6">
      <div className="flex items-center space-x-3 mb-6">
        <AcademicCapIcon className="w-6 h-6 text-[#84a98c]" />
        <h3 className="text-lg font-semibold text-[#84a98c]">Register New Student</h3>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#84a98c] font-medium">First Name *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter first name"
                      className="border-2 border-gray-300 focus:border-[#84a98c] focus:ring-0"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="middleName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#84a98c] font-medium">Middle Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter middle name"
                      className="border-2 border-gray-300 focus:border-[#84a98c] focus:ring-0"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#84a98c] font-medium">Last Name *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter last name"
                      className="border-2 border-gray-300 focus:border-[#84a98c] focus:ring-0"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="extension"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#84a98c] font-medium">Extension</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Jr., Sr., III, etc."
                      className="border-2 border-gray-300 focus:border-[#84a98c] focus:ring-0"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lrn"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#84a98c] font-medium">LRN *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="12-digit Learner Reference Number"
                      className="border-2 border-gray-300 focus:border-[#84a98c] focus:ring-0 font-mono"
                      maxLength={12}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gradeId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#84a98c] font-medium">Grade Level *</FormLabel>
                  <FormControl>
                    <select
                      className="flex h-10 w-full border-2 border-gray-300 bg-background px-3 py-2 text-sm focus:border-[#84a98c] focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                      {...field}
                    >
                      <option value="">Select Grade Level</option>
                      <option value="1">Grade 1</option>
                      <option value="2">Grade 2</option>
                      <option value="3">Grade 3</option>
                      <option value="4">Grade 4</option>
                      <option value="5">Grade 5</option>
                      <option value="6">Grade 6</option>
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="sectionId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#84a98c] font-medium">Section *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter section"
                      className="border-2 border-gray-300 focus:border-[#84a98c] focus:ring-0"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#84a98c] font-medium">Status</FormLabel>
                  <FormControl>
                    <select
                      className="flex h-10 w-full border-2 border-gray-300 bg-background px-3 py-2 text-sm focus:border-[#84a98c] focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                      {...field}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="transferred">Transferred</option>
                      <option value="graduated">Graduated</option>
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <Button
              type="submit"
              variant="filled"
              theme="primary"
              size="md"
              radius="none"
              loading={isSubmitting}
              loadingText="Registering..."
              leftIcon={<AcademicCapIcon className="w-4 h-4" />}
            >
              Register Student
            </Button>

            {onCancel && (
              <Button
                type="button"
                onClick={onCancel}
                variant="outlined"
                theme="primary"
                size="md"
                radius="none"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </Form>
    </div>
  );
}
