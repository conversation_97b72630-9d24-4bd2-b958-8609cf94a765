"use client";
import React from 'react';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { BaseCard } from '../cards';

interface CardSkeletonProps {
  count?: number;
  borderColor?: 'default' | 'primary' | 'student' | 'teacher' | 'admin';
}

export default function CardSkeleton({ count = 1, borderColor = 'primary' }: CardSkeletonProps) {
  return (
    <SkeletonTheme baseColor="#f3f4f6" highlightColor="#e5e7eb">
      {Array.from({ length: count }).map((_, index) => (
        <BaseCard key={index} borderColor={borderColor} hover={false}>
          {/* Header */}
          <div className="flex justify-between items-start mb-3">
            <div className="flex-1">
              <Skeleton height={24} width="70%" className="mb-1" />
              <Skeleton height={16} width="50%" />
            </div>
            <Skeleton height={24} width={60} />
          </div>

          {/* Content */}
          <div className="space-y-2 mb-4">
            <div>
              <Skeleton height={12} width="30%" className="mb-1" />
              <Skeleton height={20} width="80%" />
            </div>
            <div>
              <Skeleton height={12} width="25%" className="mb-1" />
              <Skeleton height={20} width="60%" />
            </div>
            <div>
              <Skeleton height={12} width="35%" className="mb-1" />
              <Skeleton height={20} width="90%" />
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-2">
            <Skeleton height={32} width={60} />
            <Skeleton height={32} width={60} />
            <Skeleton height={32} width={80} />
          </div>
        </BaseCard>
      ))}
    </SkeletonTheme>
  );
}
