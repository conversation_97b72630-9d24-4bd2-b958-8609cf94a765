import type { Metadata } from "next";
import { Poppins } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "./contexts/AuthContext";
import QueryProvider from "./providers/QueryProvider";
import ToastProvider from "./providers/ToastProvider";

// Import Poppins font
const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["300"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "GreenTech - Sustainable Digital Exam Platform",
  description: "Promoting environmental sustainability through digital exams. Secure QR code login, automated grading, and real-time exam features for students, teachers, and administrators.",
  keywords: "digital exams, sustainable education, QR code login, automated grading, eco-friendly learning",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${poppins.variable} antialiased`}
        style={{
          fontFamily: 'var(--font-poppins), system-ui, sans-serif',
          fontWeight: 300
        }}
      >
        <QueryProvider>
          <AuthProvider>
            {children}
            <ToastProvider />
          </AuthProvider>
        </QueryProvider>
      </body>
    </html>
  );
}
