"use client";
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardInfoRow, CardBadge, CardActions } from './index';

interface TeacherCardTemplateProps {
  teacher: {
    firstName: string;
    middleName?: string;
    lastName: string;
    email: string;
    status: string;
    subjects?: string[];
    gradeIds?: string[];
    sectionIds?: string[];
    examIds?: string[];
  };
  subjectBadges?: React.ReactNode;
  gradeBadges?: React.ReactNode;
  sectionBadges?: React.ReactNode;
  actions?: React.ReactNode;
  className?: string;
}

export default function TeacherCardTemplate({ 
  teacher, 
  subjectBadges,
  gradeBadges,
  sectionBadges,
  actions,
  className 
}: TeacherCardTemplateProps) {
  const fullName = `${teacher.lastName}, ${teacher.firstName}${
    teacher.middleName ? ` ${teacher.middleName}` : ''
  }`;

  const statusBadge = (
    <CardBadge
      variant={teacher.isActive ? 'success' : 'error'}
    >
      {teacher.isActive ? 'Active' : 'Inactive'}
    </CardBadge>
  );

  return (
    <BaseCard 
      borderColor="teacher" 
      className={className}
    >
      <CardHeader
        title={fullName}
        subtitle={teacher.email}
        badge={statusBadge}
        titleColor="teacher"
      />

      <CardContent>
        <CardInfoRow
          label="Subjects"
          labelColor="teacher"
        >
          {subjectBadges || (
            <span className="text-xs text-gray-400">No subjects assigned</span>
          )}
        </CardInfoRow>

        <CardInfoRow
          label="Grades"
          labelColor="teacher"
        >
          {gradeBadges || (
            <span className="text-xs text-gray-400">No grades assigned</span>
          )}
        </CardInfoRow>

        <CardInfoRow
          label="Sections"
          labelColor="teacher"
        >
          {sectionBadges || (
            <span className="text-xs text-gray-400">No sections assigned</span>
          )}
        </CardInfoRow>

        <CardInfoRow
          label="Exams"
          labelColor="teacher"
          value={`${teacher.examIds?.length || 0} exams`}
        />
      </CardContent>

      {actions && (
        <CardActions>
          {actions}
        </CardActions>
      )}
    </BaseCard>
  );
}
