"use client";
import React, { useState } from 'react';
import { PencilIcon, TrashIcon, BuildingLibraryIcon, ClipboardDocumentListIcon, PlusIcon } from "@heroicons/react/24/outline";
import { Button } from '@/components/ui/Button';
import { CardBadge } from '@/components/cards';
import { useStudentCountByGrade, useSubjectCountByGrade, useStudentCountBySection } from '@/hooks/useAcademicStats';
import { Grade } from '@/lib/services/gradeService';
import { Subject } from '@/lib/types/school';
import SectionManagementModal from './SectionManagementModal';
import SubjectManagementModal from './SubjectManagementModal';

interface GradeCardProps {
  grade: Grade & { sections?: any[] };
  subjects: Subject[];
  settings?: any;
}

interface SectionItemProps {
  section: any;
  canDeleteSections: boolean;
  onEditSection: (section: any) => void;
  onDeleteSection: (section: any) => void;
}

function SectionItem({ section, canDeleteSections, onEditSection, onDeleteSection }: SectionItemProps) {
  const { data: studentCount = 0 } = useStudentCountBySection(section.id);

  return (
    <div className="flex items-center justify-between p-3 bg-gray-50 group hover:bg-gray-100 transition-colors">
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-[#84a98c] bg-opacity-20 flex items-center justify-center">
          <span className="text-sm font-medium text-white">{section.name[0]}</span>
        </div>
        <div>
          <p className="font-medium text-gray-900">{section.name}</p>
          <p className="text-sm text-gray-600">Students: {studentCount}</p>
        </div>
      </div>
      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <button
          onClick={() => onEditSection(section)}
          className="p-1 text-gray-400 hover:text-blue-600 transition-colors cursor-pointer"
          title="Edit Section"
        >
          <PencilIcon className="w-4 h-4" />
        </button>
        {canDeleteSections && (
          <button
            onClick={() => onDeleteSection(section)}
            className="p-1 text-gray-400 hover:text-red-600 transition-colors cursor-pointer"
            title="Delete Section"
          >
            <TrashIcon className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
}

export default function GradeCardHorizontal({ grade, subjects, settings }: GradeCardProps) {
  const [editingSection, setEditingSection] = useState<any>(null);
  const [editingSubject, setEditingSubject] = useState<any>(null);
  const [deletingSection, setDeletingSection] = useState<any>(null);
  const [deletingSubject, setDeletingSubject] = useState<any>(null);
  const [showAddSectionModal, setShowAddSectionModal] = useState(false);
  const [showAddSubjectModal, setShowAddSubjectModal] = useState(false);

  // Get real-time counts from academic stats
  const { data: studentCount = 0 } = useStudentCountByGrade(grade.id);
  const { data: subjectCount = 0 } = useSubjectCountByGrade(grade.id);

  const sectionCount = grade.sections?.length || 0;
  const totalStudents = studentCount;

  // Check delete permissions from settings
  const canDeleteSections = settings?.allowDeleteSections || false;
  const canDeleteSubjects = settings?.allowDeleteSubjects || false;

  return (
    <div className="bg-white border-2 border-gray-200 p-6">
      {/* Grade Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-[#84a98c] bg-opacity-10">
            <BuildingLibraryIcon className="w-6 h-6 text-[#84a98c]" />
          </div>
          <div>
            <div className="flex items-center space-x-3">
              <h3 className="text-xl font-semibold text-gray-900">{grade.name}</h3>  
              <CardBadge variant={sectionCount > 0 ? 'warning' : 'error'}>
                {sectionCount > 0 ? sectionCount+' sections' : 'No sections'}
              </CardBadge>
              <CardBadge variant={subjectCount > 0 ? 'warning' : 'error'}>
                {subjectCount > 0 ? subjectCount+' subjects' : 'No subjects'}
              </CardBadge>
          </div>
            
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <CardBadge variant={grade.isActive ? 'success' : 'error'}>
            {grade.isActive ? 'Active' : 'Inactive'}
          </CardBadge>
        </div>
      </div>

      {/* Sections and Subjects Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sections */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-gray-900">Sections ({sectionCount})</h4>
            <Button
              onClick={() => setShowAddSectionModal(true)}
              variant="outlined"
              theme="primary"
              size="sm"
              radius="none"
              leftIcon={<PlusIcon className="w-3 h-3" />}
            >
              Add Section
            </Button>
          </div>
          <div className="space-y-2">
            {grade.sections && grade.sections.length > 0 ? (
              grade.sections.map((section: any) => (
                <SectionItem
                  key={section.id}
                  section={section}
                  canDeleteSections={canDeleteSections}
                  onEditSection={setEditingSection}
                  onDeleteSection={setDeletingSection}
                />
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <BuildingLibraryIcon className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                <p>No sections created yet</p>
              </div>
            )}
          </div>
        </div>

        {/* Subjects */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-gray-900">Subjects ({subjects.length})</h4>
            <Button
              onClick={() => setShowAddSubjectModal(true)}
              variant="outlined"
              theme="primary"
              size="sm"
              radius="none"
              leftIcon={<PlusIcon className="w-3 h-3" />}
            >
              Add Subject
            </Button>
          </div>
          <div className="space-y-2">
            {subjects.length > 0 ? (
              subjects.map((subject) => (
                <div key={subject.id} className="flex items-center justify-between p-3 bg-gray-50 group hover:bg-gray-100 transition-colors">
                  <div className="flex items-center space-x-3">
                    <div 
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: subject.color || '#84a98c' }}
                    />
                    <div>
                      <p className="font-medium text-gray-900">{subject.name}</p>
                      <p className="text-sm text-gray-600 font-mono">{subject.code}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={() => setEditingSubject(subject)}
                      className="p-1 text-gray-400 hover:text-blue-600 transition-colors cursor-pointer"
                      title="Edit Subject"
                    >
                      <PencilIcon className="w-4 h-4" />
                    </button>
                    {canDeleteSubjects && (
                      <button
                        onClick={() => setDeletingSubject(subject)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors cursor-pointer"
                        title="Delete Subject"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <ClipboardDocumentListIcon className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                <p>No subjects created yet</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modals */}
      {/* Section Management Modals */}
      {editingSection && (
        <SectionManagementModal
          isOpen={!!editingSection}
          onClose={() => setEditingSection(null)}
          mode="edit"
          section={editingSection}
        />
      )}

      {deletingSection && (
        <SectionManagementModal
          isOpen={!!deletingSection}
          onClose={() => setDeletingSection(null)}
          mode="delete"
          section={deletingSection}
        />
      )}

      {/* Subject Management Modals */}
      {editingSubject && (
        <SubjectManagementModal
          isOpen={!!editingSubject}
          onClose={() => setEditingSubject(null)}
          mode="edit"
          subject={editingSubject}
        />
      )}

      {deletingSubject && (
        <SubjectManagementModal
          isOpen={!!deletingSubject}
          onClose={() => setDeletingSubject(null)}
          mode="delete"
          subject={deletingSubject}
        />
      )}

      {/* Creation Modals */}
      <SectionManagementModal
        isOpen={showAddSectionModal}
        onClose={() => setShowAddSectionModal(false)}
        mode="create"
        gradeId={grade.id}
      />

      <SubjectManagementModal
        isOpen={showAddSubjectModal}
        onClose={() => setShowAddSubjectModal(false)}
        mode="create"
        gradeId={grade.id}
      />
    </div>
  );
}
