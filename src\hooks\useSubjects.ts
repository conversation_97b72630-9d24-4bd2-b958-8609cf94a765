import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { subjectService, Subject, SubjectFormData } from '@/lib/services/subjectService';
import { toast } from 'react-hot-toast';

// Query keys
export const subjectKeys = {
  all: ['subjects'] as const,
  lists: () => [...subjectKeys.all, 'list'] as const,
  list: (filters: string) => [...subjectKeys.lists(), { filters }] as const,
  details: () => [...subjectKeys.all, 'detail'] as const,
  detail: (id: string) => [...subjectKeys.details(), id] as const,
  byGrade: (gradeId: string) => [...subjectKeys.all, 'byGrade', gradeId] as const,
};

// Get all subjects
export function useSubjects() {
  return useQuery({
    queryKey: subjectKeys.lists(),
    queryFn: () => subjectService.getAllSubjects(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get subjects by grade
export function useSubjectsByGrade(gradeId: string) {
  return useQuery({
    queryKey: subjectKeys.byGrade(gradeId),
    queryFn: () => subjectService.getSubjectsByGrade(gradeId),
    enabled: !!gradeId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get subject by ID
export function useSubject(id: string) {
  return useQuery({
    queryKey: subjectKeys.detail(id),
    queryFn: () => subjectService.getSubjectById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Create subject mutation
export function useAddSubject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SubjectFormData) => subjectService.createSubject(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: subjectKeys.all });
      toast.success('Subject created successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to create subject');
    },
  });
}

// Update subject mutation
export function useUpdateSubject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<SubjectFormData> }) =>
      subjectService.updateSubject(id, updates),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: subjectKeys.all });
      queryClient.invalidateQueries({ queryKey: subjectKeys.detail(id) });
      toast.success('Subject updated successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update subject');
    },
  });
}

// Delete subject mutation
export function useDeleteSubject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => subjectService.deleteSubject(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: subjectKeys.all });
      toast.success('Subject deleted successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to delete subject');
    },
  });
}

// Helper hook for subject options in forms
export function useSubjectOptions(gradeId?: string) {
  const { data: subjects, isLoading } = gradeId
    ? useSubjectsByGrade(gradeId)
    : useSubjects();

  const options = subjects?.map(subject => ({
    value: subject.id,
    label: subject.name,
    code: subject.code,
    color: subject.color
  })) || [];

  return {
    options,
    isLoading
  };
}
