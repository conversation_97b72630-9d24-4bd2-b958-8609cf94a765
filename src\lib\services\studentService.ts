import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  writeBatch,
  increment
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { StudentFormData } from '@/lib/schemas';
import { sectionService } from './gradeService';
import { qrService } from './qrService';

export interface Student extends StudentFormData {
  id: string;
  // Support both old and new data structures
  fullName?: string[]; // New structure: [firstName, middleName, lastName]
  studentId?: string; // New structure: unique student ID
  qr_code?: string; // New structure: QR code field
  enrollmentDate?: string;
  qrUrl?: string;
  qrGenerated?: boolean;
  hash?: string; // Added for QR authentication
  createdAt: string;
  updatedAt: string;
}

export interface StudentWithGradeSection extends Student {
  gradeName?: string;
  sectionName?: string;
  gradeLevel?: number;
}

export const studentService = {
  // Create a new student
  async createStudent(data: StudentFormData): Promise<Student> {
    const now = new Date().toISOString();

    // Convert form data to new database structure
    const studentData = {
      // New structure fields
      fullName: [data.firstName, data.middleName || '', data.lastName],
      extension: data.extension || '',
      gradeId: data.gradeId,
      sectionId: data.sectionId,
      studentId: `STU${Date.now()}`, // Generate unique student ID
      lrn: data.lrn,
      qr_code: '', // Empty initially, will be filled when QR is generated

      // Keep old structure for backward compatibility
      firstName: data.firstName,
      middleName: data.middleName,
      lastName: data.lastName,
      status: data.status,

      // Metadata
      enrollmentDate: now,
      createdAt: now,
      updatedAt: now,
    };

    const batch = writeBatch(db);

    // Add student document
    const studentRef = doc(collection(db, 'students'));
    batch.set(studentRef, studentData);

    // Increment section count
    const sectionRef = doc(db, 'sections', data.sectionId);
    batch.update(sectionRef, {
      currentCount: increment(1),
      updatedAt: now,
    });

    await batch.commit();

    return {
      id: studentRef.id,
      ...studentData,
    };
  },

  // Get all students
  async getAllStudents(): Promise<Student[]> {
    const studentsSnapshot = await getDocs(collection(db, 'students'));

    const students = studentsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Student[];

    // Sort by last name, then first name - handle both old and new data structures
    return students.sort((a, b) => {
      // Get names for comparison
      let aLastName = '', aFirstName = '';
      let bLastName = '', bFirstName = '';

      // Handle student A
      if (a.fullName && Array.isArray(a.fullName)) {
        // New structure: fullName is an array [firstName, middleName, lastName]
        aFirstName = a.fullName[0] || '';
        aLastName = a.fullName[2] || '';
      } else if (a.firstName && a.lastName) {
        // Old structure: individual name fields
        aFirstName = a.firstName;
        aLastName = a.lastName;
      }

      // Handle student B
      if (b.fullName && Array.isArray(b.fullName)) {
        // New structure: fullName is an array [firstName, middleName, lastName]
        bFirstName = b.fullName[0] || '';
        bLastName = b.fullName[2] || '';
      } else if (b.firstName && b.lastName) {
        // Old structure: individual name fields
        bFirstName = b.firstName;
        bLastName = b.lastName;
      }

      const lastNameCompare = aLastName.localeCompare(bLastName);
      if (lastNameCompare !== 0) return lastNameCompare;
      return aFirstName.localeCompare(bFirstName);
    });
  },

  // Get students by grade
  async getStudentsByGrade(gradeId: string): Promise<Student[]> {
    const studentsSnapshot = await getDocs(
      query(collection(db, 'students'), where('gradeId', '==', gradeId))
    );
    
    const students = studentsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Student[];

    // Sort by last name - handle both old and new data structures
    return students.sort((a, b) => {
      let aLastName = '', bLastName = '';

      // Handle student A
      if (a.fullName && Array.isArray(a.fullName)) {
        aLastName = a.fullName[2] || '';
      } else if (a.lastName) {
        aLastName = a.lastName;
      }

      // Handle student B
      if (b.fullName && Array.isArray(b.fullName)) {
        bLastName = b.fullName[2] || '';
      } else if (b.lastName) {
        bLastName = b.lastName;
      }

      return aLastName.localeCompare(bLastName);
    });
  },

  // Get students by section
  async getStudentsBySection(sectionId: string): Promise<Student[]> {
    const studentsSnapshot = await getDocs(
      query(collection(db, 'students'), where('sectionId', '==', sectionId))
    );
    
    const students = studentsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Student[];
    
    // Sort by last name - handle both old and new data structures
    return students.sort((a, b) => {
      let aLastName = '', bLastName = '';

      // Handle student A
      if (a.fullName && Array.isArray(a.fullName)) {
        aLastName = a.fullName[2] || '';
      } else if (a.lastName) {
        aLastName = a.lastName;
      }

      // Handle student B
      if (b.fullName && Array.isArray(b.fullName)) {
        bLastName = b.fullName[2] || '';
      } else if (b.lastName) {
        bLastName = b.lastName;
      }

      return aLastName.localeCompare(bLastName);
    });
  },

  // Get student by ID
  async getStudentById(studentId: string): Promise<Student | null> {
    const studentDoc = await getDoc(doc(db, 'students', studentId));
    
    if (!studentDoc.exists()) {
      return null;
    }

    return {
      id: studentDoc.id,
      ...studentDoc.data()
    } as Student;
  },

  // Get student by LRN
  async getStudentByLRN(lrn: string): Promise<Student | null> {
    const studentsSnapshot = await getDocs(
      query(collection(db, 'students'), where('lrn', '==', lrn))
    );
    
    if (studentsSnapshot.empty) {
      return null;
    }

    const studentDoc = studentsSnapshot.docs[0];
    return {
      id: studentDoc.id,
      ...studentDoc.data()
    } as Student;
  },

  // Update student
  async updateStudent(studentId: string, updates: Partial<StudentFormData>): Promise<void> {
    const batch = writeBatch(db);
    const now = new Date().toISOString();

    // Get current student data to check if section changed
    const currentStudent = await this.getStudentById(studentId);
    if (!currentStudent) {
      throw new Error('Student not found');
    }

    // Update student document
    const studentRef = doc(db, 'students', studentId);
    batch.update(studentRef, {
      ...updates,
      updatedAt: now,
    });

    // If section changed, update section counts
    if (updates.sectionId && updates.sectionId !== currentStudent.sectionId) {
      // Decrement old section count (only if student had a previous section)
      if (currentStudent.sectionId) {
        const oldSectionRef = doc(db, 'sections', currentStudent.sectionId);
        batch.update(oldSectionRef, {
          currentCount: increment(-1),
          updatedAt: now,
        });
      }

      // Increment new section count
      const newSectionRef = doc(db, 'sections', updates.sectionId);
      batch.update(newSectionRef, {
        currentCount: increment(1),
        updatedAt: now,
      });
    }

    await batch.commit();
  },

  // Delete student
  async deleteStudent(studentId: string): Promise<void> {
    const batch = writeBatch(db);
    const now = new Date().toISOString();

    // Get student data to update section count
    const student = await this.getStudentById(studentId);
    if (!student) {
      throw new Error('Student not found');
    }

    // Delete student document
    const studentRef = doc(db, 'students', studentId);
    batch.delete(studentRef);

    // Only decrement section count if student has a sectionId (handles legacy data)
    if (student.sectionId) {
      const sectionRef = doc(db, 'sections', student.sectionId);
      batch.update(sectionRef, {
        currentCount: increment(-1),
        updatedAt: now,
      });
    }

    await batch.commit();
  },

  // Generate and update student QR code
  async generateStudentQR(studentId: string): Promise<void> {
    try {
      // Get student data
      const student = await this.getStudentById(studentId);
      if (!student) {
        throw new Error('Student not found');
      }

      // Generate hash if student doesn't have one
      let hash = student.hash;
      if (!hash) {
        hash = qrService.generateStudentHash(student.lrn);
      }

      // Generate QR code image
      const qrUrl = await qrService.generateStudentQR(student.lrn, hash);

      // Update student record
      const studentRef = doc(db, 'students', studentId);
      await updateDoc(studentRef, {
        hash,
        qrUrl,
        qrGenerated: true,
        updatedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error generating student QR:', error);
      throw new Error('Failed to generate QR code for student');
    }
  },

  // Legacy method - kept for backward compatibility
  async updateStudentQR(studentId: string, qrUrl: string): Promise<void> {
    const studentRef = doc(db, 'students', studentId);
    await updateDoc(studentRef, {
      qrUrl,
      qrGenerated: true,
      updatedAt: new Date().toISOString(),
    });
  },

  // Get students with grade and section names
  async getStudentsWithGradeSection(): Promise<StudentWithGradeSection[]> {
    const students = await this.getAllStudents();
    
    // This would typically be done with a join, but Firestore doesn't support joins
    // So we'll need to fetch grade and section data separately
    // For now, return students as-is and handle the grade/section names in the UI
    return students;
  },

  // Transfer student to different section
  async transferStudent(studentId: string, newGradeId: string, newSectionId: string): Promise<void> {
    await this.updateStudent(studentId, {
      gradeId: newGradeId,
      sectionId: newSectionId,
    });
  },

  // Get student count by section
  async getStudentCountBySection(sectionId: string): Promise<number> {
    const students = await this.getStudentsBySection(sectionId);
    return students.length;
  },

  // Sync section counts (utility function to fix any count discrepancies)
  async syncSectionCounts(): Promise<void> {
    const sections = await sectionService.getAllSections();
    const batch = writeBatch(db);
    const now = new Date().toISOString();

    for (const section of sections) {
      const actualCount = await this.getStudentCountBySection(section.id);
      
      if (section.currentCount !== actualCount) {
        const sectionRef = doc(db, 'sections', section.id);
        batch.update(sectionRef, {
          currentCount: actualCount,
          updatedAt: now,
        });
      }
    }

    await batch.commit();
  }
};
