"use client";
import React from 'react';
import { EyeIcon, PencilIcon, TrashIcon } from "@heroicons/react/24/outline";
import { Button } from '@/components/ui/Button';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { TeacherCardTemplate, CardBadge } from '@/components/cards';
import { useGradeAndSectionNames } from '@/hooks/useGrades';
import { useSubjects } from '@/hooks/useSubjects';
import { useDeleteTeacher } from '@/hooks/useTeachers';
import type { Teacher } from '@/lib/services/teacherService';

interface TeacherCardProps {
  teacher: Teacher;
  onView: () => void;
  onEdit: () => void;
}

export default function TeacherCard({ teacher, onView, onEdit }: TeacherCardProps) {
  const { getGradeName, getSectionName } = useGradeAndSectionNames();
  const { data: subjects = [] } = useSubjects();
  const deleteTeacherMutation = useDeleteTeacher();

  const handleDelete = async () => {
    try {
      await deleteTeacherMutation.mutateAsync(teacher.id);
    } catch (error) {
      // Error handled by mutation
    }
  };

  // Prepare subject badges from assignments
  const uniqueSubjects = teacher.assignments ?
    Array.from(new Set(teacher.assignments.map(a => a.subject?.name).filter(Boolean))) : [];

  const subjectBadges = uniqueSubjects.length > 0 ? (
    <div className="flex flex-wrap gap-1">
      {uniqueSubjects.slice(0, 3).map((subjectName, index) => {
        const assignment = teacher.assignments?.find(a => a.subject?.name === subjectName);
        const subjectColor = assignment?.subject?.color || '#84a98c';
        return (
          <CardBadge
            key={index}
            variant="custom"
            style={{
              backgroundColor: subjectColor,
              color: 'white'
            }}
          >
            {subjectName}
          </CardBadge>
        );
      })}
      {uniqueSubjects.length > 3 && (
        <CardBadge variant="default">
          +{uniqueSubjects.length - 3} more
        </CardBadge>
      )}
    </div>
  ) : undefined;

  // Prepare grade badges from assignments
  const uniqueGrades = teacher.assignments ?
    Array.from(new Set(teacher.assignments.map(a => a.grade?.name).filter(Boolean))) : [];

  const gradeBadges = uniqueGrades.length > 0 ? (
    <div className="flex flex-wrap gap-1">
      {uniqueGrades.map((gradeName, index) => (
        <CardBadge key={index} variant="info">
          {gradeName}
        </CardBadge>
      ))}
    </div>
  ) : undefined;

  // Prepare section badges - our seeded data doesn't include section assignments
  const sectionBadges = undefined;

  // Prepare action buttons
  const actions = (
    <>
      <Button
        onClick={onView}
        variant="outlined"
        theme="primary"
        size="sm"
        radius="none"
        leftIcon={<EyeIcon className="w-4 h-4" />}
      >
        View
      </Button>

      <Button
        onClick={onEdit}
        variant="outlined"
        theme="primary"
        size="sm"
        radius="none"
        leftIcon={<PencilIcon className="w-4 h-4" />}
      >
        Edit
      </Button>

      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button
            variant="outlined"
            theme="error"
            size="sm"
            radius="none"
            leftIcon={<TrashIcon className="w-4 h-4" />}
          >
            Delete
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Teacher</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {teacher.firstName} {teacher.lastName}?
              {teacher.examIds && teacher.examIds.length > 0 && (
                <span className="text-red-600 block mt-2">
                  This teacher has {teacher.examIds.length} exam(s) and cannot be deleted.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <Button
              onClick={handleDelete}
              disabled={teacher.examIds && teacher.examIds.length > 0}
              loading={deleteTeacherMutation.isPending}
              variant="filled"
              theme="error"
              size="sm"
              radius="none"
            >
              {deleteTeacherMutation.isPending ? 'Deleting...' : 'Delete'}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );

  return (
    <TeacherCardTemplate
      teacher={teacher}
      subjectBadges={subjectBadges}
      gradeBadges={gradeBadges}
      sectionBadges={sectionBadges}
      actions={actions}
    />
  );
}
