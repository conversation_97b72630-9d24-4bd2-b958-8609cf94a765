/* GREENTECH Universal Color Palette */

:root {
  /* Primary System Colors (Green Theme) */
  --color-primary-50: #f7faf8;
  --color-primary-100: #eff5f1;
  --color-primary-200: #dfeae3;
  --color-primary-300: #c4d5ca;
  --color-primary-400: #a1bfa9;
  --color-primary-500: #84a98c;
  --color-primary-600: #6b8e72;
  --color-primary-700: #57735d;
  --color-primary-800: #485c4d;
  --color-primary-900: #3d4d41;
  --color-primary-950: #1f2821;

  /* Student Theme (Pastel Blue) */
  --color-student-50: #f0f7ff;
  --color-student-100: #e0efff;
  --color-student-200: #c7e2ff;
  --color-student-300: #a5d0ff;
  --color-student-400: #7bb5ff;
  --color-student-500: #5a9cff;
  --color-student-600: #4285f4;
  --color-student-700: #3b6fd9;
  --color-student-800: #3558b0;
  --color-student-900: #334a8a;
  --color-student-950: #1e2d54;

  /* Teacher Theme (Pastel Purple) */
  --color-teacher-50: #faf7ff;
  --color-teacher-100: #f4efff;
  --color-teacher-200: #ebe2ff;
  --color-teacher-300: #dcc9ff;
  --color-teacher-400: #c7a6ff;
  --color-teacher-500: #b084ff;
  --color-teacher-600: #9c6bff;
  --color-teacher-700: #8855e6;
  --color-teacher-800: #7047c2;
  --color-teacher-900: #5d3c9e;
  --color-teacher-950: #3a2569;

  /* Admin Theme (Navy Blue - Existing) */
  --color-admin-50: #f8fafc;
  --color-admin-100: #f1f5f9;
  --color-admin-200: #e2e8f0;
  --color-admin-300: #cbd5e1;
  --color-admin-400: #94a3b8;
  --color-admin-500: #64748b;
  --color-admin-600: #475569;
  --color-admin-700: #334155;
  --color-admin-800: #1e293b;
  --color-admin-900: #0f172a;
  --color-admin-950: #020617;

  /* Neutral Colors */
  --color-neutral-50: #fafaf9;
  --color-neutral-100: #f5f5f4;
  --color-neutral-200: #e7e5e4;
  --color-neutral-300: #d6d3d1;
  --color-neutral-400: #a8a29e;
  --color-neutral-500: #78716c;
  --color-neutral-600: #57534e;
  --color-neutral-700: #44403c;
  --color-neutral-800: #292524;
  --color-neutral-900: #1c1917;
  --color-neutral-950: #0c0a09;

  /* Semantic Colors */
  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;

  --color-error-50: #fef2f2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;

  --color-warning-50: #fffbeb;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;

  --color-info-50: #eff6ff;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;
  --color-info-700: #1d4ed8;

  /* Background Colors */
  --color-background-primary: #fafaf6;
  --color-background-secondary: #ffffff;
  --color-background-tertiary: #f8f9fa;

  /* Text Colors */
  --color-text-primary: #1a1a1a;
  --color-text-secondary: #6b7280;
  --color-text-tertiary: #9ca3af;
  --color-text-inverse: #ffffff;

  /* Border Colors */
  --color-border-light: #e5e7eb;
  --color-border-medium: #d1d5db;
  --color-border-dark: #6b7280;

  /* Shadow Colors */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transition Durations */
  --transition-fast: 150ms;
  --transition-normal: 200ms;
  --transition-slow: 300ms;

  /* Border Radius */
  --radius-none: 0px;
  --radius-sm: 2px;
  --radius-md: 4px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-full: 9999px;

  /* Spacing Scale */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
}

/* Theme-specific CSS Custom Properties */
.theme-primary {
  --theme-50: var(--color-primary-50);
  --theme-100: var(--color-primary-100);
  --theme-200: var(--color-primary-200);
  --theme-300: var(--color-primary-300);
  --theme-400: var(--color-primary-400);
  --theme-500: var(--color-primary-500);
  --theme-600: var(--color-primary-600);
  --theme-700: var(--color-primary-700);
  --theme-800: var(--color-primary-800);
  --theme-900: var(--color-primary-900);
  --theme-950: var(--color-primary-950);
}

.theme-student {
  --theme-50: var(--color-student-50);
  --theme-100: var(--color-student-100);
  --theme-200: var(--color-student-200);
  --theme-300: var(--color-student-300);
  --theme-400: var(--color-student-400);
  --theme-500: var(--color-student-500);
  --theme-600: var(--color-student-600);
  --theme-700: var(--color-student-700);
  --theme-800: var(--color-student-800);
  --theme-900: var(--color-student-900);
  --theme-950: var(--color-student-950);
}

.theme-teacher {
  --theme-50: var(--color-teacher-50);
  --theme-100: var(--color-teacher-100);
  --theme-200: var(--color-teacher-200);
  --theme-300: var(--color-teacher-300);
  --theme-400: var(--color-teacher-400);
  --theme-500: var(--color-teacher-500);
  --theme-600: var(--color-teacher-600);
  --theme-700: var(--color-teacher-700);
  --theme-800: var(--color-teacher-800);
  --theme-900: var(--color-teacher-900);
  --theme-950: var(--color-teacher-950);
}

.theme-admin {
  --theme-50: var(--color-admin-50);
  --theme-100: var(--color-admin-100);
  --theme-200: var(--color-admin-200);
  --theme-300: var(--color-admin-300);
  --theme-400: var(--color-admin-400);
  --theme-500: var(--color-admin-500);
  --theme-600: var(--color-admin-600);
  --theme-700: var(--color-admin-700);
  --theme-800: var(--color-admin-800);
  --theme-900: var(--color-admin-900);
  --theme-950: var(--color-admin-950);
}

/* Utility Classes */
.bg-theme-50 { background-color: var(--theme-50); }
.bg-theme-100 { background-color: var(--theme-100); }
.bg-theme-200 { background-color: var(--theme-200); }
.bg-theme-300 { background-color: var(--theme-300); }
.bg-theme-400 { background-color: var(--theme-400); }
.bg-theme-500 { background-color: var(--theme-500); }
.bg-theme-600 { background-color: var(--theme-600); }
.bg-theme-700 { background-color: var(--theme-700); }
.bg-theme-800 { background-color: var(--theme-800); }
.bg-theme-900 { background-color: var(--theme-900); }
.bg-theme-950 { background-color: var(--theme-950); }

.text-theme-50 { color: var(--theme-50); }
.text-theme-100 { color: var(--theme-100); }
.text-theme-200 { color: var(--theme-200); }
.text-theme-300 { color: var(--theme-300); }
.text-theme-400 { color: var(--theme-400); }
.text-theme-500 { color: var(--theme-500); }
.text-theme-600 { color: var(--theme-600); }
.text-theme-700 { color: var(--theme-700); }
.text-theme-800 { color: var(--theme-800); }
.text-theme-900 { color: var(--theme-900); }
.text-theme-950 { color: var(--theme-950); }

.border-theme-200 { border-color: var(--theme-200); }
.border-theme-300 { border-color: var(--theme-300); }
.border-theme-400 { border-color: var(--theme-400); }
.border-theme-500 { border-color: var(--theme-500); }
.border-theme-600 { border-color: var(--theme-600); }
.border-theme-700 { border-color: var(--theme-700); }
.border-theme-800 { border-color: var(--theme-800); }

/* Animation Classes */
.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
