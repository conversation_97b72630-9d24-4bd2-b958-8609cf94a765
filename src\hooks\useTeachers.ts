import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  teacherService,
  Teacher,
  TeacherWithAssignments,
  TeacherFormData,
  TeacherFormWithAssignments
} from '@/lib/services/teacherService';
import toast from 'react-hot-toast';

// Fetch all teachers
export const useTeachers = () => {
  return useQuery({
    queryKey: ['teachers'],
    queryFn: () => teacherService.getAllTeachers(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Fetch all teachers with their assignments
export const useTeachersWithAssignments = () => {
  return useQuery({
    queryKey: ['teachers', 'with-assignments'],
    queryFn: () => teacherService.getTeachersWithAssignments(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Fetch teacher by ID
export const useTeacher = (id: string) => {
  return useQuery({
    queryKey: ['teachers', id],
    queryFn: () => teacherService.getTeacherById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

// Fetch teachers by grade ID
export const useTeachersByGrade = (gradeId: string) => {
  return useQuery({
    queryKey: ['teachers', 'grade', gradeId],
    queryFn: () => gradeId ? teacherService.getTeachersByGrade(gradeId) : [],
    enabled: !!gradeId,
    staleTime: 5 * 60 * 1000,
  });
};

// Fetch teachers by section ID
export const useTeachersBySection = (sectionId: string) => {
  return useQuery({
    queryKey: ['teachers', 'section', sectionId],
    queryFn: () => sectionId ? teacherService.getTeachersBySection(sectionId) : [],
    enabled: !!sectionId,
    staleTime: 5 * 60 * 1000,
  });
};

// Add new teacher (basic)
export const useAddTeacher = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (teacherData: TeacherFormData) => teacherService.createTeacher(teacherData),
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
      queryClient.invalidateQueries({ queryKey: ['grades'] });

      toast.success('Teacher registered successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to register teacher');
    },
  });
};

// Add new teacher with assignments
export const useAddTeacherWithAssignments = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (teacherData: TeacherFormWithAssignments) =>
      teacherService.createTeacherWithAssignments(teacherData),
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      queryClient.invalidateQueries({ queryKey: ['teacher_subject_assignments'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
      queryClient.invalidateQueries({ queryKey: ['grades'] });

      toast.success('Teacher registered with assignments successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to register teacher with assignments');
    },
  });
};

// Update teacher
export const useUpdateTeacher = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<TeacherFormData> }): Promise<void> => {
      await teacherService.updateTeacher(id, updates);
    },
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
      queryClient.invalidateQueries({ queryKey: ['grades'] });

      toast.success('Teacher updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update teacher');
    },
  });
};

// Delete teacher
export const useDeleteTeacher = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (teacherId: string) => teacherService.deleteTeacher(teacherId),
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
      queryClient.invalidateQueries({ queryKey: ['grades'] });

      toast.success('Teacher deleted successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete teacher');
    },
  });
};

// Add exam to teacher
export const useAddExamToTeacher = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ teacherId, examId }: { teacherId: string; examId: string }): Promise<void> => {
      await teacherService.addExamToTeacher(teacherId, examId);
    },
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      queryClient.invalidateQueries({ queryKey: ['exams'] });

      toast.success('Exam assigned to teacher successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to assign exam to teacher');
    },
  });
};

// Remove exam from teacher
export const useRemoveExamFromTeacher = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ teacherId, examId }: { teacherId: string; examId: string }): Promise<void> => {
      await teacherService.removeExamFromTeacher(teacherId, examId);
    },
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      queryClient.invalidateQueries({ queryKey: ['exams'] });

      toast.success('Exam removed from teacher successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to remove exam from teacher');
    },
  });
};


