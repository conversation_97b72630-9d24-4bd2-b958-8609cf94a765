import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { studentService, Student } from '@/lib/services/studentService';
import { StudentFormData } from '@/lib/schemas';
import toast from 'react-hot-toast';
import {
  collection,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  query,
  where,
  orderBy
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Fetch all students
export const useStudents = () => {
  return useQuery({
    queryKey: ['students'],
    queryFn: () => studentService.getAllStudents(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Fetch students by grade ID
export const useStudentsByGrade = (gradeId: string) => {
  return useQuery({
    queryKey: ['students', 'grade', gradeId],
    queryFn: () => gradeId ? studentService.getStudentsByGrade(gradeId) : [],
    enabled: !!gradeId,
    staleTime: 5 * 60 * 1000,
  });
};

// Fetch students by section ID
export const useStudentsBySection = (sectionId: string) => {
  return useQuery({
    queryKey: ['students', 'section', sectionId],
    queryFn: () => sectionId ? studentService.getStudentsBySection(sectionId) : [],
    enabled: !!sectionId,
    staleTime: 5 * 60 * 1000,
  });
};

// Add new student
export const useAddStudent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (studentData: StudentFormData) => studentService.createStudent(studentData),
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['students'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
      queryClient.invalidateQueries({ queryKey: ['grades'] });

      toast.success('Student registered successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to register student');
    },
  });
};

// Update student
export const useUpdateStudent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ lrn, updates }: { lrn: string; updates: Partial<StudentFormData> }): Promise<void> => {
      // Find student by LRN first
      const student = await studentService.getStudentByLRN(lrn);
      if (!student) {
        throw new Error('Student not found');
      }

      await studentService.updateStudent(student.id, updates);
    },
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['students'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
      queryClient.invalidateQueries({ queryKey: ['grades'] });

      toast.success('Student updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update student');
    },
  });
};

// Delete student
export const useDeleteStudent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (studentId: string) => studentService.deleteStudent(studentId),
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['students'] });
      queryClient.invalidateQueries({ queryKey: ['sections'] });
      queryClient.invalidateQueries({ queryKey: ['grades'] });

      toast.success('Student deleted successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete student');
    },
  });
};

// Generate QR code for student
export const useGenerateStudentQR = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (lrn: string): Promise<void> => {
      // Find student by LRN first
      const student = await studentService.getStudentByLRN(lrn);
      if (!student) {
        throw new Error('Student not found');
      }

      // Generate actual QR code with hash
      await studentService.generateStudentQR(student.id);
    },
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['students'] });

      toast.success('QR Code generated successfully!');
    },
    onError: (error: any) => {
      console.error('QR Generation Error:', error);
      toast.error(error.message || 'Failed to generate QR code');
    },
  });
};
