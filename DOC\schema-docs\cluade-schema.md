# Philippine K-12 Exam System Database Schema (Grades 4-6)

## Core Collections

### 1. admins
```php
[
    '_id' => ObjectId,
    'email' => '<EMAIL>',
    'password' => 'hashed_password',
    'first_name' => 'Maria',
    'last_name' => '<PERSON>',
    'role' => 'principal', // principal, admin
    'employee_id' => 'P2024001',
    'status' => 'active', // active, inactive
    'created_at' => DateTime,
    'updated_at' => DateTime
]
```

### 2. grade_levels
```php
[
    '_id' => ObjectId,
    'level' => 4, // 4, 5, or 6
    'name' => 'Grade 4',
    'created_at' => DateTime,
    'updated_at' => DateTime
]
```

### 3. sections
```php
[
    '_id' => ObjectId,
    'name' => 'Rizal',
    'grade_level_id' => ObjectId, // References grade_levels
    'created_at' => DateTime,
    'updated_at' => DateTime
]
```

### 4. subjects
```php
[
    '_id' => ObjectId,
    'name' => 'English',
    'code' => 'ENG', // Optional subject code
    'grade_level_id' => ObjectId, // References grade_levels
    'created_at' => DateTime,
    'updated_at' => DateTime
]
```

### 5. teachers
```php
[
    '_id' => ObjectId,
    'email' => '<EMAIL>',
    'password' => 'hashed_password',
    'first_name' => 'Juan',
    'last_name' => 'Cruz',
    'employee_id' => 'T2024001',
    'status' => 'active', // active, inactive
    'created_at' => DateTime,
    'updated_at' => DateTime
]
```

### 6. students
```php
[
    '_id' => ObjectId,
    'student_id' => 'S2024001',
    'first_name' => 'Maria',
    'last_name' => 'Santos',
    'qr_code' => 'unique_qr_string',
    'section_id' => ObjectId, // References sections
    'status' => 'active', // active, inactive
    'created_at' => DateTime,
    'updated_at' => DateTime
]
```

## Assignment Collections (Many-to-Many Relationships)

### 7. teacher_subject_assignments
```php
[
    '_id' => ObjectId,
    'teacher_id' => ObjectId, // References teachers
    'subject_id' => ObjectId, // References subjects
    'section_id' => ObjectId, // References sections
    'school_year' => '2024-2025',
    'created_at' => DateTime,
    'updated_at' => DateTime
]
```

### 8. student_enrollments
```php
[
    '_id' => ObjectId,
    'student_id' => ObjectId, // References students
    'subject_id' => ObjectId, // References subjects
    'section_id' => ObjectId, // References sections
    'school_year' => '2024-2025',
    'enrollment_date' => DateTime,
    'status' => 'enrolled' // enrolled, dropped
]
```

## Exam and Assessment Collections

### 9. exams
```php
[
    '_id' => ObjectId,
    'title' => 'Quarter 1 English Exam',
    'description' => 'Comprehensive exam covering lessons 1-5',
    'subject_id' => ObjectId, // References subjects
    'section_id' => ObjectId, // References sections
    'teacher_id' => ObjectId, // References teachers (creator)
    'type' => 'exam', // exam, quiz
    'status' => 'pending_approval', // draft, pending_approval, approved, rejected, published
    'approval_status' => [
        'approved_by' => ObjectId, // Admin/Principal ID
        'approved_at' => DateTime,
        'rejection_reason' => 'string' // if rejected
    ],
    'schedule' => [
        'start_date' => DateTime,
        'end_date' => DateTime,
        'duration_minutes' => 120
    ],
    'settings' => [
        'shuffle_questions' => true,
        'show_results_immediately' => true,
        'allow_review' => true,
        'prevent_tab_switching' => true
    ],
    'total_points' => 100,
    'created_at' => DateTime,
    'updated_at' => DateTime
]
```

### 10. exam_questions
```php
[
    '_id' => ObjectId,
    'exam_id' => ObjectId, // References exams
    'question_text' => 'What is the capital of the Philippines?',
    'question_type' => 'multiple_choice', // multiple_choice, true_false, essay, fill_blank
    'thinking_level' => 'LOTS', // HOTS, LOTS
    'points' => 5,
    'order' => 1,
    'options' => [ // For multiple choice
        'A' => 'Manila',
        'B' => 'Cebu',
        'C' => 'Davao',
        'D' => 'Iloilo'
    ],
    'correct_answer' => 'A', // For objective questions
    'rubric' => 'Essay grading criteria...', // For essay questions
    'created_at' => DateTime,
    'updated_at' => DateTime
]
```

### 11. exam_attempts
```php
[
    '_id' => ObjectId,
    'exam_id' => ObjectId, // References exams
    'student_id' => ObjectId, // References students
    'status' => 'completed', // in_progress, completed, submitted
    'started_at' => DateTime,
    'submitted_at' => DateTime,
    'total_score' => 85,
    'total_possible' => 100,
    'percentage' => 85.0,
    'auto_saved' => true,
    'tab_violations' => 2, // Count of tab switches detected
    'created_at' => DateTime,
    'updated_at' => DateTime
]
```

### 12. exam_answers
```php
[
    '_id' => ObjectId,
    'attempt_id' => ObjectId, // References exam_attempts
    'question_id' => ObjectId, // References exam_questions
    'student_answer' => 'A', // Student's answer
    'is_correct' => true, // For objective questions
    'points_earned' => 5,
    'teacher_feedback' => 'Good analysis', // For manually graded questions
    'graded_by' => ObjectId, // Teacher ID for manual grading
    'graded_at' => DateTime,
    'auto_saved_at' => DateTime, // Last auto-save timestamp
    'created_at' => DateTime,
    'updated_at' => DateTime
]
```

### 13. student_feedback
```php
[
    '_id' => ObjectId,
    'student_id' => ObjectId, // References students
    'exam_id' => ObjectId, // References exams
    'attempt_id' => ObjectId, // References exam_attempts
    'feedback_text' => 'I think question 5 was confusing...',
    'system_response' => 'Your detailed score breakdown: ...',
    'created_at' => DateTime,
    'responded_at' => DateTime
]
```

## User Roles and Access Levels

### Admin/Principal Panel
- Create and manage student accounts (with QR codes)
- Create and manage teacher accounts (with email/password)
- Review and approve/reject exam questionnaires
- Monitor all user activity and system management
- Access all reports and analytics

### Teacher Panel
- Create exams using HOTS/LOTS questions
- Enter correct answers for automatic grading
- Submit exams for principal approval
- Monitor student performance and progress
- Manually grade essay-type questions
- View assigned subjects and sections

### Student Panel
- Login via QR code scanning
- View enrolled subjects dashboard
- Take available exams and quizzes
- Review answers before submission
- View results and correct answers immediately
- Submit feedback on performance

## Key Design Decisions and Rationale

### 1. **Subject Assignment Approach**
The **teacher_subject_assignments** collection approach over storing subjects in teacher documents because:
- **Flexibility**: Teachers can be assigned/unassigned from subjects easily
- **Historical Data**: Track assignment changes over school years
- **Complex Queries**: Easier to find all teachers for a subject or all subjects for a teacher
- **Section-Level Assignment**: Teachers are assigned to specific subject-section combinations

### 2. **Grade Level Structure**
- Subjects belong to grade levels (not stored as arrays in grade levels)
- Sections belong to grade levels
- Proper normalization and easier querying

### 3. **Student Enrollment**
- Students are assigned to sections, and enrolled in subjects within those sections
- Reflects the Philippine K-12 system where students in a section study the same subjects

### 4. **Exam Management**
- Exams are created per subject-section combination
- Clear approval workflow with status tracking
- Questions are stored separately for better management and reusability

### 5. **Answer Management**
- Separate collection for answers allows auto-saving functionality
- Tracks both objective and subjective question responses
- Maintains audit trail for manual grading

## Sample Data Flow

```php
// Example: Grade 4, Section Rizal, English subject
Grade Level: "Grade 4" (ID: 1)
├── Section: "Rizal" (grade_level_id: 1)
├── Subject: "English" (grade_level_id: 1)
├── Teacher Assignment: Teacher "Juan Cruz" → English → Rizal section
├── Student Enrollment: Student "Maria Santos" → Rizal section → English subject
└── Exam: "Quarter 1 English Exam" → English subject → Rizal section
```

## Philippine K-12 Context Implementation

### Grade 4 Example Structure
```php
// Sections: Rizal, Bonifacio, Aguinaldo, Luna
// Subjects: English, Math, Science, Filipino, ESP
// Teachers can handle multiple sections per subject
```

### Grade 5 Example Structure
```php
// Sections: Bulacan, Pampanga, Bataan, Zambales
// Subjects: English, Math, Science, Araling Panlipunan
// Different teacher assignments from Grade 4
```

### Grade 6 Example Structure
```php
// Similar pattern with different sections and subjects
// Maintains academic independence per teacher
// All exams require principal approval
```

## System Features Implementation

### Auto-Save Functionality
- `exam_answers.auto_saved_at` tracks last save
- Periodic saves during exam attempts
- Recovery mechanism for interrupted sessions

### Tab Switching Detection
- `exam_attempts.tab_violations` counter
- Real-time monitoring during exams
- Configurable violation thresholds

### Approval Workflow
- Exam status progression: draft → pending_approval → approved/rejected → published
- Principal review and feedback mechanism
- Revision tracking for rejected exams

### Feedback System
- Student feedback on exam performance
- Automated system responses with score breakdowns
- Teacher access to student feedback for improvement

## Benefits of This Schema

1. **Normalized Data**: Reduces redundancy and maintains data integrity
2. **Flexible Assignments**: Easy to reassign teachers or modify subject offerings
3. **Scalable**: Can easily add more grades, subjects, or sections
4. **Audit Trail**: Complete tracking of assignments, enrollments, and exam activities
5. **Performance**: Optimized for common queries (student dashboard, teacher workload, etc.)
6. **Philippine K-12 Compliance**: Matches the actual school structure and workflow
7. **Role-Based Access**: Clear separation of admin, teacher, and student functionalities

This schema provides a solid foundation for your exam system while maintaining flexibility for future enhancements and strict adherence to the Philippine K-12 educational structure.