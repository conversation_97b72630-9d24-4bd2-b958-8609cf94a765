import { NextRequest, NextResponse } from 'next/server';
import { db } from '../../../lib/firebase';
import { collection, doc, setDoc, getDocs, query, where } from 'firebase/firestore';

// Teacher seed data
const teacherSeeds = [
  {
    id: 'teacher1',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    middleName: 'Cruz',
    employeeId: 'EMP001',
    department: 'Mathematics',
    position: 'Senior Teacher',
    status: 'active',
    subjects: ['Mathematics', 'Algebra', 'Geometry'],
    gradeLevel: ['Grade 5', 'Grade 6'],
    phoneNumber: '+63 ************',
    address: 'Pinagbarilan, Baguio City',
    dateHired: '2020-06-15',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'teacher2',
    email: '<EMAIL>',
    firstName: 'Juan',
    lastName: '<PERSON><PERSON> Cruz',
    middleName: '<PERSON>',
    employeeId: 'EMP002',
    department: 'Science',
    position: 'Teacher II',
    status: 'active',
    subjects: ['Science', 'Biology', 'Chemistry'],
    gradeLevel: ['Grade 4', 'Grade 5'],
    phoneNumber: '+63 ************',
    address: 'Pinagbarilan, Baguio City',
    dateHired: '2021-08-20',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'teacher3',
    email: '<EMAIL>',
    firstName: 'Ana',
    lastName: 'Garcia',
    middleName: 'Lopez',
    employeeId: 'EMP003',
    department: 'English',
    position: 'Teacher I',
    status: 'active',
    subjects: ['English', 'Literature', 'Grammar'],
    gradeLevel: ['Grade 1', 'Grade 2', 'Grade 3'],
    phoneNumber: '+63 ************',
    address: 'Pinagbarilan, Baguio City',
    dateHired: '2022-01-10',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Admin seed data
const adminSeeds = [
  {
    id: 'admin1',
    email: '<EMAIL>',
    firstName: 'Roberto',
    lastName: 'Mendoza',
    middleName: 'Torres',
    employeeId: 'ADM001',
    position: 'School Principal',
    department: 'Administration',
    status: 'active',
    role: 'principal',
    permissions: ['user_management', 'exam_approval', 'system_settings', 'reports'],
    phoneNumber: '+63 ************',
    address: 'Pinagbarilan, Baguio City',
    dateHired: '2018-03-01',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'admin2',
    email: '<EMAIL>',
    firstName: 'Carmen',
    lastName: 'Villanueva',
    middleName: 'Ramos',
    employeeId: 'ADM002',
    position: 'Assistant Principal',
    department: 'Administration',
    status: 'active',
    role: 'assistant_principal',
    permissions: ['user_management', 'exam_approval', 'reports'],
    phoneNumber: '+63 ************',
    address: 'Pinagbarilan, Baguio City',
    dateHired: '2019-07-15',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Student seed data - Updated with actual grade and section IDs from database
const studentSeeds = [
  {
    id: 'student1',
    lrn: '129848291239',
    firstName: 'Jemmiah',
    lastName: 'Cayetano',
    middleName: '',
    extension: '',
    gradeId: 'GP1teEsjsjW3smrdCDFy', // Grade 4
    sectionId: 'yVoiUPc2NnL47V8YAzEg', // Grade 4 - Section B
    status: 'active',
    enrollmentDate: '2024-08-15',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'student2',
    lrn: '123456789013',
    firstName: 'Andres',
    lastName: 'Bonifacio',
    middleName: 'De Castro',
    extension: '',
    gradeId: 'I156fMwTdlWQPd7xIGJo', // Grade 6
    sectionId: 'LO0KyUMmFKX0NveHw1xu', // Grade 6 - Section A
    status: 'active',
    enrollmentDate: '2024-08-15',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'student3',
    lrn: '123456789014',
    firstName: 'Maria',
    lastName: 'Clara',
    middleName: 'Santos',
    extension: '',
    gradeId: 'voPxLDpaEnHBewbCfauD', // Grade 5
    sectionId: 'eBpLSlUEdbpLzSwGoXoc', // Grade 5 - Section A
    status: 'active',
    enrollmentDate: '2024-08-15',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'student4',
    lrn: '123456789015',
    firstName: 'Juan',
    lastName: 'Luna',
    middleName: 'Novicio',
    extension: '',
    gradeId: 'GP1teEsjsjW3smrdCDFy', // Grade 4
    sectionId: 'BD6jmHpeGYk2EvDyegiH', // Grade 4 - Section A
    status: 'active',
    enrollmentDate: '2024-08-15',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'student5',
    lrn: '123456789016',
    firstName: 'Gabriela',
    lastName: 'Silang',
    middleName: 'Cariño',
    extension: '',
    gradeId: 'voPxLDpaEnHBewbCfauD', // Grade 5
    sectionId: 'hBGVw0TVrAJGQGU5fdcL', // Grade 5 - Section B
    status: 'active',
    enrollmentDate: '2024-08-15',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

async function seedTeachers() {
  const results = [];
  
  for (const teacher of teacherSeeds) {
    try {
      // Check if teacher already exists
      const teacherQuery = query(collection(db, 'teachers'), where('email', '==', teacher.email));
      const existingTeacher = await getDocs(teacherQuery);
      
      if (!existingTeacher.empty) {
        results.push({ type: 'teacher', email: teacher.email, status: 'already_exists' });
        continue;
      }

      await setDoc(doc(db, 'teachers', teacher.id), teacher);
      results.push({ type: 'teacher', email: teacher.email, status: 'created' });
    } catch (error) {
      results.push({ type: 'teacher', email: teacher.email, status: 'error', error: error instanceof Error ? error.message : 'Unknown error' });
    }
  }
  
  return results;
}

async function seedAdmins() {
  const results = [];
  
  for (const admin of adminSeeds) {
    try {
      // Check if admin already exists
      const adminQuery = query(collection(db, 'admins'), where('email', '==', admin.email));
      const existingAdmin = await getDocs(adminQuery);
      
      if (!existingAdmin.empty) {
        results.push({ type: 'admin', email: admin.email, status: 'already_exists' });
        continue;
      }

      await setDoc(doc(db, 'admins', admin.id), admin);
      results.push({ type: 'admin', email: admin.email, status: 'created' });
    } catch (error) {
      results.push({ type: 'admin', email: admin.email, status: 'error', error: error instanceof Error ? error.message : 'Unknown error' });
    }
  }
  
  return results;
}

async function seedStudents() {
  const results = [];
  
  for (const student of studentSeeds) {
    try {
      // Check if student already exists
      const studentQuery = query(collection(db, 'students'), where('lrn', '==', student.lrn));
      const existingStudent = await getDocs(studentQuery);
      
      if (!existingStudent.empty) {
        results.push({ type: 'student', lrn: student.lrn, status: 'already_exists' });
        continue;
      }

      await setDoc(doc(db, 'students', student.id), student);
      results.push({ type: 'student', lrn: student.lrn, status: 'created' });
    } catch (error) {
      results.push({ type: 'student', lrn: student.lrn, status: 'error', error: error instanceof Error ? error.message : 'Unknown error' });
    }
  }
  
  return results;
}

export async function POST(request: NextRequest) {
  try {
    const teacherResults = await seedTeachers();
    const adminResults = await seedAdmins();
    const studentResults = await seedStudents();

    const allResults = [...teacherResults, ...adminResults, ...studentResults];
    
    return NextResponse.json({
      success: true,
      message: 'User seeding completed',
      results: allResults,
      summary: {
        teachers: {
          created: teacherResults.filter(r => r.status === 'created').length,
          existing: teacherResults.filter(r => r.status === 'already_exists').length,
          errors: teacherResults.filter(r => r.status === 'error').length
        },
        admins: {
          created: adminResults.filter(r => r.status === 'created').length,
          existing: adminResults.filter(r => r.status === 'already_exists').length,
          errors: adminResults.filter(r => r.status === 'error').length
        },
        students: {
          created: studentResults.filter(r => r.status === 'created').length,
          existing: studentResults.filter(r => r.status === 'already_exists').length,
          errors: studentResults.filter(r => r.status === 'error').length
        }
      },
      testAccounts: {
        teachers: teacherSeeds.map(t => ({ email: t.email, password: 'Teacher123!' })),
        admins: adminSeeds.map(a => ({ email: a.email, password: 'Admin123!' }))
      }
    });
  } catch (error) {
    console.error('Seeding error:', error);
    return NextResponse.json({
      success: false,
      message: 'Error during seeding',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'User Seeder API',
    usage: 'Send a POST request to this endpoint to seed users',
    testAccounts: {
      teachers: [
        '<EMAIL> / Teacher123!',
        '<EMAIL> / Teacher123!',
        '<EMAIL> / Teacher123!'
      ],
      admins: [
        '<EMAIL> / Admin123!',
        '<EMAIL> / Principal123!'
      ]
    }
  });
}
