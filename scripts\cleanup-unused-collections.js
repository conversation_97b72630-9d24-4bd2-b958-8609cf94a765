const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
try {
  const serviceAccount = require('../serviceAccountKey.json');
  
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: 'greentech-assessment'
  });

  console.log('✅ Firebase Admin initialized successfully');
} catch (error) {
  console.error('❌ Error initializing Firebase:', error.message);
  process.exit(1);
}

const db = admin.firestore();

async function deleteUnusedCollections() {
  try {
    console.log('🗑️ Cleaning up unused collections...\n');
    
    // Delete the unused teacherAssignments collection
    const collectionRef = db.collection('teacherAssignments');
    const snapshot = await collectionRef.get();
    
    if (snapshot.empty) {
      console.log('✅ teacherAssignments collection is already empty or doesn\'t exist');
      return;
    }
    
    console.log(`📋 Found ${snapshot.size} documents in teacherAssignments collection`);
    console.log('🗑️ Deleting all documents...');
    
    // Delete documents in batches
    const batch = db.batch();
    snapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    console.log('✅ Successfully deleted all documents from teacherAssignments collection');
    
    console.log('\n📊 Cleanup Summary:');
    console.log('   ✅ Kept: teacher_subject_assignments (7 documents) - ACTIVE');
    console.log('   ❌ Deleted: teacherAssignments (24 documents) - UNUSED');
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    throw error;
  }
}

// Run the cleanup
deleteUnusedCollections()
  .then(() => {
    console.log('\n🎉 Cleanup completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Cleanup failed:', error);
    process.exit(1);
  });
