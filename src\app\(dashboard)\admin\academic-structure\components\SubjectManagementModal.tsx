"use client";
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Modal } from '@/components/ui/Modal';

import { useUpdateSubject, useDeleteSubject } from '@/hooks/useSubjects';
import SubjectCreateForm from './SubjectCreateForm';

interface SubjectManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'create' | 'edit' | 'delete';
  subject?: any;
  gradeId?: string;
}

const SUBJECT_COLORS = [
  '#84a98c', // Default green
  '#3b82f6', // Blue
  '#ef4444', // Red
  '#f59e0b', // Amber
  '#8b5cf6', // Purple
  '#06b6d4', // Cyan
  '#ec4899', // Pink
  '#10b981', // Emerald
];

export default function SubjectManagementModal({ 
  isOpen, 
  onClose, 
  mode, 
  subject, 
  gradeId 
}: SubjectManagementModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    gradeId: gradeId || '',
    description: '',
    color: '#84a98c',
    code: ''
  });

  const updateSubjectMutation = useUpdateSubject();
  const deleteSubjectMutation = useDeleteSubject();

  useEffect(() => {
    if (subject && mode === 'edit') {
      setFormData({
        name: subject.name || '',
        gradeId: subject.gradeId || '',
        description: subject.description || '',
        color: subject.color || '#84a98c',
        code: subject.code || ''
      });
    } else if (mode === 'create') {
      setFormData({
        name: '',
        gradeId: gradeId || '',
        description: '',
        color: '#84a98c',
        code: ''
      });
    }
  }, [subject, mode, gradeId]);



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (mode === 'edit' && subject) {
        await updateSubjectMutation.mutateAsync({
          id: subject.id,
          updates: {
            name: formData.name,
            description: formData.description,
            color: formData.color,
            code: formData.code
          }
        });
        onClose();
      }
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleDelete = async () => {
    if (subject) {
      try {
        await deleteSubjectMutation.mutateAsync(subject.id);
        onClose();
      } catch (error) {
        // Error handled by mutation
      }
    }
  };

  if (!isOpen) return null;

  const getTitle = () => {
    switch (mode) {
      case 'create': return 'Add New Subject';
      case 'edit': return 'Edit Subject';
      case 'delete': return 'Delete Subject';
      default: return 'Subject Management';
    }
  };

  const getDescription = () => {
    switch (mode) {
      case 'create': return 'Create a new subject for the selected grade level.';
      case 'edit': return 'Update the subject information.';
      case 'delete': return `Are you sure you want to delete ${subject?.name}? This action cannot be undone and will affect all teachers and exams associated with this subject.`;
      default: return '';
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={getTitle()}
      description={getDescription()}
      maxWidth="md"
    >

        {mode === 'delete' ? (
          /* Delete Confirmation */
          <div className="flex justify-end space-x-3">
            <Button
              onClick={onClose}
              variant="outlined"
              theme="neutral"
              size="sm"
            >
              Cancel
            </Button>
            <Button
              onClick={handleDelete}
              variant="filled"
              theme="error"
              size="sm"
              loading={deleteSubjectMutation.isPending}
            >
              Delete Subject
            </Button>
          </div>
        ) : mode === 'create' ? (
          /* Create Form */
          <SubjectCreateForm onClose={onClose} onSuccess={onClose} gradeId={gradeId} />
        ) : (
          /* Edit Form */
          <form onSubmit={handleSubmit} className="space-y-4">

            {/* Subject Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Subject Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border-2 border-gray-300 focus:outline-none focus:border-[#84a98c] text-gray-900 placeholder-gray-600 bg-white transition-colors"
                placeholder="e.g., Mathematics, Science, English"
                required
              />
            </div>

            {/* Subject Code (Auto-generated) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Subject Code
              </label>
              <input
                type="text"
                value={formData.code}
                onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
                className="w-full px-3 py-2 border-2 border-gray-300 focus:outline-none focus:border-[#84a98c] font-mono text-gray-900 placeholder-gray-600 bg-white transition-colors"
                placeholder="Auto-generated"
              />
              <p className="text-xs text-gray-500 mt-1">
                Auto-generated based on subject name and grade level
              </p>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border-2 border-gray-300 focus:outline-none focus:border-[#84a98c] text-gray-900 placeholder-gray-600 bg-white transition-colors resize-none"
                rows={3}
                placeholder="Brief description of the subject"
              />
            </div>

            {/* Color Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Color
              </label>
              <div className="flex space-x-2">
                {SUBJECT_COLORS.map(color => (
                  <button
                    key={color}
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, color }))}
                    className={`w-8 h-8 rounded-full border-2 transition-all cursor-pointer ${
                      formData.color === color
                        ? 'border-gray-800 scale-110'
                        : 'border-gray-300 hover:border-gray-500'
                    }`}
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                onClick={onClose}
                variant="outlined"
                theme="neutral"
                size="sm"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="filled"
                theme="primary"
                size="sm"
                loading={updateSubjectMutation.isPending}
              >
                Update Subject
              </Button>
            </div>
          </form>
        )}
    </Modal>
  );
}
