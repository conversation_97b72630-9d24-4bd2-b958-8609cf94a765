"use client";
import React from 'react';
import { BuildingLibraryIcon, AcademicCapIcon, UserGroupIcon, ClipboardDocumentListIcon } from "@heroicons/react/24/outline";

// Import hooks
import { useGradesWithSections } from '@/hooks/useGrades';
import { useSubjects } from '@/hooks/useSubjects';
import { useAcademicStats } from '@/hooks/useAcademicStats';
import { useTeachers } from '@/hooks/useTeachers';
import { useStudents } from '@/hooks/useStudents';

// Import components
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

const AdminDashboard: React.FC = () => {
  // Data fetching using hooks
  const { data: gradesWithSections = [], isLoading: gradesLoading } = useGradesWithSections();
  const { data: subjects = [], isLoading: subjectsLoading } = useSubjects();
  const { data: academicStats, isLoading: statsLoading } = useAcademicStats();
  const { data: teachers = [], isLoading: teachersLoading } = useTeachers();
  const { data: students = [], isLoading: studentsLoading } = useStudents();

  // Calculate statistics
  const totalSections = academicStats?.totalSections || gradesWithSections.reduce((total, grade) =>
    total + (grade.sections?.length || 0), 0
  );
  const totalSubjects = academicStats?.totalSubjects || subjects.length;
  const totalStudents = academicStats?.totalStudents || students.length;
  const totalTeachers = academicStats?.totalTeachers || teachers.length;
  const activeGrades = gradesWithSections.filter(grade => grade.isActive);

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-3xl font-bold text-[#1A2340] mb-2">Dashboard Overview</h2>
        <p className="text-gray-600">System statistics and recent activity</p>
      </div>

      {/* Academic Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
        <div className="bg-white border-2 border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <BuildingLibraryIcon className="w-8 h-8 text-[#84a98c]" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Grade Levels</h3>
              <p className="text-2xl font-bold text-[#84a98c]">
                {gradesLoading ? <LoadingSpinner size="md" /> : activeGrades.length}
              </p>
              <p className="text-sm text-gray-600">Active grades</p>
            </div>
          </div>
        </div>

        <div className="bg-white border-2 border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <BuildingLibraryIcon className="w-8 h-8 text-[#84a98c]" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Total Sections</h3>
              <p className="text-2xl font-bold text-[#84a98c]">
                {statsLoading || gradesLoading ? <LoadingSpinner size="md" /> : totalSections}
              </p>
              <p className="text-sm text-gray-600">Across all grades</p>
            </div>
          </div>
        </div>

        <div className="bg-white border-2 border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <ClipboardDocumentListIcon className="w-8 h-8 text-[#84a98c]" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Total Subjects</h3>
              <p className="text-2xl font-bold text-[#84a98c]">
                {statsLoading || subjectsLoading ? <LoadingSpinner size="md" /> : totalSubjects}
              </p>
              <p className="text-sm text-gray-600">K-12 curriculum</p>
            </div>
          </div>
        </div>

        <div className="bg-white border-2 border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <AcademicCapIcon className="w-8 h-8 text-[#84a98c]" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Total Students</h3>
              <p className="text-2xl font-bold text-[#84a98c]">
                {statsLoading || studentsLoading ? <LoadingSpinner size="md" /> : totalStudents}
              </p>
              <p className="text-sm text-gray-600">Enrolled students</p>
            </div>
          </div>
        </div>

        <div className="bg-white border-2 border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <UserGroupIcon className="w-8 h-8 text-[#84a98c]" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Total Teachers</h3>
              <p className="text-2xl font-bold text-[#84a98c]">
                {statsLoading || teachersLoading ? <LoadingSpinner size="md" /> : totalTeachers}
              </p>
              <p className="text-sm text-gray-600">Active teachers</p>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default AdminDashboard;
