import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminService, AdminProfile, AdminProfileUpdateData } from '../lib/services/adminService';
import { useAuth } from '../app/contexts/AuthContext';
import toast from 'react-hot-toast';

/**
 * Hook to get current admin profile
 */
export function useAdminProfile() {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: ['adminProfile', user?.email],
    queryFn: async () => {
      if (!user?.email) {
        throw new Error('No user email available');
      }
      return await adminService.getAdminByEmail(user.email);
    },
    enabled: !!user?.email,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });
}

/**
 * Hook to update admin profile
 */
export function useUpdateAdminProfile() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: AdminProfileUpdateData }) => {
      await adminService.updateAdminProfile(id, data);
    },
    onSuccess: () => {
      // Invalidate and refetch admin profile
      queryClient.invalidateQueries({ queryKey: ['adminProfile', user?.email] });
      toast.success('Profile updated successfully!');
    },
    onError: (error: any) => {
      console.error('Error updating admin profile:', error);
      toast.error('Failed to update profile. Please try again.');
    }
  });
}

/**
 * Hook to get admin display name
 */
export function useAdminDisplayName() {
  const { data: adminProfile, isLoading } = useAdminProfile();
  
  const displayName = adminProfile 
    ? adminService.getDisplayName(adminProfile)
    : null;
    
  return {
    displayName,
    isLoading,
    adminProfile
  };
}
