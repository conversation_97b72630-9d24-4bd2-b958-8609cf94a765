// School-related type definitions

export interface Grade {
  id: string;
  name: string;           // "Grade 4", "Grade 5", "Grade 6"
  level: number;          // 4, 5, 6
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Section {
  id: string;
  name: string;           // "A", "B", "C", "D", etc.
  gradeId: string;        // Reference to grade document
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Subject {
  id: string;
  name: string;           // "English", "Mathematics", etc.
  code: string;           // "ENG4", "MATH4", etc.
  gradeId: string;        // Reference to grade document
  description?: string;   // Subject description
  color?: string;         // Color for UI display
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface GradeWithSections extends Grade {
  sections: Section[];
}

// Form data types
export interface CreateGradeData {
  name: string;
  level: number;
  isActive?: boolean;
}

export interface CreateSectionData {
  name: string;
  gradeId: string;
  isActive?: boolean;
}

export interface CreateSubjectData {
  name: string;
  code: string;
  gradeId: string;
  description?: string;
  color?: string;
  isActive?: boolean;
}

// Query result types
export interface GradeSectionSummary {
  gradeId: string;
  gradeName: string;
  gradeLevel: number;
  sections: {
    id: string;
    name: string;
    isActive: boolean;
  }[];
}
