"use client";

import React from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useStudentsBySection } from '@/hooks/useStudents';
import { useGradeById } from '@/hooks/useGrades';
import { useSectionById } from '@/hooks/useSections';
import { useTeacherAssignmentsWithDetails } from '@/hooks/useTeacherProfile';
import { 
  UserGroupIcon, 
  AcademicCapIcon,
  PlusIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';

export default function SectionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const gradeId = params.gradeId as string;
  const sectionId = params.sectionId as string;

  const { data: students = [], isLoading: isLoadingStudents } = useStudentsBySection(sectionId);
  const { data: grade, isLoading: isLoadingGrade } = useGradeById(gradeId);
  const { data: section, isLoading: isLoadingSection } = useSectionById(sectionId);
  const { data: assignments = [], isLoading: isLoadingAssignments } = useTeacherAssignmentsWithDetails();

  // Filter assignments for this specific section
  const sectionAssignments = assignments.filter(
    assignment => assignment.sectionId === sectionId && assignment.gradeId === gradeId
  );

  const handleCreateExam = (subjectId: string, subjectName: string) => {
    // Navigate to exam creation with pre-filled section and subject
    router.push(`/teacher/exams/create?gradeId=${gradeId}&sectionId=${sectionId}&subjectId=${subjectId}&subjectName=${encodeURIComponent(subjectName)}`);
  };

  const isLoading = isLoadingStudents || isLoadingGrade || isLoadingSection || isLoadingAssignments;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#FAFAF6] flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-[#84a98c] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-[#84a98c] font-medium">Loading section details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-none flex items-center justify-center">
            <UserGroupIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {grade?.name || `Grade ${gradeId}`} - {section?.name || `Section ${sectionId}`}
            </h2>
            <p className="text-gray-600">
              {students.length} student{students.length !== 1 ? 's' : ''} enrolled
            </p>
          </div>
        </div>
        
        <Button
          variant="outlined"
          theme="neutral"
          onClick={() => router.back()}
        >
          ← Back to Dashboard
        </Button>
      </div>

      {/* Section Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-none flex items-center justify-center">
              <UserGroupIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Students</p>
              <p className="text-2xl font-bold text-gray-900">{students.length}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-none flex items-center justify-center">
              <AcademicCapIcon className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Subjects Teaching</p>
              <p className="text-2xl font-bold text-gray-900">{sectionAssignments.length}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-none flex items-center justify-center">
              <ClipboardDocumentListIcon className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Exams Created</p>
              <p className="text-2xl font-bold text-gray-900">0</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Subjects I Teach in This Section */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Subjects I Teach</h3>
        
        {sectionAssignments.length === 0 ? (
          <div className="text-center py-8">
            <AcademicCapIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No subjects assigned for this section</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sectionAssignments.map((assignment) => (
              <div key={assignment.id} className="bg-gray-50 border border-gray-200 p-4 rounded-none">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-gray-900">
                    {assignment.subjectName || assignment.subjectId}
                  </h4>
                  <AcademicCapIcon className="w-5 h-5 text-gray-400" />
                </div>
                
                <div className="space-y-2 mb-4">
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">School Year:</span> {assignment.schoolYear}
                  </p>
                </div>
                
                <Button
                  variant="filled"
                  theme="primary"
                  size="sm"
                  className="w-full"
                  onClick={() => handleCreateExam(assignment.subjectId, assignment.subjectName || assignment.subjectId)}
                >
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Create Exam
                </Button>
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* Students List */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Students in This Section</h3>
        
        {students.length === 0 ? (
          <div className="text-center py-12">
            <UserGroupIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-semibold text-gray-900 mb-2">No Students Enrolled</h4>
            <p className="text-gray-600">
              This section doesn't have any students enrolled yet.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Student Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Student ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Enrollment Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {students.map((student) => (
                  <tr key={student.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600">
                            {student.firstName.charAt(0)}{student.lastName.charAt(0)}
                          </span>
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">
                            {student.firstName} {student.middleName ? `${student.middleName} ` : ''}{student.lastName}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {student.studentId}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {student.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        student.status === 'active' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {student.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {student.enrollmentDate ? new Date(student.enrollmentDate).toLocaleDateString() : 'N/A'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>
    </div>
  );
}
