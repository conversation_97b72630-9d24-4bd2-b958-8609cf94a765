import { 
  collection, 
  doc, 
  getDoc, 
  updateDoc, 
  Timestamp 
} from 'firebase/firestore';
import { db } from '../firebase';

export interface SystemSettings {
  // Delete functionality toggles
  allowDeleteSections: boolean;
  allowDeleteSubjects: boolean;
  allowDeleteGrades: boolean;
  allowDeleteStudents: boolean;
  allowDeleteTeachers: boolean;
  allowDeleteExams: boolean;
  
  // System settings
  systemName: string;
  schoolName: string;
  schoolYear: string;
  
  // UI settings
  itemsPerPage: number;
  enableDarkMode: boolean;
  enableNotifications: boolean;
  
  // Exam settings
  defaultExamDuration: number; // minutes
  allowRetakeExams: boolean;
  showCorrectAnswers: boolean;
  
  // Security settings
  sessionTimeout: number; // minutes
  requirePasswordChange: boolean;
  enableTwoFactorAuth: boolean;
  
  // Backup settings
  autoBackup: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  
  // Metadata
  createdAt: Timestamp;
  updatedAt: Timestamp;
  version: string;
}

export interface UpdateSettingsData {
  [key: string]: any;
}

class SettingsService {
  private settingsDoc = doc(db, 'settings', 'system');

  // Get system settings
  async getSettings(): Promise<SystemSettings | null> {
    try {
      const docSnap = await getDoc(this.settingsDoc);
      
      if (docSnap.exists()) {
        return docSnap.data() as SystemSettings;
      } else {
        console.warn('Settings document does not exist');
        return null;
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      throw new Error('Failed to fetch settings');
    }
  }

  // Update settings
  async updateSettings(updates: UpdateSettingsData): Promise<void> {
    try {
      const updateData = {
        ...updates,
        updatedAt: Timestamp.now()
      };

      await updateDoc(this.settingsDoc, updateData);
    } catch (error) {
      console.error('Error updating settings:', error);
      throw new Error('Failed to update settings');
    }
  }

  // Update specific setting
  async updateSetting(key: string, value: any): Promise<void> {
    try {
      await this.updateSettings({ [key]: value });
    } catch (error) {
      console.error(`Error updating setting ${key}:`, error);
      throw error;
    }
  }

  // Get specific setting value
  async getSetting(key: string): Promise<any> {
    try {
      const settings = await this.getSettings();
      return settings ? settings[key as keyof SystemSettings] : null;
    } catch (error) {
      console.error(`Error getting setting ${key}:`, error);
      throw error;
    }
  }

  // Check if delete is allowed for entity type
  async isDeleteAllowed(entityType: 'sections' | 'subjects' | 'grades' | 'students' | 'teachers' | 'exams'): Promise<boolean> {
    try {
      const settings = await this.getSettings();
      if (!settings) return false;

      const settingKey = `allowDelete${entityType.charAt(0).toUpperCase() + entityType.slice(1, -1)}s` as keyof SystemSettings;
      return settings[settingKey] as boolean;
    } catch (error) {
      console.error(`Error checking delete permission for ${entityType}:`, error);
      return false;
    }
  }
}

export const settingsService = new SettingsService();
