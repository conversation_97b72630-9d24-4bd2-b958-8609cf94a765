import { useQuery } from '@tanstack/react-query';
import { getAcademicStats, getStudentCountBySection, getStudentCountByGrade, getSubjectCountByGrade } from '@/lib/services/academicStatsService';

// Query keys
export const academicStatsKeys = {
  all: ['academicStats'] as const,
  overview: () => [...academicStatsKeys.all, 'overview'] as const,
  studentsBySection: (sectionId: string) => [...academicStatsKeys.all, 'students', 'section', sectionId] as const,
  studentsByGrade: (gradeId: string) => [...academicStatsKeys.all, 'students', 'grade', gradeId] as const,
  subjectsByGrade: (gradeId: string) => [...academicStatsKeys.all, 'subjects', 'grade', gradeId] as const,
};

// Get comprehensive academic statistics
export function useAcademicStats() {
  return useQuery({
    queryKey: academicStatsKeys.overview(),
    queryFn: () => getAcademicStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
  });
}

// Get student count by section
export function useStudentCountBySection(sectionId: string) {
  return useQuery({
    queryKey: academicStatsKeys.studentsBySection(sectionId),
    queryFn: () => getStudentCountBySection(sectionId),
    enabled: !!sectionId,
    staleTime: 2 * 60 * 1000,
  });
}

// Get student count by grade
export function useStudentCountByGrade(gradeId: string) {
  return useQuery({
    queryKey: academicStatsKeys.studentsByGrade(gradeId),
    queryFn: () => getStudentCountByGrade(gradeId),
    enabled: !!gradeId,
    staleTime: 2 * 60 * 1000,
  });
}

// Get subject count by grade
export function useSubjectCountByGrade(gradeId: string) {
  return useQuery({
    queryKey: academicStatsKeys.subjectsByGrade(gradeId),
    queryFn: () => getSubjectCountByGrade(gradeId),
    enabled: !!gradeId,
    staleTime: 2 * 60 * 1000,
  });
}
