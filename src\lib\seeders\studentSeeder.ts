/**
 * Student Seeder
 * Creates realistic student data distributed across grades and sections
 * 
 * Structure: 25-30 students per section with realistic Filipino names
 */

import { db } from '@/lib/firebase';
import { collection, addDoc, getDocs, deleteDoc, writeBatch, doc, updateDoc } from 'firebase/firestore';
import { Grade, Section } from '@/lib/types/school';

// Realistic Filipino names for students
const filipinoFirstNames = {
  male: [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
  ],
  female: [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
  ]
};

const fi<PERSON><PERSON><PERSON><PERSON><PERSON> = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON><PERSON> <PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Perez', 'Gonzales', 'Rodriguez', 'Fernandez', 'Lopez',
  'Rivera', 'Flores', 'Gomez', 'Alvarez', 'Romero', 'Jimenez', 'Ruiz', 'Vargas', 'Castro', 'Ortega'
];

/**
 * Clear all students from the database
 */
export async function clearStudents() {
  console.log('🧹 Clearing existing students...');
  
  try {
    const studentsSnapshot = await getDocs(collection(db, 'students'));
    const batch = writeBatch(db);
    
    studentsSnapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });
    
    if (studentsSnapshot.docs.length > 0) {
      await batch.commit();
      console.log(`✅ Cleared ${studentsSnapshot.docs.length} students`);
    } else {
      console.log('ℹ️ No students to clear');
    }
  } catch (error) {
    console.error('❌ Error clearing students:', error);
    throw error;
  }
}

/**
 * Generate a random Filipino name
 */
function generateRandomName(): { firstName: string; lastName: string; gender: 'male' | 'female' } {
  const gender = Math.random() > 0.5 ? 'male' : 'female';
  const firstName = filipinoFirstNames[gender][Math.floor(Math.random() * filipinoFirstNames[gender].length)];
  const lastName = filipinoLastNames[Math.floor(Math.random() * filipinoLastNames.length)];
  
  return { firstName, lastName, gender };
}

/**
 * Generate a student ID
 */
function generateStudentId(gradeLevel: number, sectionName: string, studentNumber: number): string {
  const year = new Date().getFullYear();
  const paddedNumber = studentNumber.toString().padStart(3, '0');
  return `${year}-${gradeLevel}${sectionName}-${paddedNumber}`;
}

/**
 * Generate a random birthdate for the grade level
 */
function generateBirthdate(gradeLevel: number): string {
  const currentYear = new Date().getFullYear();
  const baseAge = gradeLevel + 5; // Grade 4 = ~9 years old, Grade 5 = ~10, Grade 6 = ~11
  const birthYear = currentYear - baseAge - Math.floor(Math.random() * 2); // ±1 year variation
  
  const month = Math.floor(Math.random() * 12) + 1;
  const day = Math.floor(Math.random() * 28) + 1; // Safe day range
  
  return `${birthYear}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
}

/**
 * Create students for all grades and sections
 */
export async function seedStudents(grades: Grade[], sections: Section[]): Promise<any[]> {
  console.log('👥 Creating students for all sections...');
  
  const students: any[] = [];
  const now = new Date().toISOString();
  
  try {
    for (const grade of grades) {
      const gradeSections = sections.filter(s => s.gradeId === grade.id);
      console.log(`   Creating students for ${grade.name} (${gradeSections.length} sections)...`);
      
      for (const section of gradeSections) {
        const studentsPerSection = 25 + Math.floor(Math.random() * 6); // 25-30 students per section
        console.log(`     Creating ${studentsPerSection} students for Section ${section.name}...`);
        
        for (let i = 1; i <= studentsPerSection; i++) {
          const { firstName, lastName, gender } = generateRandomName();
          const studentId = generateStudentId(grade.level, section.name, i);
          const birthdate = generateBirthdate(grade.level);
          
          const studentData = {
            studentId,
            firstName,
            lastName,
            gender,
            birthdate,
            gradeId: grade.id,
            sectionId: section.id,
            enrollmentDate: now,
            isActive: true,
            createdAt: now,
            updatedAt: now
          };
          
          const docRef = await addDoc(collection(db, 'students'), studentData);
          
          const student = {
            id: docRef.id,
            ...studentData
          };
          
          students.push(student);
        }
        
        console.log(`     ✅ Created ${studentsPerSection} students for ${grade.name} - Section ${section.name}`);
      }
    }
    
    console.log(`✅ Successfully created ${students.length} students`);
    return students;
    
  } catch (error) {
    console.error('❌ Error creating students:', error);
    throw error;
  }
}

/**
 * Get all students
 */
export async function getAllStudents(): Promise<any[]> {
  try {
    const studentsSnapshot = await getDocs(collection(db, 'students'));
    const students: any[] = [];
    
    studentsSnapshot.docs.forEach((doc) => {
      students.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    return students.sort((a, b) => `${a.lastName}, ${a.firstName}`.localeCompare(`${b.lastName}, ${b.firstName}`));
  } catch (error) {
    console.error('❌ Error getting students:', error);
    throw error;
  }
}

/**
 * Get students by grade ID
 */
export async function getStudentsByGrade(gradeId: string): Promise<any[]> {
  try {
    const studentsSnapshot = await getDocs(collection(db, 'students'));
    const students: any[] = [];
    
    studentsSnapshot.docs.forEach((doc) => {
      const data = doc.data();
      if (data.gradeId === gradeId) {
        students.push({
          id: doc.id,
          ...data
        });
      }
    });
    
    return students.sort((a, b) => `${a.lastName}, ${a.firstName}`.localeCompare(`${b.lastName}, ${b.firstName}`));
  } catch (error) {
    console.error('❌ Error getting students by grade:', error);
    throw error;
  }
}

/**
 * Get students by section ID
 */
export async function getStudentsBySection(sectionId: string): Promise<any[]> {
  try {
    const studentsSnapshot = await getDocs(collection(db, 'students'));
    const students: any[] = [];
    
    studentsSnapshot.docs.forEach((doc) => {
      const data = doc.data();
      if (data.sectionId === sectionId) {
        students.push({
          id: doc.id,
          ...data
        });
      }
    });
    
    return students.sort((a, b) => `${a.lastName}, ${a.firstName}`.localeCompare(`${b.lastName}, ${b.firstName}`));
  } catch (error) {
    console.error('❌ Error getting students by section:', error);
    throw error;
  }
}

/**
 * Create a single student
 */
export async function createStudent(studentData: any): Promise<any> {
  const now = new Date().toISOString();
  
  const data = {
    ...studentData,
    createdAt: now,
    updatedAt: now
  };
  
  try {
    const docRef = await addDoc(collection(db, 'students'), data);
    
    return {
      id: docRef.id,
      ...data
    };
  } catch (error) {
    console.error('❌ Error creating student:', error);
    throw error;
  }
}

/**
 * Update student
 */
export async function updateStudent(studentId: string, updates: any): Promise<void> {
  try {
    const studentRef = doc(db, 'students', studentId);
    await updateDoc(studentRef, {
      ...updates,
      updatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Error updating student:', error);
    throw error;
  }
}

/**
 * Delete student
 */
export async function deleteStudent(studentId: string): Promise<void> {
  try {
    await deleteDoc(doc(db, 'students', studentId));
  } catch (error) {
    console.error('❌ Error deleting student:', error);
    throw error;
  }
}
