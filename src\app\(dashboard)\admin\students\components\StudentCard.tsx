"use client";
import React from 'react';
import { QrCodeIcon, EyeIcon, PencilIcon, TrashIcon } from "@heroicons/react/24/outline";
import { Button } from '@/components/ui/Button';
import { StudentCardTemplate } from '@/components/cards';
import { useGradeAndSectionNames } from '@/hooks/useGrades';
import { useDeleteStudent, useGenerateStudentQR } from '@/hooks/useStudents';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface StudentCardProps {
  student: any;
  onView: () => void;
  onEdit: () => void;
  onViewQR: () => void;
}

export default function StudentCard({ student, onView, onEdit, onViewQR }: StudentCardProps) {
  const { getGradeName, getSectionName } = useGradeAndSectionNames();
  const deleteStudentMutation = useDeleteStudent();
  const generateQRMutation = useGenerateStudentQR();
  let firstName = '', middleName = '', lastName = '';
  const getStudentDisplayName = () => {
    if (student.fullName && Array.isArray(student.fullName)) {
        firstName = student.fullName[0] || '';
        middleName = student.fullName[1] || '';
        lastName = student.fullName[2] || '';
    }
    return 'this student';
  };

  const handleDelete = async () => {
    try {
      await deleteStudentMutation.mutateAsync(student.id);
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleGenerateQR = async () => {
    try {
      await generateQRMutation.mutateAsync(student.lrn);
    } catch (error) {
      // Error handled by mutation
    }
  };

  // Prepare grade and section display
  const gradeSection = student.gradeId && student.sectionId
    ? `${getGradeName(student.gradeId)} - ${getSectionName(student.sectionId)}`
    : undefined;

  // Prepare action buttons
  const actions = (
    <>
      <Button
        onClick={onView}
        variant="outlined"
        theme="primary"
        size="sm"
        radius="none"
        leftIcon={<EyeIcon className="w-4 h-4" />}
      >
        View
      </Button>

      <Button
        onClick={onEdit}
        variant="outlined"
        theme="primary"
        size="sm"
        radius="none"
        leftIcon={<PencilIcon className="w-4 h-4" />}
      >
        Edit
      </Button>

      {/* QR Code Actions */}
      {(student.qrUrl || student.qr_code) ? (
        <Button
          onClick={onViewQR}
          variant="filled"
          theme="primary"
          size="sm"
          radius="none"
          leftIcon={<QrCodeIcon className="w-4 h-4" />}
        >
          View QR
        </Button>
      ) : (
        <Button
          onClick={handleGenerateQR}
          variant="filled"
          theme="primary"
          size="sm"
          radius="none"
          loading={generateQRMutation.isPending}
          leftIcon={<QrCodeIcon className="w-4 h-4" />}
        >
          {generateQRMutation.isPending ? 'Generating...' : 'Generate QR'}
        </Button>
      )}

      {/* Delete Button */}
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button
            variant="outlined"
            theme="error"
            size="sm"
            radius="none"
            leftIcon={<TrashIcon className="w-4 h-4" />}
          >
            Delete
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Student</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {getStudentDisplayName()}?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <Button
              onClick={handleDelete}
              loading={deleteStudentMutation.isPending}
              variant="filled"
              theme="error"
              size="sm"
              radius="none"
            >
              {deleteStudentMutation.isPending ? 'Deleting...' : 'Delete'}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );

  return (
    <StudentCardTemplate
      student={student}
      gradeSection={gradeSection}
      actions={actions}
    />
  );
}
