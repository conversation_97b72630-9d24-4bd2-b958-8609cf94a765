import FeatureItem from './FeatureItem';

const FeatureShowcase: React.FC = () => {
  const features = [
    {
      title: "QR Code Login",
      description: "Secure instant access with mobile scanning technology",
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1">
          <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 013.75 9.375v-4.5zM3.75 14.625c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5a1.125 1.125 0 01-1.125-1.125v-4.5zM13.5 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0113.5 9.375v-4.5z" />
          <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 6.75h.75v.75h-.75v-.75zM6.75 16.5h.75v.75h-.75v-.75zM16.5 6.75h.75v.75h-.75v-.75zM13.5 13.5H15V15h-1.5v-1.5zM15 15h1.5v1.5H15V15zM13.5 15H15v1.5h-1.5V15zM16.5 13.5H18V15h-1.5v-1.5zM16.5 15H18v1.5h-1.5V15zM18 16.5h1.5V18H18v-1.5z" />
        </svg>
      )
    },
    {
      title: "Auto-Save Feature",
      description: "Real-time progress saving prevents data loss automatically",
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1">
          <path strokeLinecap="round" strokeLinejoin="round" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"/>
        </svg>
      )
    },
    {
      title: "Performance Tracking",
      description: "Detailed analytics and insights for continuous improvement",
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1">
          <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
        </svg>
      )
    },
    {
      title: "Anti-Cheating System",
      description: "Advanced monitoring ensures integrity during examination sessions",
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1">
          <path strokeLinecap="round" strokeLinejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
        </svg>
      )
    },
    {
      title: "Cloud Storage",
      description: "Secure backup and accessibility from anywhere worldwide",
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1">
          <path strokeLinecap="round" strokeLinejoin="round" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
        </svg>
      )
    },
    {
      title: "Live Monitoring",
      description: "Real-time supervision and support during exam periods",
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1">
          <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          <path strokeLinecap="round" strokeLinejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
        </svg>
      )
    }
  ];

  return (
    <div className="relative h-96 overflow-hidden bg-blush">
      {/* Fade blur overlays */}
      <div className="absolute top-0 left-0 right-0 h-20 fade-top z-10 pointer-events-none"></div>
      <div className="absolute bottom-0 left-0 right-0 h-20 fade-bottom z-10 pointer-events-none"></div>
      
      <div className="w-full h-full">
        {/* Single Elevator Column */}
        <div className="animate-scroll-down space-y-6 w-full hover:pause-animation">
          {/* Render features */}
          {features.map((feature, index) => (
            <FeatureItem 
              key={index} 
              icon={feature.icon} 
              title={feature.title} 
              description={feature.description} 
            />
          ))}
          
          {/* Duplicates for continuous scroll */}
          {features.map((feature, index) => (
            <FeatureItem 
              key={`duplicate-${index}`} 
              icon={feature.icon} 
              title={feature.title} 
              description={feature.description} 
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default FeatureShowcase; 