"use client";
import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { useGradesWithSections } from '@/hooks/useGrades';
import { useSubjects } from '@/hooks/useSubjects';
import { useAddTeacherWithAssignments } from '@/hooks/useTeachers';
import { TeacherFormWithAssignments, TeacherAssignmentFormData } from '@/lib/services/teacherService';

interface TeacherFormWithAssignmentsProps {
  onClose: () => void;
  onSuccess: () => void;
}

interface Assignment {
  id: number;
  gradeId: string;
  subjectId: string;
  sectionIds: string[];
}

export default function TeacherFormWithAssignmentsComponent({ 
  onClose, 
  onSuccess 
}: TeacherFormWithAssignmentsProps) {
  const [teacherForm, setTeacherForm] = useState<TeacherFormWithAssignments>({
    firstName: '',
    middleName: '',
    lastName: '',
    email: '',
    status: 'active',
    assignments: []
  });

  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [assignmentCounter, setAssignmentCounter] = useState(0);
  const [currentGrade, setCurrentGrade] = useState<string>('');

  const { data: gradesWithSections, isLoading: gradesLoading } = useGradesWithSections();
  const { data: subjects, isLoading: subjectsLoading } = useSubjects();
  const addTeacherMutation = useAddTeacherWithAssignments();

  // Get subjects for current grade
  const currentGradeSubjects = subjects?.filter(subject => 
    subject.gradeId === currentGrade
  ) || [];

  // Get sections for current grade
  const currentGradeSections = gradesWithSections?.find(grade => 
    grade.id === currentGrade
  )?.sections || [];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setTeacherForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const addAssignment = () => {
    if (!currentGrade) {
      alert('Please select a grade first');
      return;
    }

    const newAssignment: Assignment = {
      id: assignmentCounter + 1,
      gradeId: currentGrade,
      subjectId: '',
      sectionIds: []
    };

    setAssignments(prev => [...prev, newAssignment]);
    setAssignmentCounter(prev => prev + 1);
  };

  const removeAssignment = (id: number) => {
    setAssignments(prev => prev.filter(assignment => assignment.id !== id));
  };

  const updateAssignment = (id: number, field: keyof Assignment, value: any) => {
    setAssignments(prev => prev.map(assignment => 
      assignment.id === id 
        ? { ...assignment, [field]: value }
        : assignment
    ));
  };

  const toggleSection = (assignmentId: number, sectionId: string) => {
    setAssignments(prev => prev.map(assignment => {
      if (assignment.id === assignmentId) {
        const sectionIds = assignment.sectionIds.includes(sectionId)
          ? assignment.sectionIds.filter(id => id !== sectionId)
          : [...assignment.sectionIds, sectionId];
        return { ...assignment, sectionIds };
      }
      return assignment;
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate assignments
    const validAssignments = assignments.filter(assignment => 
      assignment.subjectId && assignment.sectionIds.length > 0
    );

    if (validAssignments.length === 0) {
      alert('Please add at least one valid assignment (subject and sections)');
      return;
    }

    const formData: TeacherFormWithAssignments = {
      ...teacherForm,
      assignments: validAssignments.map(assignment => ({
        gradeId: assignment.gradeId,
        subjectId: assignment.subjectId,
        sectionIds: assignment.sectionIds
      }))
    };

    try {
      await addTeacherMutation.mutateAsync(formData);
      onSuccess();
    } catch (error) {
      console.error('Error creating teacher:', error);
    }
  };

  // Filter assignments by current grade for display
  const currentGradeAssignments = assignments.filter(assignment => 
    assignment.gradeId === currentGrade
  );

  if (gradesLoading || subjectsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-[#84a98c] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-[#84a98c]">Loading form data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <div className="bg-[#84a98c] text-white p-6 rounded-t-lg">
        <h2 className="text-2xl font-bold">Register New Teacher</h2>
        <p className="text-[#FAFAF6] opacity-90">Create comprehensive teacher assignments across grades, subjects, and sections</p>
      </div>

      <form onSubmit={handleSubmit} className="p-6">
        {/* Basic Information */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
            Basic Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                First Name *
              </label>
              <input
                type="text"
                name="firstName"
                value={teacherForm.firstName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 bg-white text-gray-900 placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-[#84a98c]"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Middle Name
              </label>
              <input
                type="text"
                name="middleName"
                value={teacherForm.middleName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 bg-white text-gray-900 placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-[#84a98c]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Last Name *
              </label>
              <input
                type="text"
                name="lastName"
                value={teacherForm.lastName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 bg-white text-gray-900 placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-[#84a98c]"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address *
              </label>
              <input
                type="email"
                name="email"
                value={teacherForm.email}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 bg-white text-gray-900 placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-[#84a98c]"
                required
              />
            </div>
          </div>
        </div>

        {/* Subject Assignments */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
            Subject Assignments
          </h3>

          {/* Grade Tabs */}
          <div className="flex mb-6 border-b border-gray-200">
            {gradesWithSections?.map(grade => (
              <button
                key={grade.id}
                type="button"
                onClick={() => setCurrentGrade(grade.id)}
                className={`px-6 py-3 font-medium border-b-2 transition-colors ${
                  currentGrade === grade.id
                    ? 'border-[#84a98c] text-[#84a98c]'
                    : 'border-transparent text-gray-500 hover:text-[#84a98c]'
                }`}
              >
                {grade.name}
              </button>
            ))}
          </div>

          {/* Assignments Container */}
          <div className="space-y-4 mb-6">
            {currentGradeAssignments.length === 0 ? (
              <div className="text-center py-8 bg-gray-50 rounded-lg">
                <p className="text-gray-500 italic">
                  {currentGrade 
                    ? 'Click "Add New Assignment" to assign subjects and sections to this teacher'
                    : 'Please select a grade to start adding assignments'
                  }
                </p>
              </div>
            ) : (
              currentGradeAssignments.map(assignment => (
                <AssignmentCard
                  key={assignment.id}
                  assignment={assignment}
                  subjects={currentGradeSubjects}
                  sections={currentGradeSections}
                  onUpdate={updateAssignment}
                  onRemove={removeAssignment}
                  onToggleSection={toggleSection}
                />
              ))
            )}
          </div>

          {currentGrade && (
            <Button
              type="button"
              onClick={addAssignment}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              + Add New Assignment
            </Button>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-4 pt-6 border-t border-gray-200">
          <Button
            type="button"
            onClick={onClose}
            variant="outline"
            className="px-6"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={addTeacherMutation.isPending}
            className="bg-[#84a98c] hover:bg-[#6b8a6e] text-white px-6"
          >
            {addTeacherMutation.isPending ? 'Registering...' : 'Register Teacher'}
          </Button>
        </div>
      </form>
    </div>
  );
}

// Assignment Card Component
interface AssignmentCardProps {
  assignment: Assignment;
  subjects: any[];
  sections: any[];
  onUpdate: (id: number, field: keyof Assignment, value: any) => void;
  onRemove: (id: number) => void;
  onToggleSection: (assignmentId: number, sectionId: string) => void;
}

function AssignmentCard({ 
  assignment, 
  subjects, 
  sections, 
  onUpdate, 
  onRemove, 
  onToggleSection 
}: AssignmentCardProps) {
  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h4 className="font-medium text-gray-900">
          Assignment #{assignment.id}
        </h4>
        <button
          type="button"
          onClick={() => onRemove(assignment.id)}
          className="text-red-600 hover:text-red-800 text-sm font-medium"
        >
          Remove
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Subject Selection */}
        <div>
          <h5 className="font-medium text-gray-700 mb-3">Select Subject</h5>
          <div className="space-y-2">
            {subjects.map(subject => (
              <label key={subject.id} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-white cursor-pointer">
                <input
                  type="radio"
                  name={`subject_${assignment.id}`}
                  value={subject.id}
                  checked={assignment.subjectId === subject.id}
                  onChange={(e) => onUpdate(assignment.id, 'subjectId', e.target.value)}
                  className="mr-3"
                />
                <span 
                  className="font-medium"
                  style={{ color: subject.color || '#374151' }}
                >
                  {subject.name}
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Section Selection */}
        <div>
          <h5 className="font-medium text-gray-700 mb-3">Select Sections (Multiple allowed)</h5>
          <div className="grid grid-cols-2 gap-2">
            {sections.map(section => (
              <label 
                key={section.id} 
                className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-all ${
                  assignment.sectionIds.includes(section.id)
                    ? 'border-[#84a98c] bg-green-50'
                    : 'border-gray-200 hover:border-[#84a98c] hover:bg-green-50'
                }`}
                onClick={() => onToggleSection(assignment.id, section.id)}
              >
                <input
                  type="checkbox"
                  checked={assignment.sectionIds.includes(section.id)}
                  onChange={() => {}} // Handled by onClick
                  className="mr-2"
                />
                <span className="text-sm font-medium text-gray-900">
                  {section.name}
                </span>
              </label>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
