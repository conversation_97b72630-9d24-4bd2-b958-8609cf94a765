import { doc, setDoc, Timestamp } from 'firebase/firestore';
import { db } from '../firebase';
import { SystemSettings } from '../services/settingsService';

/**
 * Seed default system settings
 */
export async function seedSystemSettings(): Promise<void> {
  try {
    console.log('🔧 Seeding system settings...');

    const defaultSettings: SystemSettings = {
      // Delete functionality toggles
      allowDeleteSections: false,
      allowDeleteSubjects: false,
      allowDeleteGrades: false,
      allowDeleteStudents: true,
      allowDeleteTeachers: false,
      allowDeleteExams: true,
      
      // System settings
      systemName: 'GreenTech',
      schoolName: 'Pinagbarilan Elementary School',
      schoolYear: '2024-2025',
      
      // UI settings
      itemsPerPage: 10,
      enableDarkMode: false,
      enableNotifications: true,
      
      // Exam settings
      defaultExamDuration: 60, // minutes
      allowRetakeExams: false,
      showCorrectAnswers: true,
      
      // Security settings
      sessionTimeout: 30, // minutes
      requirePasswordChange: false,
      enableTwoFactorAuth: false,
      
      // Backup settings
      autoBackup: true,
      backupFrequency: 'weekly' as const,
      
      // Metadata
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      version: '1.0.0'
    };

    const settingsRef = doc(db, 'settings', 'system');
    await setDoc(settingsRef, defaultSettings);

    console.log('✅ System settings seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding system settings:', error);
    throw error;
  }
}

/**
 * Reset and seed system settings
 */
export async function resetAndSeedSettings(): Promise<void> {
  try {
    console.log('🔄 Resetting and seeding system settings...');
    await seedSystemSettings();
    console.log('✅ System settings reset and seeded successfully');
  } catch (error) {
    console.error('❌ Error resetting system settings:', error);
    throw error;
  }
}
