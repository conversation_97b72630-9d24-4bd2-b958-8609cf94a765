"use client";
import { useState } from 'react';
import { getAuth, createUserWithEmailAndPassword, signOut } from 'firebase/auth';
import { Button } from '@/components/ui/Button';

const testAccounts = {
  teachers: [
    { email: '<EMAIL>', password: 'Teacher123!' },
    { email: '<EMAIL>', password: 'Teacher123!' },
    { email: '<EMAIL>', password: 'Teacher123!' }
  ],
  admins: [
    { email: '<EMAIL>', password: 'Admin123!' },
    { email: '<EMAIL>', password: 'Principal123!' }
  ]
};

interface AuthResult {
  email: string;
  status: 'created' | 'already_exists' | 'error';
  error?: string;
}

const CreateAuthUsersPage = () => {
  const [isCreating, setIsCreating] = useState(false);
  const [results, setResults] = useState<AuthResult[]>([]);
  const [currentAccount, setCurrentAccount] = useState<string>('');

  const createAuthUser = async (email: string, password: string): Promise<AuthResult> => {
    const auth = getAuth();
    
    try {
      await createUserWithEmailAndPassword(auth, email, password);
      // Sign out immediately after creating to avoid staying logged in as test user
      await signOut(auth);
      return { email, status: 'created' };
    } catch (error: any) {
      if (error.code === 'auth/email-already-in-use') {
        return { email, status: 'already_exists' };
      }
      return { email, status: 'error', error: error.message };
    }
  };

  const createAllAuthUsers = async () => {
    setIsCreating(true);
    setResults([]);
    
    const allAccounts = [...testAccounts.teachers, ...testAccounts.admins];
    const newResults: AuthResult[] = [];

    for (const account of allAccounts) {
      setCurrentAccount(account.email);
      const result = await createAuthUser(account.email, account.password);
      newResults.push(result);
      setResults([...newResults]);
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setCurrentAccount('');
    setIsCreating(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'created': return 'text-green-700 bg-green-100';
      case 'already_exists': return 'text-yellow-700 bg-yellow-100';
      case 'error': return 'text-red-700 bg-red-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'created': return '✅';
      case 'already_exists': return '⚠️';
      case 'error': return '❌';
      default: return '⏳';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#f7faf8] to-[#eff5f1] py-12 px-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-[#57735d] mb-4">
            Create Firebase Auth Users
          </h1>
          <p className="text-lg text-[#6b8e72]">
            Create Firebase Authentication users for the seeded test accounts
          </p>
        </div>

        {/* Problem Explanation */}
        <div className="bg-yellow-50 border-2 border-yellow-300 p-6 mb-8">
          <h2 className="text-xl font-bold text-yellow-800 mb-3">
            🔧 Why This Step is Needed
          </h2>
          <div className="text-yellow-700 space-y-2">
            <p>• The previous seeder created <strong>Firestore documents</strong> but not <strong>Firebase Auth users</strong></p>
            <p>• Login requires both: Auth user (for authentication) + Firestore document (for user data)</p>
            <p>• This page creates the missing Firebase Auth users with the correct passwords</p>
            <p>• After this, you'll be able to login with the test accounts</p>
          </div>
        </div>

        {/* Create Auth Users */}
        <div className="bg-white border-2 border-[#84a98c] p-8 mb-8 shadow-lg">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-[#57735d] mb-4">
              Create Authentication Users
            </h2>
            <p className="text-[#6b8e72] mb-6">
              This will create Firebase Auth users for all test accounts (5 total)
            </p>
            
            <Button
              onClick={createAllAuthUsers}
              disabled={isCreating}
              loading={isCreating}
              loadingText="Creating Auth Users..."
              variant="filled"
              theme="primary"
              size="lg"
              radius="none"
            >
              {isCreating ? 'Creating Auth Users...' : 'Create All Auth Users'}
            </Button>

            {currentAccount && (
              <p className="mt-4 text-sm text-[#6b8e72]">
                Currently creating: <strong>{currentAccount}</strong>
              </p>
            )}
          </div>
        </div>

        {/* Results */}
        {results.length > 0 && (
          <div className="bg-white border-2 border-gray-300 p-6 mb-8 shadow-lg">
            <h3 className="text-xl font-bold text-gray-800 mb-4">Creation Results</h3>
            
            <div className="space-y-3">
              {results.map((result, index) => (
                <div key={index} className={`p-3 border-l-4 ${getStatusColor(result.status)}`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="mr-2">{getStatusIcon(result.status)}</span>
                      <strong>{result.email}</strong>
                    </div>
                    <span className="text-sm font-medium">
                      {result.status === 'created' && 'Created Successfully'}
                      {result.status === 'already_exists' && 'Already Exists'}
                      {result.status === 'error' && 'Error'}
                    </span>
                  </div>
                  {result.error && (
                    <p className="text-xs mt-1 ml-6">{result.error}</p>
                  )}
                </div>
              ))}
            </div>

            {/* Summary */}
            {results.length === 5 && (
              <div className="mt-6 p-4 bg-green-50 border border-green-200">
                <h4 className="font-bold text-green-800 mb-2">✅ Process Complete!</h4>
                <div className="text-sm text-green-700">
                  <p>Created: {results.filter(r => r.status === 'created').length}</p>
                  <p>Already Existed: {results.filter(r => r.status === 'already_exists').length}</p>
                  <p>Errors: {results.filter(r => r.status === 'error').length}</p>
                </div>
                <p className="mt-3 font-medium text-green-800">
                  🎉 You can now login with the test accounts!
                </p>
              </div>
            )}
          </div>
        )}

        {/* Test Accounts Reference */}
        <div className="bg-blue-50 border-2 border-blue-300 p-6 mb-8">
          <h3 className="text-xl font-bold text-blue-800 mb-4">Test Account Credentials</h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            {/* Teacher Accounts */}
            <div>
              <h4 className="font-semibold text-blue-700 mb-3">👨‍🏫 Teacher Accounts</h4>
              <div className="space-y-2">
                {testAccounts.teachers.map((teacher, index) => (
                  <div key={index} className="bg-white p-3 border border-blue-200">
                    <p className="font-mono text-sm">
                      <strong>Email:</strong> {teacher.email}<br />
                      <strong>Password:</strong> {teacher.password}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* Admin Accounts */}
            <div>
              <h4 className="font-semibold text-blue-700 mb-3">👨‍💼 Admin Accounts</h4>
              <div className="space-y-2">
                {testAccounts.admins.map((admin, index) => (
                  <div key={index} className="bg-white p-3 border border-blue-200">
                    <p className="font-mono text-sm">
                      <strong>Email:</strong> {admin.email}<br />
                      <strong>Password:</strong> {admin.password}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-white border-2 border-gray-300 p-6 mb-8">
          <h3 className="text-lg font-bold text-gray-800 mb-4">Next Steps</h3>
          <div className="space-y-2 text-sm text-gray-600">
            <p>1. ✅ <strong>Firestore Data:</strong> Already created via /seed-users</p>
            <p>2. 🔄 <strong>Auth Users:</strong> Create them using the button above</p>
            <p>3. 🧪 <strong>Test Login:</strong> Try logging in with the test accounts</p>
            <p>4. 🏗️ <strong>Refactor Dashboards:</strong> Use real data to build features</p>
          </div>
        </div>

        {/* Navigation */}
        <div className="text-center space-x-4">
          <Button
            onClick={() => window.location.href = '/seed-users'}
            variant="outlined"
            theme="primary"
            size="md"
            radius="none"
          >
            ← Back to Seeder
          </Button>
          
          <Button
            onClick={() => window.location.href = '/login'}
            variant="filled"
            theme="primary"
            size="md"
            radius="none"
          >
            Test Login →
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CreateAuthUsersPage;
