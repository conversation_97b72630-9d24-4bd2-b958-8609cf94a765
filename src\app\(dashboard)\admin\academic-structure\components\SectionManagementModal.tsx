"use client";
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Modal } from '@/components/ui/Modal';
import { useActiveGrades } from '@/hooks/useGrades';
import { useUpdateSection, useDeleteSection } from '@/hooks/useGrades';
import SectionCreateForm from './SectionCreateForm';

interface SectionManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'create' | 'edit' | 'delete';
  section?: any;
  gradeId?: string;
}

export default function SectionManagementModal({ 
  isOpen, 
  onClose, 
  mode, 
  section, 
  gradeId 
}: SectionManagementModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    gradeId: gradeId || ''
  });

  const updateSectionMutation = useUpdateSection();
  const deleteSectionMutation = useDeleteSection();

  useEffect(() => {
    if (section && mode === 'edit') {
      setFormData({
        name: section.name || '',
        gradeId: section.gradeId || ''
      });
    } else if (mode === 'create') {
      setFormData({
        name: '',
        gradeId: gradeId || ''
      });
    }
  }, [section, mode, gradeId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (mode === 'edit' && section) {
        await updateSectionMutation.mutateAsync({
          id: section.id,
          data: { name: formData.name }
        });
        onClose();
      }
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleDelete = async () => {
    if (section) {
      try {
        await deleteSectionMutation.mutateAsync(section.id);
        onClose();
      } catch (error) {
        // Error handled by mutation
      }
    }
  };

  if (!isOpen) return null;

  const getTitle = () => {
    switch (mode) {
      case 'create': return 'Add New Section';
      case 'edit': return 'Edit Section';
      case 'delete': return 'Delete Section';
      default: return 'Section Management';
    }
  };

  const getDescription = () => {
    switch (mode) {
      case 'create': return 'Create a new section for the selected grade level.';
      case 'edit': return 'Update the section information.';
      case 'delete': return `Are you sure you want to delete Section ${section?.name}? This action cannot be undone and will affect all students in this section.`;
      default: return '';
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={getTitle()}
      description={getDescription()}
      maxWidth="md"
    >

        {mode === 'delete' ? (
          /* Delete Confirmation */
          <div className="flex justify-end space-x-3">
            <Button
              onClick={onClose}
              variant="outlined"
              theme="neutral"
              size="sm"
            >
              Cancel
            </Button>
            <Button
              onClick={handleDelete}
              variant="filled"
              theme="warning"
              size="sm"
              loading={deleteSectionMutation.isPending}
            >
              Delete Section
            </Button>
          </div>
        ) : mode === 'create' ? (
          /* Create Form */
          <SectionCreateForm onClose={onClose} onSuccess={onClose} gradeId={gradeId} />
        ) : (
          /* Edit Form */
          <form onSubmit={handleSubmit} className="space-y-4">

            {/* Section Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Section Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border-2 border-gray-300 focus:outline-none focus:border-[#84a98c] text-gray-900 placeholder-gray-600 bg-white transition-colors"
                placeholder="e.g., A, B, C, Rizal, Bonifacio"
                required
              />
            </div>



            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                onClick={onClose}
                variant="outlined"
                theme="neutral"
                size="sm"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="filled"
                theme="primary"
                size="sm"
                loading={updateSectionMutation.isPending}
              >
                Update Section
              </Button>
            </div>
          </form>
        )}
    </Modal>
  );
}
