import { 
  collection, 
  doc, 
  getDocs, 
  getDoc,
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy,
  writeBatch
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Grade, Section, CreateGradeData, CreateSectionData, GradeWithSections } from '@/lib/types/school';

// Grade Management Functions
export const gradeService = {
  // Create a new grade
  async createGrade(data: CreateGradeData): Promise<Grade> {
    const now = new Date().toISOString();
    const gradeData = {
      ...data,
      isActive: data.isActive ?? true,
      createdAt: now,
      updatedAt: now,
    };

    const docRef = await addDoc(collection(db, 'grades'), gradeData);
    return {
      id: docRef.id,
      ...gradeData,
    };
  },

  // Get all grades
  async getAllGrades(): Promise<Grade[]> {
    const gradesSnapshot = await getDocs(collection(db, 'grades'));

    const grades = gradesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Grade[];

    // Sort by level in JavaScript instead of Firestore
    return grades.sort((a, b) => a.level - b.level);
  },

  // Get active grades only
  async getActiveGrades(): Promise<Grade[]> {
    const gradesSnapshot = await getDocs(
      query(collection(db, 'grades'), where('isActive', '==', true))
    );

    const grades = gradesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Grade[];

    // Sort by level in JavaScript instead of Firestore
    return grades.sort((a, b) => a.level - b.level);
  },

  // Get grade by ID
  async getGradeById(gradeId: string): Promise<Grade | null> {
    const gradeDoc = await getDoc(doc(db, 'grades', gradeId));
    
    if (!gradeDoc.exists()) {
      return null;
    }

    return {
      id: gradeDoc.id,
      ...gradeDoc.data()
    } as Grade;
  },

  // Update grade
  async updateGrade(gradeId: string, updates: Partial<CreateGradeData>): Promise<void> {
    const gradeRef = doc(db, 'grades', gradeId);
    await updateDoc(gradeRef, {
      ...updates,
      updatedAt: new Date().toISOString(),
    });
  },

  // Delete grade (and all its sections)
  async deleteGrade(gradeId: string): Promise<void> {
    const batch = writeBatch(db);

    // Delete all sections for this grade
    const sectionsSnapshot = await getDocs(
      query(collection(db, 'sections'), where('gradeId', '==', gradeId))
    );

    sectionsSnapshot.docs.forEach(sectionDoc => {
      batch.delete(sectionDoc.ref);
    });

    // Delete the grade
    batch.delete(doc(db, 'grades', gradeId));

    await batch.commit();
  },

  // Get grades with their sections
  async getGradesWithSections(): Promise<GradeWithSections[]> {
    const grades = await this.getActiveGrades();
    const sections = await sectionService.getAllSections();

    return grades.map(grade => ({
      ...grade,
      sections: sections.filter(section => section.gradeId === grade.id)
    }));
  }
};

// Section Management Functions
export const sectionService = {
  // Create a new section
  async createSection(data: CreateSectionData): Promise<Section> {
    const now = new Date().toISOString();
    const sectionData = {
      ...data,
      isActive: data.isActive ?? true,
      createdAt: now,
      updatedAt: now,
    };

    const docRef = await addDoc(collection(db, 'sections'), sectionData);
    return {
      id: docRef.id,
      ...sectionData,
    };
  },

  // Get all sections
  async getAllSections(): Promise<Section[]> {
    const sectionsSnapshot = await getDocs(collection(db, 'sections'));

    const sections = sectionsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Section[];

    // Sort by gradeId and name in JavaScript
    return sections.sort((a, b) => {
      if (a.gradeId !== b.gradeId) {
        return a.gradeId.localeCompare(b.gradeId);
      }
      return a.name.localeCompare(b.name);
    });
  },

  // Get sections by grade
  async getSectionsByGrade(gradeId: string): Promise<Section[]> {
    const sectionsSnapshot = await getDocs(
      query(collection(db, 'sections'), where('gradeId', '==', gradeId))
    );

    const sections = sectionsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Section[];

    // Filter active sections and sort by name in JavaScript
    return sections
      .filter(section => section.isActive)
      .sort((a, b) => a.name.localeCompare(b.name));
  },

  // Get section by ID
  async getSectionById(sectionId: string): Promise<Section | null> {
    const sectionDoc = await getDoc(doc(db, 'sections', sectionId));
    
    if (!sectionDoc.exists()) {
      return null;
    }

    return {
      id: sectionDoc.id,
      ...sectionDoc.data()
    } as Section;
  },

  // Update section
  async updateSection(sectionId: string, updates: Partial<CreateSectionData>): Promise<void> {
    const sectionRef = doc(db, 'sections', sectionId);
    await updateDoc(sectionRef, {
      ...updates,
      updatedAt: new Date().toISOString(),
    });
  },

  // Update section student count
  async updateSectionCount(sectionId: string, newCount: number): Promise<void> {
    const sectionRef = doc(db, 'sections', sectionId);
    await updateDoc(sectionRef, {
      currentCount: newCount,
      updatedAt: new Date().toISOString(),
    });
  },

  // Delete section
  async deleteSection(sectionId: string): Promise<void> {
    await deleteDoc(doc(db, 'sections', sectionId));
  }
};