const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  const serviceAccount = require('../serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

async function checkTeachers() {
  try {
    console.log('🔍 Checking Teachers Collection...\n');
    
    const snapshot = await db.collection('teachers').get();
    console.log(`📊 Total teachers: ${snapshot.size}\n`);
    
    if (snapshot.empty) {
      console.log('❌ No teachers found in collection.\n');
      return;
    }
    
    snapshot.forEach((doc, index) => {
      console.log(`👨‍🏫 Teacher ${index + 1} (ID: ${doc.id}):`);
      const data = doc.data();
      console.log(JSON.stringify(data, null, 2));
      console.log('─'.repeat(50));
    });
    
    // Also check if there are any exams
    console.log('\n🔍 Checking Exams Collection...\n');
    const examsSnapshot = await db.collection('exams').get();
    console.log(`📊 Total exams: ${examsSnapshot.size}\n`);
    
    if (!examsSnapshot.empty) {
      examsSnapshot.forEach((doc, index) => {
        console.log(`📝 Exam ${index + 1} (ID: ${doc.id}):`);
        const data = doc.data();
        console.log(JSON.stringify(data, null, 2));
        console.log('─'.repeat(50));
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkTeachers().then(() => {
  console.log('✅ Check complete!');
  process.exit(0);
}).catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
