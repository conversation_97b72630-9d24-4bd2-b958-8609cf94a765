"use client";
import FeatureShowcase from './FeatureShowcase';
import { useRouter } from 'next/navigation';
import { Button } from './ui/Button';
import { ArrowRightIcon } from '@heroicons/react/24/outline';

const HeroSection: React.FC = () => {
  const router = useRouter();
  return (
    <>
      {/* Header with Logo - Separated Container */}
      <header className="border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 lg:px-8 py-4">
          <div className="flex items-center space-x-3">
            <img src="/logo.png" alt="Pinagbarilan Elementary School Logo" className="w-10 h-10 object-contain" />
            <div>
              <h1 className="text-xl font-bold text-[#84a98c] leading-tight">
                GreenTech
              </h1>
              <p className="text-sm text-[#57735d] font-medium">
                Pinagbarilan Elementary School
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-16 pb-16 px-6 lg:px-8 min-h-screen flex items-center">
        <div className="max-w-7xl mx-auto w-full">
          <div className="grid lg:grid-cols-2 gap-16 items-start">
            {/* Left Content */}
            <div className="space-y-8 pt-8">
              <h2 className="text-4xl lg:text-6xl text-black leading-tight" style={{fontWeight: 300}}>
                Digital Learning
                <span className="block text-[#84a98c]">Assessment Platform</span>
              </h2>
            <p className="text-xl text-gray-600 leading-relaxed max-w-lg" style={{fontWeight: 300}}>
              Empowering students with sustainable digital exam technology featuring secure access and automated assessment.
            </p>
            <Button
              onClick={() => router.push('/login')}
              variant="filled"
              theme="primary"
              size="xl"
              radius="none"
              rightIcon={<ArrowRightIcon className="w-5 h-5" />}
              className="px-12 py-4"
            >
              Login to Platform
            </Button>
          </div>

            {/* Right Elevator Showcase */}
            <FeatureShowcase />
          </div>
        </div>
      </section>


    </>
  );
};

export default HeroSection; 