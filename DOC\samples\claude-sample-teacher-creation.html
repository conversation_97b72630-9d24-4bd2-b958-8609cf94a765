<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register New Teacher - Improved</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .form-container {
            padding: 40px;
        }
        
        .form-section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            font-weight: 600;
            color: #555;
            margin-bottom: 8px;
        }
        
        .form-group input {
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
        }
        
        .assignment-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid #e9ecef;
        }
        
        .assignment-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .assignment-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #495057;
        }
        
        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }
        
        .remove-btn:hover {
            background: #c82333;
        }
        
        .subject-section-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            align-items: start;
        }
        
        .subject-select {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }
        
        .subject-select h4 {
            margin-bottom: 15px;
            color: #495057;
        }
        
        .subject-option {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 10px;
            border-radius: 6px;
            transition: background-color 0.2s ease;
        }
        
        .subject-option:hover {
            background: #f8f9fa;
        }
        
        .subject-option input[type="radio"] {
            margin-right: 12px;
            transform: scale(1.2);
        }
        
        .sections-select {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }
        
        .sections-select h4 {
            margin-bottom: 15px;
            color: #495057;
        }
        
        .sections-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        
        .section-option {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .section-option:hover {
            border-color: #4CAF50;
            background: #f8fff8;
        }
        
        .section-option input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }
        
        .section-option.selected {
            border-color: #4CAF50;
            background: #e8f5e8;
        }
        
        .add-assignment-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s ease;
            margin-bottom: 30px;
        }
        
        .add-assignment-btn:hover {
            transform: translateY(-2px);
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            padding-top: 30px;
            border-top: 2px solid #e0e0e0;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-cancel {
            background: #6c757d;
            color: white;
        }
        
        .btn-cancel:hover {
            background: #5a6268;
        }
        
        .btn-submit {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
        }
        
        .grade-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .grade-tab {
            padding: 15px 25px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            color: #6c757d;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .grade-tab.active {
            color: #4CAF50;
            border-bottom-color: #4CAF50;
        }
        
        .grade-tab:hover {
            color: #4CAF50;
        }
        
        .no-assignments {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 40px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Register New Teacher</h1>
            <p>Create comprehensive teacher assignments across grades, subjects, and sections</p>
        </div>
        
        <div class="form-container">
            <form id="teacherForm">
                <!-- Basic Information -->
                <div class="form-section">
                    <h2 class="section-title">Basic Information</h2>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="firstName">First Name *</label>
                            <input type="text" id="firstName" name="firstName" required>
                        </div>
                        <div class="form-group">
                            <label for="middleName">Middle Name</label>
                            <input type="text" id="middleName" name="middleName">
                        </div>
                        <div class="form-group">
                            <label for="lastName">Last Name *</label>
                            <input type="text" id="lastName" name="lastName" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                    </div>
                </div>
                
                <!-- Subject Assignments -->
                <div class="form-section">
                    <h2 class="section-title">Subject Assignments</h2>
                    
                    <div class="grade-tabs">
                        <button type="button" class="grade-tab active" data-grade="4">Grade 4</button>
                        <button type="button" class="grade-tab" data-grade="5">Grade 5</button>
                        <button type="button" class="grade-tab" data-grade="6">Grade 6</button>
                    </div>
                    
                    <div id="assignmentsContainer">
                        <div class="no-assignments">
                            Click "Add New Assignment" to assign subjects and sections to this teacher
                        </div>
                    </div>
                    
                    <button type="button" class="add-assignment-btn" onclick="addAssignment()">
                        + Add New Assignment
                    </button>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-cancel">Cancel</button>
                    <button type="submit" class="btn btn-submit">Register Teacher</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let assignmentCounter = 0;
        let currentGrade = 4;
        
        // Sample data - replace with actual data from your backend
        const gradeData = {
            4: {
                subjects: [
                    { id: 1, name: 'English', color: '#e74c3c' },
                    { id: 2, name: 'Math', color: '#3498db' },
                    { id: 3, name: 'Science', color: '#2ecc71' },
                    { id: 4, name: 'Filipino', color: '#f39c12' },
                    { id: 5, name: 'ESP', color: '#9b59b6' }
                ],
                sections: [
                    { id: 1, name: 'Rizal' },
                    { id: 2, name: 'Bonifacio' },
                    { id: 3, name: 'Aguinaldo' },
                    { id: 4, name: 'Luna' }
                ]
            },
            5: {
                subjects: [
                    { id: 6, name: 'English', color: '#e74c3c' },
                    { id: 7, name: 'Math', color: '#3498db' },
                    { id: 8, name: 'Science', color: '#2ecc71' },
                    { id: 9, name: 'Araling Panlipunan', color: '#f39c12' }
                ],
                sections: [
                    { id: 5, name: 'Bulacan' },
                    { id: 6, name: 'Pampanga' },
                    { id: 7, name: 'Bataan' },
                    { id: 8, name: 'Zambales' }
                ]
            },
            6: {
                subjects: [
                    { id: 10, name: 'English', color: '#e74c3c' },
                    { id: 11, name: 'Math', color: '#3498db' },
                    { id: 12, name: 'Science', color: '#2ecc71' },
                    { id: 13, name: 'Araling Panlipunan', color: '#f39c12' },
                    { id: 14, name: 'Music, Arts, PE & Health', color: '#9b59b6' }
                ],
                sections: [
                    { id: 9, name: 'Mayon' },
                    { id: 10, name: 'Taal' },
                    { id: 11, name: 'Pinatubo' }
                ]
            }
        };
        
        // Grade tab switching
        document.querySelectorAll('.grade-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.grade-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                currentGrade = parseInt(this.dataset.grade);
                filterAssignmentsByGrade();
            });
        });
        
        function filterAssignmentsByGrade() {
            const assignments = document.querySelectorAll('.assignment-container');
            let visibleCount = 0;
            
            assignments.forEach(assignment => {
                const grade = parseInt(assignment.dataset.grade);
                if (grade === currentGrade) {
                    assignment.style.display = 'block';
                    visibleCount++;
                } else {
                    assignment.style.display = 'none';
                }
            });
            
            const noAssignments = document.querySelector('.no-assignments');
            if (visibleCount === 0) {
                noAssignments.style.display = 'block';
            } else {
                noAssignments.style.display = 'none';
            }
        }
        
        function addAssignment() {
            assignmentCounter++;
            const data = gradeData[currentGrade];
            
            const assignmentHtml = `
                <div class="assignment-container" data-grade="${currentGrade}" data-id="${assignmentCounter}">
                    <div class="assignment-header">
                        <div class="assignment-title">Grade ${currentGrade} Assignment #${assignmentCounter}</div>
                        <button type="button" class="remove-btn" onclick="removeAssignment(${assignmentCounter})">Remove</button>
                    </div>
                    
                    <div class="subject-section-grid">
                        <div class="subject-select">
                            <h4>Select Subject</h4>
                            ${data.subjects.map(subject => `
                                <div class="subject-option">
                                    <input type="radio" id="subject_${assignmentCounter}_${subject.id}" 
                                           name="subject_${assignmentCounter}" value="${subject.id}"
                                           onchange="updateSections(${assignmentCounter})">
                                    <label for="subject_${assignmentCounter}_${subject.id}" style="color: ${subject.color}; font-weight: 600;">
                                        ${subject.name}
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        
                        <div class="sections-select">
                            <h4>Select Sections (Multiple allowed)</h4>
                            <div class="sections-grid">
                                ${data.sections.map(section => `
                                    <div class="section-option" onclick="toggleSection(this)">
                                        <input type="checkbox" id="section_${assignmentCounter}_${section.id}" 
                                               name="sections_${assignmentCounter}[]" value="${section.id}">
                                        <label for="section_${assignmentCounter}_${section.id}">${section.name}</label>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.querySelector('.no-assignments').style.display = 'none';
            document.getElementById('assignmentsContainer').insertAdjacentHTML('beforeend', assignmentHtml);
        }
        
        function removeAssignment(id) {
            document.querySelector(`[data-id="${id}"]`).remove();
            
            const remainingAssignments = document.querySelectorAll('.assignment-container[data-grade="' + currentGrade + '"]');
            if (remainingAssignments.length === 0) {
                document.querySelector('.no-assignments').style.display = 'block';
            }
        }
        
        function toggleSection(element) {
            const checkbox = element.querySelector('input[type="checkbox"]');
            checkbox.checked = !checkbox.checked;
            element.classList.toggle('selected', checkbox.checked);
        }
        
        function updateSections(assignmentId) {
            // This function can be used to dynamically filter sections based on subject
            // For now, all sections remain available
        }
        
        // Form submission
        document.getElementById('teacherForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const assignments = [];
            
            document.querySelectorAll('.assignment-container').forEach(container => {
                const grade = container.dataset.grade;
                const assignmentId = container.dataset.id;
                
                const selectedSubject = container.querySelector(`input[name="subject_${assignmentId}"]:checked`);
                const selectedSections = container.querySelectorAll(`input[name="sections_${assignmentId}[]"]:checked`);
                
                if (selectedSubject && selectedSections.length > 0) {
                    assignments.push({
                        grade_level: parseInt(grade),
                        subject_id: parseInt(selectedSubject.value),
                        section_ids: Array.from(selectedSections).map(s => parseInt(s.value))
                    });
                }
            });
            
            const teacherData = {
                firstName: formData.get('firstName'),
                middleName: formData.get('middleName'),
                lastName: formData.get('lastName'),
                email: formData.get('email'),
                assignments: assignments
            };
            
            console.log('Teacher Data:', teacherData);
            alert('Teacher registration data prepared! Check console for details.');
        });
    </script>
</body>
</html>