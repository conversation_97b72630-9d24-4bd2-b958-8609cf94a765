"use client";
import { useRouter } from "next/navigation";
import { AcademicCapIcon, UserIcon, ArrowRightIcon } from "@heroicons/react/24/outline";
import { Button } from "../../../components/ui/Button";

const LoginPage = () => {
  const router = useRouter();

  const handleRoleSelection = (role: 'student' | 'teacher') => {
    if (role === 'student') {
      router.push('/login/student');
    } else {
      router.push('/login/teacher');
    }
  };

  return (
    <div className="theme-primary">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          <img src="/logo.png" alt="Pinagbarilan Elementary School Logo" className="w-16 h-16 object-contain" />
        </div>
        <h1 className="text-3xl font-bold text-[#57735d] mb-2">Welcome to GreenTech</h1>
        <p className="text-[#6b8e72]">Choose your login type to continue</p>
      </div>

      {/* Role Selection Cards */}
      <div className="space-y-4">
        {/* Student Login */}
        <div className="theme-student">
          <Button
            onClick={() => handleRoleSelection('student')}
            variant="outlined"
            theme="student"
            size="lg"
            fullWidth
            radius="none"
            className="p-6 h-auto"
            leftIcon={<AcademicCapIcon className="w-8 h-8" />}
            rightIcon={<ArrowRightIcon className="w-5 h-5" />}
            href="/login/student"
            navigationLoading={true}
          >
            <div className="flex-1 text-left">
              <h3 className="text-lg font-semibold">Student Login</h3>
              <p className="text-sm opacity-80">Login with your QR code</p>
            </div>
          </Button>
        </div>

        {/* Teacher Login */}
        <div className="theme-teacher">
          <Button
            onClick={() => handleRoleSelection('teacher')}
            variant="outlined"
            theme="teacher"
            size="lg"
            fullWidth
            radius="none"
            className="p-6 h-auto"
            leftIcon={<UserIcon className="w-8 h-8" />}
            rightIcon={<ArrowRightIcon className="w-5 h-5" />}
            href="/login/teacher"
            navigationLoading={true}
          >
            <div className="flex-1 text-left">
              <h3 className="text-lg font-semibold">Teacher Login</h3>
              <p className="text-sm opacity-80">Login with email and password</p>
            </div>
          </Button>
        </div>


      </div>
    </div>
  );
};

export default LoginPage;
