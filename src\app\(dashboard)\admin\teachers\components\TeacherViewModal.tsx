"use client";
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/Button';
import { CardBadge } from '@/components/cards';
import { useGradeAndSectionNames } from '@/hooks/useGrades';
import { useSubjects } from '@/hooks/useSubjects';
import type { TeacherWithAssignments } from '@/lib/services/teacherService';

interface TeacherViewModalProps {
  teacher: TeacherWithAssignments | null;
  isOpen: boolean;
  onClose: () => void;
}

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
};

const modalVariants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: -50
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.3
    }
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    y: -50,
    transition: {
      duration: 0.2
    }
  }
};

export default function TeacherViewModal({ teacher, isOpen, onClose }: TeacherViewModalProps) {
  const { getGradeName, getSectionName, grades } = useGradeAndSectionNames();
  const { data: subjects = [] } = useSubjects();

  // Helper function to get section with grade name
  const getSectionWithGrade = (sectionId: string): string => {
    if (!grades) return 'Unknown Section';

    for (const grade of grades) {
      const section = grade.sections?.find(s => s.id === sectionId);
      if (section) {
        return `${grade.name} - ${section.name}`;
      }
    }

    return 'Unknown Section';
  };

  if (!teacher) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed left-[50%] top-[50%] z-50 w-full max-w-3xl max-h-[90vh] translate-x-[-50%] translate-y-[-50%] bg-white border-2 border-[#84a98c] shadow-lg p-6 overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="mb-6">
              <h2 className="text-lg font-semibold leading-none tracking-tight text-[#84a98c] mb-2">Teacher Details</h2>
              <p className="text-sm text-gray-600">
                View complete teacher information and assignments.
              </p>
            </div>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">Full Name</label>
              <p className="text-gray-700 bg-gray-50 p-3 border rounded">
                {teacher.lastName}, {teacher.firstName}
                {teacher.middleName && ` ${teacher.middleName}`}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">Email</label>
              <p className="text-gray-700 bg-gray-50 p-3 border rounded">{teacher.email}</p>
            </div>
          </div>

          {/* Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">Status</label>
              <div className="bg-gray-50 p-3 border rounded">
                <CardBadge
                  variant={teacher.status === 'active' ? 'success' : 'error'}
                  size="md"
                >
                  {teacher.status === 'active' ? 'Active' : 'Inactive'}
                </CardBadge>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-[#84a98c] mb-1">Exams</label>
              <div className="bg-gray-50 p-3 border rounded">
                <span className="text-gray-700">{teacher.examIds?.length || 0} exams assigned</span>
              </div>
            </div>
          </div>

          {/* Subjects */}
          <div>
            <label className="block text-sm font-medium text-[#84a98c] mb-2">Subjects</label>
            <div className="bg-gray-50 p-4 border rounded">
              {teacher.subjects && teacher.subjects.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {teacher.subjects.map((subject, index) => {
                    const subjectData = subjects.find(s => s.name === subject);
                    return (
                      <CardBadge
                        key={index}
                        variant="custom"
                        size="md"
                        style={{
                          backgroundColor: subjectData?.color || '#E5E7EB',
                          color: '#374151'
                        }}
                      >
                        {subject}
                      </CardBadge>
                    );
                  })}
                </div>
              ) : (
                <span className="text-gray-400">No subjects assigned</span>
              )}
            </div>
          </div>

          {/* Grade Levels */}
          <div>
            <label className="block text-sm font-medium text-[#84a98c] mb-2">Grade Levels</label>
            <div className="bg-gray-50 p-4 border rounded">
              {teacher.gradeIds && teacher.gradeIds.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {teacher.gradeIds.map(gradeId => (
                    <CardBadge key={gradeId} variant="info" size="md">
                      {getGradeName(gradeId)}
                    </CardBadge>
                  ))}
                </div>
              ) : (
                <span className="text-gray-400">No grades assigned</span>
              )}
            </div>
          </div>

          {/* Sections */}
          <div>
            <label className="block text-sm font-medium text-[#84a98c] mb-2">Sections</label>
            <div className="bg-gray-50 p-4 border rounded">
              {teacher.sectionIds && teacher.sectionIds.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {teacher.sectionIds.map(sectionId => (
                    <CardBadge key={sectionId} variant="success" size="md">
                      {getSectionWithGrade(sectionId)}
                    </CardBadge>
                  ))}
                </div>
              ) : (
                <span className="text-gray-400">No sections assigned</span>
              )}
            </div>
          </div>

          {/* Timestamps */}
          {(teacher.createdAt || teacher.updatedAt) && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {teacher.createdAt && (
                <div>
                  <label className="block text-sm font-medium text-[#84a98c] mb-1">Created</label>
                  <p className="text-gray-700 bg-gray-50 p-3 border rounded text-sm">
                    {new Date(teacher.createdAt.seconds * 1000).toLocaleString()}
                  </p>
                </div>
              )}
              {teacher.updatedAt && (
                <div>
                  <label className="block text-sm font-medium text-[#84a98c] mb-1">Last Updated</label>
                  <p className="text-gray-700 bg-gray-50 p-3 border rounded text-sm">
                    {new Date(teacher.updatedAt.seconds * 1000).toLocaleString()}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

            <div className="flex justify-end mt-6">
              <Button
                onClick={onClose}
                variant="outlined"
                theme="neutral"
              >
                Close
              </Button>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
