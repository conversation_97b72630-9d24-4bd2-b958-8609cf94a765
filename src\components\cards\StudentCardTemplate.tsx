"use client";
import React from 'react';
import { AcademicCapIcon, QrCodeIcon } from "@heroicons/react/24/outline";
import { <PERSON><PERSON>ard, CardHeader, CardContent, CardInfoRow, CardBadge, CardActions } from './index';

interface StudentCardTemplateProps {
  student: {
    // Support both old and new data structures
    firstName?: string;
    middleName?: string;
    lastName?: string;
    fullName?: string[];
    extension?: string;
    lrn: string;
    studentId?: string;
    status?: string;
    gradeId?: string;
    sectionId?: string;
    qrUrl?: string;
    qr_code?: string;
  };
  gradeSection?: string;
  actions?: React.ReactNode;
  className?: string;
}

export default function StudentCardTemplate({
  student,
  gradeSection,
  actions,
  className
}: StudentCardTemplateProps) {
  // Handle both old and new data structures
  let displayName = '';
  if (student.fullName && Array.isArray(student.fullName)) {
    // New structure: fullName is an array [firstName, middleName, lastName]
    const [firstName, middleName, lastName] = student.fullName;
    displayName = `${lastName || ''}, ${firstName || ''}${
      middleName ? ` ${middleName}` : ''
    }${student.extension ? ` ${student.extension}` : ''}`;
  } else if (student.firstName && student.lastName) {
    // Old structure: individual name fields
    displayName = `${student.lastName}, ${student.firstName}${
      student.middleName ? ` ${student.middleName}` : ''
    }${student.extension ? ` ${student.extension}` : ''}`;
  } else {
    displayName = 'Unknown Student';
  }

  // Handle LRN display - support both lrn and studentId
  const lrnDisplay = student.lrn || student.studentId || 'No LRN';

  return (
    <BaseCard
      borderColor="student"
      className={className}
    >
      <CardHeader
        title={displayName}
        subtitle={`LRN: ${lrnDisplay}`}
        titleColor="student"
      />

      <CardContent>
        <CardInfoRow
          icon={<AcademicCapIcon className="w-4 h-4 text-gray-500" />}
          label="Grade & Section"
          labelColor="student"
        >
          {gradeSection ? (
            <span className="text-sm text-gray-700">{gradeSection}</span>
          ) : (
            <span className="text-sm text-gray-400 italic">Not assigned to grade/section</span>
          )}
        </CardInfoRow>

        <CardInfoRow
          icon={<QrCodeIcon className="w-4 h-4 text-gray-500" />}
          label="QR Code"
          labelColor="student"
        >
          {(student.qrUrl || student.qr_code) ? (
            <span className="text-sm text-green-600">QR Code generated</span>
          ) : (
            <span className="text-sm text-gray-400">QR Code not generated</span>
          )}
        </CardInfoRow>
      </CardContent>

      {actions && (
        <CardActions>
          {actions}
        </CardActions>
      )}
    </BaseCard>
  );
}
