"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { signInWithEmailAndPassword, getAuth } from "firebase/auth";
import { ShieldCheckIcon, EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";
import { Button } from "../../../../components/ui/Button";

const AdminLoginPage = () => {
  const [formData, setFormData] = useState({
    email: "",
    password: ""
  });
  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) setError("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    
    if (!formData.email || !formData.password) {
      setError("Please fill in all fields.");
      return;
    }

    setIsSubmitting(true);
    try {
      const auth = getAuth();
      await signInWithEmailAndPassword(auth, formData.email, formData.password);
      
      // Check if user is an admin in the admins collection
      const { collection, query, where, getDocs } = await import("firebase/firestore");
      const { db } = await import("../../../../lib/firebase");

      const adminsQuery = query(
        collection(db, "admins"),
        where("email", "==", formData.email),
        where("status", "==", "active")
      );
      const adminSnapshot = await getDocs(adminsQuery);

      if (!adminSnapshot.empty) {
        // User is an admin, redirect to admin dashboard
        router.push("/admin/dashboard");
        return;
      }

      // If not found in admins collection, check hardcoded admin emails as fallback
      const adminEmails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
      ];

      if (adminEmails.includes(formData.email.toLowerCase())) {
        router.push("/admin/dashboard");
        return;
      }

      // If no admin access found, deny access
      setError("Access denied. This page is for administrators only.");
      await auth.signOut();
    } catch (err: any) {
      console.error("Admin login error:", err);
      
      let errorMessage = "Login failed. Please try again.";
      switch (err.code) {
        case 'auth/user-not-found':
          errorMessage = "No administrator account found with this email address.";
          break;
        case 'auth/wrong-password':
          errorMessage = "Incorrect password. Please try again.";
          break;
        case 'auth/invalid-email':
          errorMessage = "Please enter a valid email address.";
          break;
        case 'auth/too-many-requests':
          errorMessage = "Too many failed attempts. Please try again later.";
          break;
        case 'auth/user-disabled':
          errorMessage = "This account has been disabled. Please contact the system administrator.";
          break;
        default:
          errorMessage = err.message || errorMessage;
      }
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center px-4 theme-admin">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <ShieldCheckIcon className="w-16 h-16 text-slate-700" />
          </div>
          <h1 className="text-3xl font-bold text-slate-800 mb-2">Administrator Login</h1>
          <p className="text-slate-600">Secure access for system administrators</p>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="bg-white border-2 border-slate-300 p-8 shadow-lg">
          {/* Security Notice */}
          <div className="mb-6 p-4 bg-yellow-50 border-2 border-yellow-300">
            <div className="flex items-start space-x-2">
              <ShieldCheckIcon className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-semibold text-yellow-800">Restricted Access</h3>
                <p className="text-xs text-yellow-700 mt-1">
                  This page is for authorized administrators only. All login attempts are logged and monitored.
                </p>
              </div>
            </div>
          </div>

          {/* Email Field */}
          <div className="mb-6">
            <label htmlFor="email" className="block text-sm font-semibold text-[#1A2340] mb-2">
              Administrator Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full px-4 py-3 border-2 border-slate-300 focus:outline-none focus:ring-2 focus:ring-slate-600 focus:ring-offset-2 text-slate-800 placeholder:text-slate-500"
              placeholder="Enter your admin email"
              required
              disabled={isSubmitting}
            />
          </div>

          {/* Password Field */}
          <div className="mb-6">
            <label htmlFor="password" className="block text-sm font-semibold text-[#1A2340] mb-2">
              Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border-2 border-slate-300 focus:outline-none focus:ring-2 focus:ring-slate-600 focus:ring-offset-2 pr-12 text-slate-800 placeholder:text-slate-500"
                placeholder="Enter your password"
                required
                disabled={isSubmitting}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-[#1A2340]"
                disabled={isSubmitting}
              >
                {showPassword ? (
                  <EyeSlashIcon className="w-5 h-5" />
                ) : (
                  <EyeIcon className="w-5 h-5" />
                )}
              </button>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-4 border-2 border-red-500 bg-red-50">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={isSubmitting}
            variant="filled"
            theme="admin"
            size="lg"
            fullWidth
            radius="none"
            loading={isSubmitting}
            loadingText="Authenticating..."
          >
            Access Admin Panel
          </Button>

          {/* Security Footer */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              Protected by enterprise-grade security protocols
            </p>
          </div>
        </form>

        {/* No Back Button - Admin access is URL-only */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            Direct URL access only - No public navigation available
          </p>
        </div>
      </div>
    </div>
  );
};

export default AdminLoginPage;
