"use client";
import React, { useState } from 'react';
import { AcademicCapIcon, PlusIcon, MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { Button } from '../../../../components/ui/Button';
import Select from '../../../../components/ui/Select';


// Import hooks and types
import { useTeachersWithAssignments } from '@/hooks/useTeachers';
import { useGradesWithSections } from '@/hooks/useGrades';
import { useSubjects } from '@/hooks/useSubjects';
import { useQuery } from '@tanstack/react-query';
import type { Teacher, TeacherWithAssignments } from '@/lib/services/teacherService';

// Import components
import TeacherCard from './components/TeacherCard';
import AddTeacherModal from './components/AddTeacherModal';
import EditTeacherModal from './components/EditTeacherModal';
import TeacherViewModal from './components/TeacherViewModal';
import { CardSkeleton, PageHeaderSkeleton, FiltersSkeleton } from '@/components/skeletons';

export default function TeachersPage() {
  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSubjectFilter, setSelectedSubjectFilter] = useState('');
  const [selectedSectionFilter, setSelectedSectionFilter] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedTeacher, setSelectedTeacher] = useState<TeacherWithAssignments | null>(null);

  // Hooks - Use seeded teacher data
  const { data: teachers = [], isLoading, error } = useQuery({
    queryKey: ['teachers', 'with-assignments', 'seeded'],
    queryFn: async () => {
      const response = await fetch('/api/admin/teachers-with-assignments');
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch teachers');
      }
      return result.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  const { data: gradesWithSections = [] } = useGradesWithSections();
  const { data: subjects = [] } = useSubjects();

  // Get all unique sections for filter
  const allSections = gradesWithSections.flatMap(grade => 
    grade.sections?.map(section => ({
      ...section,
      gradeName: grade.name
    })) || []
  );

  // Filter teachers based on search, subject, and section
  const filteredTeachers = teachers.filter((teacher: { firstName: any; middleName: any; lastName: any; email: string; subjects: any[]; sectionIds: string | string[]; }) => {
    const matchesSearch = searchTerm === '' ||
      `${teacher.firstName} ${teacher.middleName} ${teacher.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
      teacher.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesSubject = selectedSubjectFilter === '' ||
      teacher.subjects?.some((subject: string) => subject.toLowerCase().includes(selectedSubjectFilter.toLowerCase()));

    const matchesSection = selectedSectionFilter === '' ||
      teacher.sectionIds?.includes(selectedSectionFilter);

    return matchesSearch && matchesSubject && matchesSection;
  });

  // Handle actions
  const handleViewTeacher = (teacher: TeacherWithAssignments) => {
    setSelectedTeacher(teacher);
    setShowViewModal(true);
  };

  const handleEditTeacher = (teacher: TeacherWithAssignments) => {
    setSelectedTeacher(teacher);
    setShowEditModal(true);
  };

  if (error) {
    return (
        <div className="flex items-center justify-center p-8">
          <div className="text-center">
            <p className="text-red-600">Error loading teachers. Please try again.</p>
          </div>
        </div>
    );
  }

  return (
      <div className="space-y-6">
        {/* Header */}
        {isLoading ? (
          <PageHeaderSkeleton />
        ) : (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <AcademicCapIcon className="w-8 h-8 text-[#84a98c]" />
              <div>
                <h2 className="text-2xl font-bold text-[#84a98c]">Teacher Management</h2>
                <p className="text-sm text-gray-600">Manage teacher accounts and assignments</p>
              </div>
            </div>

            <Button
              onClick={() => setShowAddModal(true)}
              variant="filled"
              theme="primary"
              size="md"
              radius="none"
              leftIcon={<PlusIcon className="w-4 h-4" />}
            >
              Add Teacher
            </Button>
          </div>
        )}

        {/* Filters and Search */}
        {isLoading ? (
          <FiltersSkeleton filterCount={3} />
        ) : (
          <div className="bg-white border-2 border-[#84a98c] p-6 shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-[#84a98c] bg-white text-gray-900 placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-[#84a98c]"
                />
              </div>

              <Select
                value={selectedSubjectFilter}
                onChange={(e) => setSelectedSubjectFilter(e.target.value)}
                variant="primary"
              >
                <option value="">All Subjects</option>
                {subjects.map(subject => (
                  <option key={subject.id} value={subject.code}>
                    {subject.name} for Grade {subject.code[subject.code.length - 1]}
                  </option>
                ))}
              </Select>

              <Select
                value={selectedSectionFilter}
                onChange={(e) => setSelectedSectionFilter(e.target.value)}
                variant="primary"
              >
                <option value="">All Sections</option>
                {allSections.map(section => (
                  <option key={section.id} value={section.id}>
                    {section.gradeName} - {section.name}
                  </option>
                ))}
              </Select>
            </div>

            <div className="mt-4 text-sm text-gray-600">
              Showing {filteredTeachers.length} of {teachers.length} teachers
            </div>
          </div>
        )}

      {/* Add Teacher Modal */}
      <AddTeacherModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={() => {
          setShowAddModal(false);
          // Refresh the data
        }}
      />

        {/* Teachers Cards Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <CardSkeleton count={6} borderColor="teacher" />
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTeachers.map((teacher: Teacher) => (
              <TeacherCard
                key={teacher.id}
                teacher={teacher}
                onView={() => handleViewTeacher(teacher)}
                onEdit={() => handleEditTeacher(teacher)}
              />
            ))}
          </div>
        )}

        {/* Empty State */}
        {!isLoading && filteredTeachers.length === 0 && (
          <div className="text-center py-12">
            <AcademicCapIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-500 mb-2">No teachers found</h3>
            <p className="text-gray-400">
              {searchTerm || selectedSubjectFilter || selectedSectionFilter
                ? "Try adjusting your search criteria."
                : "Get started by adding your first teacher."}
            </p>
          </div>
        )}

      {/* Modals */}
      <TeacherViewModal
        teacher={selectedTeacher}
        isOpen={showViewModal}
        onClose={() => setShowViewModal(false)}
      />

      {/* Edit Teacher Modal */}
      <EditTeacherModal
        teacher={selectedTeacher}
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSuccess={() => {
          setShowEditModal(false);
          setSelectedTeacher(null);
          // Refresh the data
        }}
      />
      </div>
  );
}
