"use client";

import { Toaster } from 'react-hot-toast';

export default function ToastProvider() {
  return (
    <Toaster
      position="top-right"
      containerStyle={{
        zIndex: 30, // Lower z-index to not interfere with modal backdrops (z-40)
      }}
      toastOptions={{
        duration: 4000,
        style: {
          background: '#84a98c',
          color: '#FAFAF6',
          border: '2px solid #84a98c',
          borderRadius: '0px',
          fontSize: '14px',
          fontWeight: '500',
        },
        success: {
          style: {
            background: '#10B981',
            color: 'white',
            border: '2px solid #10B981',
          },
        },
        error: {
          style: {
            background: '#EF4444',
            color: 'white',
            border: '2px solid #EF4444',
          },
        },
      }}
    />
  );
}
