"use client";
import { useState } from 'react';
import { Button } from '@/components/ui/Button';

interface SeedResult {
  type: string;
  email?: string;
  lrn?: string;
  status: string;
  error?: string;
}

interface SeedResponse {
  success: boolean;
  message: string;
  results: SeedResult[];
  summary: {
    teachers: { created: number; existing: number; errors: number };
    admins: { created: number; existing: number; errors: number };
    students: { created: number; existing: number; errors: number };
  };
  testAccounts: {
    teachers: { email: string; password: string }[];
    admins: { email: string; password: string }[];
  };
}

const SeedUsersPage = () => {
  const [isSeeding, setIsSeeding] = useState(false);
  const [seedResult, setSeedResult] = useState<SeedResponse | null>(null);
  const [error, setError] = useState<string>('');

  const handleSeedUsers = async () => {
    setIsSeeding(true);
    setError('');
    setSeedResult(null);

    try {
      const response = await fetch('/api/seed-users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setSeedResult(data);
      } else {
        setError(data.message || 'Failed to seed users');
      }
    } catch (err) {
      setError('Network error occurred while seeding users');
      console.error('Seeding error:', err);
    } finally {
      setIsSeeding(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#f7faf8] to-[#eff5f1] py-12 px-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-[#57735d] mb-4">
            GreenTech User Seeder
          </h1>
          <p className="text-lg text-[#6b8e72]">
            Create test accounts for teachers and administrators
          </p>
        </div>

        {/* Seeder Controls */}
        <div className="bg-white border-2 border-[#84a98c] p-8 mb-8 shadow-lg">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-[#57735d] mb-4">
              Seed Database with Test Users
            </h2>
            <p className="text-[#6b8e72] mb-6">
              This will create test accounts for teachers, admins, and students in your Firestore database.
              Existing users will not be duplicated.
            </p>
            
            <Button
              onClick={handleSeedUsers}
              disabled={isSeeding}
              loading={isSeeding}
              loadingText="Seeding Users..."
              variant="filled"
              theme="primary"
              size="lg"
              radius="none"
            >
              {isSeeding ? 'Seeding Users...' : 'Seed Users'}
            </Button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border-2 border-red-300 p-6 mb-8">
            <h3 className="text-lg font-bold text-red-800 mb-2">Error</h3>
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {/* Results Display */}
        {seedResult && (
          <div className="space-y-6">
            {/* Summary */}
            <div className="bg-green-50 border-2 border-green-300 p-6">
              <h3 className="text-lg font-bold text-green-800 mb-4">Seeding Summary</h3>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="text-center">
                  <h4 className="font-semibold text-green-700">Teachers</h4>
                  <p className="text-sm text-green-600">
                    Created: {seedResult.summary.teachers.created} | 
                    Existing: {seedResult.summary.teachers.existing} | 
                    Errors: {seedResult.summary.teachers.errors}
                  </p>
                </div>
                <div className="text-center">
                  <h4 className="font-semibold text-green-700">Admins</h4>
                  <p className="text-sm text-green-600">
                    Created: {seedResult.summary.admins.created} | 
                    Existing: {seedResult.summary.admins.existing} | 
                    Errors: {seedResult.summary.admins.errors}
                  </p>
                </div>
                <div className="text-center">
                  <h4 className="font-semibold text-green-700">Students</h4>
                  <p className="text-sm text-green-600">
                    Created: {seedResult.summary.students.created} | 
                    Existing: {seedResult.summary.students.existing} | 
                    Errors: {seedResult.summary.students.errors}
                  </p>
                </div>
              </div>
            </div>

            {/* Test Accounts */}
            <div className="bg-blue-50 border-2 border-blue-300 p-6">
              <h3 className="text-lg font-bold text-blue-800 mb-4">Test Accounts</h3>
              
              <div className="grid md:grid-cols-2 gap-6">
                {/* Teacher Accounts */}
                <div>
                  <h4 className="font-semibold text-blue-700 mb-3">👨‍🏫 Teacher Accounts</h4>
                  <div className="space-y-2">
                    {seedResult.testAccounts.teachers.map((teacher, index) => (
                      <div key={index} className="bg-white p-3 border border-blue-200">
                        <p className="font-mono text-sm">
                          <strong>Email:</strong> {teacher.email}<br />
                          <strong>Password:</strong> {teacher.password}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Admin Accounts */}
                <div>
                  <h4 className="font-semibold text-blue-700 mb-3">👨‍💼 Admin Accounts</h4>
                  <div className="space-y-2">
                    {seedResult.testAccounts.admins.map((admin, index) => (
                      <div key={index} className="bg-white p-3 border border-blue-200">
                        <p className="font-mono text-sm">
                          <strong>Email:</strong> {admin.email}<br />
                          <strong>Password:</strong> {admin.password}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Detailed Results */}
            <div className="bg-gray-50 border-2 border-gray-300 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">Detailed Results</h3>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {seedResult.results.map((result, index) => (
                  <div key={index} className={`p-2 text-sm ${
                    result.status === 'created' ? 'bg-green-100 text-green-800' :
                    result.status === 'already_exists' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    <strong>{result.type}:</strong> {result.email || result.lrn} - {result.status}
                    {result.error && <span className="block text-xs">{result.error}</span>}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Important Notice */}
        {seedResult && (
          <div className="bg-orange-50 border-2 border-orange-300 p-6 mt-8">
            <h3 className="text-lg font-bold text-orange-800 mb-4">⚠️ Important Next Step</h3>
            <div className="space-y-3 text-orange-700">
              <p>
                <strong>Firestore data created successfully!</strong> However, you still need to create
                <strong> Firebase Authentication users</strong> to enable login.
              </p>
              <p>
                The seeder created user profiles in Firestore, but login requires both:
              </p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>✅ Firestore documents (user data) - <strong>Done</strong></li>
                <li>🔄 Firebase Auth users (authentication) - <strong>Next step</strong></li>
              </ul>
              <div className="mt-4">
                <Button
                  onClick={() => window.location.href = '/create-auth-users'}
                  variant="filled"
                  theme="primary"
                  size="md"
                  radius="none"
                >
                  Create Auth Users →
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-white border-2 border-gray-300 p-6 mt-8">
          <h3 className="text-lg font-bold text-gray-800 mb-4">Complete Setup Process</h3>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-start space-x-2">
              <span className="font-bold text-green-600">1.</span>
              <div>
                <strong>Seed Firestore Data:</strong> Click "Seed Users" above to create user profiles
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <span className="font-bold text-orange-600">2.</span>
              <div>
                <strong>Create Auth Users:</strong> Visit <code>/create-auth-users</code> to enable login
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <span className="font-bold text-blue-600">3.</span>
              <div>
                <strong>Test Login:</strong> Use the generated credentials to test dashboards
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <span className="font-bold text-purple-600">4.</span>
              <div>
                <strong>Refactor Dashboards:</strong> Build features with real test data
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="text-center mt-8">
          <Button
            onClick={() => window.location.href = '/'}
            variant="outlined"
            theme="primary"
            size="md"
            radius="none"
          >
            ← Back to Home
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SeedUsersPage;
