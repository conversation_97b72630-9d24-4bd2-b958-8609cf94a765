import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from '../firebase';

export interface TeacherSubjectAssignment {
  id: string;
  teacherId: string;
  subjectId: string;
  sectionId: string;
  gradeId: string;
  schoolYear: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface TeacherAssignmentData {
  teacherId: string;
  subjectId: string;
  sectionId: string;
  gradeId: string;
  schoolYear?: string;
}

export interface TeacherAssignmentWithDetails extends TeacherSubjectAssignment {
  subjectName?: string;
  sectionName?: string;
  gradeName?: string;
  teacherName?: string;
}

class TeacherAssignmentService {
  private collection = collection(db, 'teacher_subject_assignments');

  // Get all assignments
  async getAllAssignments(): Promise<TeacherSubjectAssignment[]> {
    try {
      const snapshot = await getDocs(
        query(this.collection, orderBy('createdAt', 'desc'))
      );
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as TeacherSubjectAssignment[];
    } catch (error) {
      console.error('Error fetching assignments:', error);
      throw new Error('Failed to fetch assignments');
    }
  }

  // Get assignments by teacher
  async getAssignmentsByTeacher(teacherId: string): Promise<TeacherSubjectAssignment[]> {
    try {
      const snapshot = await getDocs(
        query(
          this.collection,
          where('teacherId', '==', teacherId),
          orderBy('createdAt', 'desc')
        )
      );
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as TeacherSubjectAssignment[];
    } catch (error) {
      console.error('Error fetching teacher assignments:', error);
      throw new Error('Failed to fetch teacher assignments');
    }
  }

  // Get assignments by section
  async getAssignmentsBySection(sectionId: string): Promise<TeacherSubjectAssignment[]> {
    try {
      const snapshot = await getDocs(
        query(
          this.collection,
          where('sectionId', '==', sectionId),
          orderBy('createdAt', 'desc')
        )
      );
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as TeacherSubjectAssignment[];
    } catch (error) {
      console.error('Error fetching section assignments:', error);
      throw new Error('Failed to fetch section assignments');
    }
  }

  // Get assignments by subject
  async getAssignmentsBySubject(subjectId: string): Promise<TeacherSubjectAssignment[]> {
    try {
      const snapshot = await getDocs(
        query(
          this.collection,
          where('subjectId', '==', subjectId),
          orderBy('createdAt', 'desc')
        )
      );
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as TeacherSubjectAssignment[];
    } catch (error) {
      console.error('Error fetching subject assignments:', error);
      throw new Error('Failed to fetch subject assignments');
    }
  }

  // Create new assignment
  async createAssignment(assignmentData: TeacherAssignmentData): Promise<string> {
    try {
      const now = Timestamp.now();
      const currentYear = new Date().getFullYear();
      const schoolYear = assignmentData.schoolYear || `${currentYear}-${currentYear + 1}`;
      
      const newAssignment = {
        ...assignmentData,
        schoolYear,
        createdAt: now,
        updatedAt: now
      };

      const docRef = await addDoc(this.collection, newAssignment);
      return docRef.id;
    } catch (error) {
      console.error('Error creating assignment:', error);
      throw error;
    }
  }

  // Create multiple assignments (batch)
  async createMultipleAssignments(assignments: TeacherAssignmentData[]): Promise<void> {
    try {
      const batch = writeBatch(db);
      const now = Timestamp.now();
      const currentYear = new Date().getFullYear();
      const defaultSchoolYear = `${currentYear}-${currentYear + 1}`;

      assignments.forEach(assignmentData => {
        const docRef = doc(this.collection);
        const schoolYear = assignmentData.schoolYear || defaultSchoolYear;
        
        batch.set(docRef, {
          ...assignmentData,
          schoolYear,
          createdAt: now,
          updatedAt: now
        });
      });

      await batch.commit();
    } catch (error) {
      console.error('Error creating multiple assignments:', error);
      throw error;
    }
  }

  // Update assignment
  async updateAssignment(id: string, updates: Partial<TeacherAssignmentData>): Promise<void> {
    try {
      const docRef = doc(this.collection, id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating assignment:', error);
      throw error;
    }
  }

  // Delete assignment
  async deleteAssignment(id: string): Promise<void> {
    try {
      const docRef = doc(this.collection, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting assignment:', error);
      throw error;
    }
  }

  // Delete all assignments for a teacher
  async deleteTeacherAssignments(teacherId: string): Promise<void> {
    try {
      const assignments = await this.getAssignmentsByTeacher(teacherId);
      const batch = writeBatch(db);

      assignments.forEach(assignment => {
        const docRef = doc(this.collection, assignment.id);
        batch.delete(docRef);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error deleting teacher assignments:', error);
      throw error;
    }
  }

  // Check if assignment exists
  async assignmentExists(teacherId: string, subjectId: string, sectionId: string): Promise<boolean> {
    try {
      const snapshot = await getDocs(
        query(
          this.collection,
          where('teacherId', '==', teacherId),
          where('subjectId', '==', subjectId),
          where('sectionId', '==', sectionId)
        )
      );
      
      return !snapshot.empty;
    } catch (error) {
      console.error('Error checking assignment existence:', error);
      throw error;
    }
  }
}

export const teacherAssignmentService = new TeacherAssignmentService();
