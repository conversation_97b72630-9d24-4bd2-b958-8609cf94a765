{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "firebase": "^11.10.0", "firebase-admin": "^13.4.0", "framer-motion": "^12.23.9", "gsap": "^3.13.0", "html-to-image": "^1.11.13", "html5-qrcode": "^2.3.8", "jsqr": "^1.4.0", "lucide-react": "^0.525.0", "next": "15.3.5", "phosphor-react": "^1.4.1", "qrcode": "^1.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "react-loading-skeleton": "^3.5.0", "react-qr-code": "^2.0.18", "react-qr-reader": "^3.0.0-beta-1", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^4.0.10"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20.19.6", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "tailwindcss": "^4", "typescript": "^5"}}