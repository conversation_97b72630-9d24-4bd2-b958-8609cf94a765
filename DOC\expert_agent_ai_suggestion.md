src/
├── app/
│   ├── (auth)/login/
│   ├── (auth)/register/
│   ├── (dashboard)/admin/
│   ├── (dashboard)/teacher/
│   ├── (dashboard)/student/
│   ├── globals.css
│   ├── layout.tsx
│   ├── page.tsx
│   └── favicon.ico
├── components/
│   ├── ui/
│   ├── layout/
│   └── features/
├── lib/
│   ├── auth.ts
│   ├── db.ts
│   └── utils.ts
├── scripts/
│   └── seeders/
├── public/
├── tests/ (or __tests__/ colocated)
├── .env.example
└── README.md