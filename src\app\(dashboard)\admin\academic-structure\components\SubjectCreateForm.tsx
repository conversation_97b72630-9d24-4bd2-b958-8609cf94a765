"use client";
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { useAddSubject } from '@/hooks/useSubjects';
import { useActiveGrades } from '@/hooks/useGrades';

const SUBJECT_COLORS = [
  '#84a98c', '#f4a261', '#e76f51', '#2a9d8f', 
  '#264653', '#e9c46a', '#f1faee', '#a8dadc'
];

interface SubjectCreateFormProps {
  onClose: () => void;
  onSuccess: () => void;
  gradeId?: string;
}

export default function SubjectCreateForm({ onClose, onSuccess, gradeId }: SubjectCreateFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    gradeId: gradeId || '',
    description: '',
    color: '#84a98c'
  });

  const addSubjectMutation = useAddSubject();
  const { data: grades = [], isLoading: gradesLoading } = useActiveGrades();

  // Auto-generate subject code when name or grade changes
  useEffect(() => {
    if (formData.name && formData.gradeId) {
      const selectedGrade = grades.find(g => g.id === formData.gradeId);
      if (selectedGrade) {
        const nameCode = formData.name
          .split(' ')
          .map(word => word.charAt(0).toUpperCase())
          .join('')
          .substring(0, 4);
        const gradeLevel = selectedGrade.level;
        setFormData(prev => ({ ...prev, code: `${nameCode}${gradeLevel}` }));
      }
    }
  }, [formData.name, formData.gradeId, grades]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await addSubjectMutation.mutateAsync({
        name: formData.name,
        code: formData.code,
        gradeId: formData.gradeId,
        description: formData.description,
        color: formData.color,
        isActive: true
      });
      onSuccess();
    } catch (error) {
      // Error handled by mutation
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Grade Selection - Only show if no gradeId is pre-selected */}
      {!gradeId && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Grade Level *
          </label>
          {gradesLoading ? (
            <div className="text-gray-500">Loading grades...</div>
          ) : (
            <div className="space-y-2">
              {grades.map(grade => (
                <label key={grade.id} className="flex items-center space-x-3 p-3 border-2 border-gray-200 hover:border-[#84a98c] hover:bg-[#f7faf8] transition-colors cursor-pointer">
                  <input
                    type="radio"
                    name="gradeId"
                    value={grade.id}
                    checked={formData.gradeId === grade.id}
                    onChange={(e) => setFormData(prev => ({ ...prev, gradeId: e.target.value }))}
                    className="w-4 h-4 text-[#84a98c] border-2 border-gray-300 focus:ring-[#84a98c] focus:ring-2"
                    required
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{grade.name}</div>
                    <div className="text-sm text-gray-600">Level {grade.level}</div>
                  </div>
                </label>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Subject Name */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Subject Name *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          className="w-full px-3 py-2 border-2 border-gray-300 focus:outline-none focus:border-[#84a98c] text-gray-900 placeholder-gray-600 bg-white transition-colors"
          placeholder="e.g., Mathematics, Science, English"
          required
        />
      </div>

      {/* Subject Code (Auto-generated) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Subject Code
        </label>
        <input
          type="text"
          value={formData.code}
          onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
          className="w-full px-3 py-2 border-2 border-gray-300 focus:outline-none focus:border-[#84a98c] font-mono text-gray-900 placeholder-gray-600 bg-white transition-colors"
          placeholder="Auto-generated"
        />
        <p className="text-xs text-gray-500 mt-1">
          Auto-generated based on subject name and grade level
        </p>
      </div>

      {/* Description */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Description
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          className="w-full px-3 py-2 border-2 border-gray-300 focus:outline-none focus:border-[#84a98c] text-gray-900 placeholder-gray-600 bg-white transition-colors resize-none"
          rows={3}
          placeholder="Brief description of the subject"
        />
      </div>

      {/* Color Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Color
        </label>
        <div className="flex space-x-2">
          {SUBJECT_COLORS.map(color => (
            <button
              key={color}
              type="button"
              onClick={() => setFormData(prev => ({ ...prev, color }))}
              className={`w-8 h-8 rounded-full border-2 transition-all cursor-pointer ${
                formData.color === color
                  ? 'border-gray-800 scale-110'
                  : 'border-gray-300 hover:border-gray-500'
              }`}
              style={{ backgroundColor: color }}
            />
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-3 pt-4">
        <Button
          type="button"
          onClick={onClose}
          variant="outlined"
          theme="neutral"
          size="sm"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="filled"
          theme="primary"
          size="sm"
          loading={addSubjectMutation.isPending}
        >
          Create Subject
        </Button>
      </div>
    </form>
  );
}
