STUDENT PANEL (For Taking Exams/Activities):
Student logs in by scanning a QR code.

Student is automatically redirected to their dashboard.
Dashboard displays the list of enrolled subjects.
Student clicks a subject to view available exams and quizzes.
Student selects and begins the exam or quiz.
Student reviews answers before submission.
Student submits the exam/quiz.
Results/scores are shown immediately after submission.
Correct answers are displayed for any incorrect responses.
Student provides feedback on their performance.
The system responds to the feedback by displaying all scores and computed grades.

TEACHER PANEL (For Creating and Grading Exams/Activities):
Teacher logs in using their email and password.

Teacher accesses their dashboard.
Teacher creates exam questionnaires using:
HOTS (Higher Order Thinking Skills)
LOTS (Lower Order Thinking Skills)
Teacher enters correct answers for objective questions to enable automatic grading.
Teacher submits the exam for principal’s approval.
If approved, the exam is posted; if not, it is revised or recreated.
Teacher monitors student performance and progress.
Teacher manually checks and grades essay-type questions.

ADMIN PANEL (For User and Exam Management):

<PERSON><PERSON> creates student accounts (with QR codes).
<PERSON><PERSON> creates teacher accounts (with email and password).
<PERSON>min reviews and approves teacher-submitted exam questionnaires.
Admin monitors all user activity and account management.

SYSTEM FEATURES:
Automatically saves student answers during exams.

Detects if a student opens another browser tab while taking an exam.

Essay questions are manually graded by teachers.

Correct answers are revealed for each incorrect response.

Exams must be approved by the principal before being posted.

The system is accessible on smartphones, tablets, and computers.

Students can submit feedback on their grades.

The system replies to feedback with a detailed breakdown of scores and computed grades.

Students log in via QR code.

Teachers log in using email and password.