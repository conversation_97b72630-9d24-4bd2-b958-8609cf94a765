import { NextRequest, NextResponse } from 'next/server';
import { db } from '../../../../lib/firebase';
import { collection, getDocs, orderBy, query } from 'firebase/firestore';

export async function GET(request: NextRequest) {
  try {
    console.log('Fetching teachers from Firestore...');
    
    // Fetch all teachers
    const teachersSnapshot = await getDocs(
      query(collection(db, 'teachers'), orderBy('lastName', 'asc'))
    );

    const teachers = teachersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    console.log(`Found ${teachers.length} teachers`);

    return NextResponse.json({
      success: true,
      data: teachers,
      count: teachers.length
    });

  } catch (error) {
    console.error('Error fetching teachers:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch teachers',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
