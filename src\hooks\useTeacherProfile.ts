import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../app/contexts/AuthContext';
import { teacherService, Teacher, TeacherFormData } from '../lib/services/teacherService';
import { teacherAssignmentService, TeacherAssignmentWithDetails } from '../lib/services/teacherAssignmentService';
import toast from 'react-hot-toast';

export interface TeacherProfileUpdateData {
  firstName: string;
  lastName: string;
  middleName?: string;
  email: string;
}

/**
 * Hook to get current teacher profile
 */
export function useTeacherProfile() {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: ['teacherProfile', user?.email],
    queryFn: async () => {
      if (!user?.email) {
        throw new Error('No user email available');
      }
      return await teacherService.getTeacherByEmail(user.email);
    },
    enabled: !!user?.email,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });
}

/**
 * Hook to get teacher assignments (sections and subjects they teach)
 */
export function useTeacherAssignments() {
  const { data: teacherProfile } = useTeacherProfile();
  
  return useQuery({
    queryKey: ['teacherAssignments', teacherProfile?.id],
    queryFn: async () => {
      if (!teacherProfile?.id) {
        throw new Error('No teacher ID available');
      }
      return await teacherAssignmentService.getAssignmentsByTeacher(teacherProfile.id);
    },
    enabled: !!teacherProfile?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get teacher assignments with full details (grade, section, subject names)
 */
export function useTeacherAssignmentsWithDetails() {
  const { data: teacherProfile } = useTeacherProfile();
  
  return useQuery({
    queryKey: ['teacherAssignmentsWithDetails', teacherProfile?.id],
    queryFn: async () => {
      if (!teacherProfile?.id) {
        throw new Error('No teacher ID available');
      }
      return await teacherAssignmentService.getAssignmentsWithDetailsByTeacher(teacherProfile.id);
    },
    enabled: !!teacherProfile?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to update teacher profile
 */
export function useUpdateTeacherProfile() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<TeacherFormData> }) => {
      await teacherService.updateTeacher(id, data);
    },
    onSuccess: () => {
      // Invalidate and refetch teacher profile
      queryClient.invalidateQueries({ queryKey: ['teacherProfile', user?.email] });
      toast.success('Profile updated successfully!');
    },
    onError: (error: any) => {
      console.error('Error updating teacher profile:', error);
      toast.error('Failed to update profile. Please try again.');
    }
  });
}

/**
 * Hook to get teacher display name
 */
export function useTeacherDisplayName() {
  const { data: teacherProfile, isLoading } = useTeacherProfile();
  
  const displayName = teacherProfile 
    ? `${teacherProfile.firstName} ${teacherProfile.lastName}`
    : null;
    
  return {
    displayName,
    isLoading,
    teacherProfile
  };
}

/**
 * Hook to get teacher sections grouped by grade
 */
export function useTeacherSectionsByGrade() {
  const { data: assignments = [], isLoading } = useTeacherAssignmentsWithDetails();
  
  // Group assignments by grade and section
  const sectionsByGrade = assignments.reduce((acc, assignment) => {
    const gradeKey = assignment.gradeId;
    const gradeName = assignment.gradeName || `Grade ${assignment.gradeId}`;
    
    if (!acc[gradeKey]) {
      acc[gradeKey] = {
        gradeId: gradeKey,
        gradeName,
        sections: {}
      };
    }
    
    const sectionKey = assignment.sectionId;
    const sectionName = assignment.sectionName || `Section ${assignment.sectionId}`;
    
    if (!acc[gradeKey].sections[sectionKey]) {
      acc[gradeKey].sections[sectionKey] = {
        sectionId: sectionKey,
        sectionName,
        subjects: []
      };
    }
    
    acc[gradeKey].sections[sectionKey].subjects.push({
      subjectId: assignment.subjectId,
      subjectName: assignment.subjectName || 'Unknown Subject'
    });
    
    return acc;
  }, {} as Record<string, {
    gradeId: string;
    gradeName: string;
    sections: Record<string, {
      sectionId: string;
      sectionName: string;
      subjects: Array<{
        subjectId: string;
        subjectName: string;
      }>;
    }>;
  }>);
  
  return {
    sectionsByGrade: Object.values(sectionsByGrade),
    isLoading,
    assignments
  };
}
