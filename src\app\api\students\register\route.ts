import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/firebase";
import { doc, setDoc } from "firebase/firestore";

export async function POST(req: NextRequest) {
  try {
    const { firstName, middleName, lastName, extension, lrn, qrUrl, hash } = await req.json();
    if (!firstName || !lastName || !lrn || !hash) {
      return NextResponse.json({ message: "Missing required fields." }, { status: 400 });
    }
    const studentDoc = doc(db, "students", lrn);
    await setDoc(studentDoc, {
      firstName,
      middleName: middleName || "",
      lastName,
      extension: extension || "",
      lrn,
      qrUrl: qrUrl || "",
      hash,
    });
    return NextResponse.json({ message: "Student registered successfully." }, { status: 200 });
  } catch (err) {
    return NextResponse.json({ message: "Failed to register student." }, { status: 500 });
  }
} 