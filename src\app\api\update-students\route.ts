import { NextRequest, NextResponse } from 'next/server';
import { db } from '../../../lib/firebase';
import { collection, getDocs, doc, updateDoc } from 'firebase/firestore';

export async function POST(request: NextRequest) {
  try {
    console.log('Starting student data update with correct grade and section IDs...');
    
    // Student updates with correct grade and section IDs
    const studentUpdates = [
      {
        lrn: '129848291239',
        gradeId: 'GP1teEsjsjW3smrdCDFy', // Grade 4
        sectionId: 'yVoiUPc2NnL47V8YAzEg', // Grade 4 - Section B
      },
      {
        lrn: '123456789013',
        gradeId: 'I156fMwTdlWQPd7xIGJo', // Grade 6
        sectionId: 'LO0KyUMmFKX0NveHw1xu', // Grade 6 - Section A
      },
      {
        lrn: '123456789014',
        gradeId: 'voPxLDpaEnHBewbCfauD', // Grade 5
        sectionId: 'eBpLSlUEdbpLzSwGoXoc', // Grade 5 - Section A
      },
      {
        lrn: '123456789015',
        gradeId: 'GP1teEsjsjW3smrdCDFy', // Grade 4
        sectionId: 'BD6jmHpeGYk2EvDyegiH', // Grade 4 - Section A
      },
      {
        lrn: '123456789016',
        gradeId: 'voPxLDpaEnHBewbCfauD', // Grade 5
        sectionId: 'hBGVw0TVrAJGQGU5fdcL', // Grade 5 - Section B
      }
    ];

    // Get all students from the database
    const studentsCollection = collection(db, 'students');
    const studentsSnapshot = await getDocs(studentsCollection);
    
    if (studentsSnapshot.empty) {
      return NextResponse.json({
        success: false,
        message: 'No students found in the database'
      }, { status: 404 });
    }

    const results = [];
    
    // Update each student with correct grade and section IDs
    for (const studentDoc of studentsSnapshot.docs) {
      const student = studentDoc.data();
      const studentId = studentDoc.id;
      
      // Find the update data for this student
      const updateData = studentUpdates.find(update => update.lrn === student.lrn);
      
      if (updateData) {
        try {
          console.log(`Updating student: ${student.firstName} ${student.lastName} (LRN: ${student.lrn})`);
          
          // Update student record with correct grade and section IDs
          const studentRef = doc(db, 'students', studentId);
          await updateDoc(studentRef, {
            gradeId: updateData.gradeId,
            sectionId: updateData.sectionId,
            updatedAt: new Date().toISOString(),
          });

          results.push({
            studentId,
            lrn: student.lrn,
            name: `${student.firstName} ${student.lastName}`,
            status: 'updated',
            gradeId: updateData.gradeId,
            sectionId: updateData.sectionId
          });
          
          console.log(`Successfully updated ${student.firstName} ${student.lastName}`);
        } catch (error) {
          console.error(`Error updating student ${studentId}:`, error);
          results.push({
            studentId,
            lrn: student.lrn,
            name: `${student.firstName} ${student.lastName}`,
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      } else {
        results.push({
          studentId,
          lrn: student.lrn,
          name: `${student.firstName} ${student.lastName}`,
          status: 'skipped',
          reason: 'No update data found for this LRN'
        });
      }
    }

    const updatedCount = results.filter(r => r.status === 'updated').length;
    const errorCount = results.filter(r => r.status === 'error').length;
    const skippedCount = results.filter(r => r.status === 'skipped').length;

    console.log(`Student update completed. Updated: ${updatedCount}, Errors: ${errorCount}, Skipped: ${skippedCount}`);

    return NextResponse.json({
      success: true,
      message: `Student update completed. ${updatedCount} updated, ${errorCount} errors, ${skippedCount} skipped.`,
      results,
      summary: {
        total: results.length,
        updated: updatedCount,
        errors: errorCount,
        skipped: skippedCount
      }
    });
  } catch (error) {
    console.error('Student update error:', error);
    return NextResponse.json({
      success: false,
      message: 'Error during student update',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Student Update API',
    usage: 'Send a POST request to this endpoint to update students with correct grade and section IDs',
    description: 'This endpoint will update all students with the correct grade and section IDs from the database'
  });
}
