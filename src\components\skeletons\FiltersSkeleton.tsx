"use client";
import React from 'react';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

interface FiltersSkeletonProps {
  filterCount?: number;
}

export default function FiltersSkeleton({ filterCount = 2 }: FiltersSkeletonProps) {
  return (
    <SkeletonTheme baseColor="#f3f4f6" highlightColor="#e5e7eb">
      <div className="bg-white border-2 border-[#84a98c] p-6 shadow-sm">
        <div className={`grid grid-cols-1 md:grid-cols-${filterCount} gap-4`}>
          {Array.from({ length: filterCount }).map((_, index) => (
            <Skeleton key={index} height={40} />
          ))}
        </div>
        <div className="mt-4">
          <Skeleton height={16} width={150} />
        </div>
      </div>
    </SkeletonTheme>
  );
}
