"use client";
import React, { useState } from 'react';
import { AcademicCapIcon, PlusIcon, MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { Button } from '../../../../components/ui/Button';
import Select from '../../../../components/ui/Select';


// Import hooks and types
import { useStudents } from '@/hooks/useStudents';
import { useGradesWithSections } from '@/hooks/useGrades';

// Import components
import StudentCard from './components/StudentCard';
import AddStudentModal from './components/AddStudentModal';
import StudentViewModal from './components/StudentViewModal';
import StudentEditModal from './components/StudentEditModal';
import StudentQRModal from './components/StudentQRModal';
import { CardSkeleton, PageHeaderSkeleton, FiltersSkeleton } from '../../../../components/skeletons';

export default function StudentsPage() {
  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSection, setSelectedSection] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showQRModal, setShowQRModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<any>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const studentsPerPage = 9;

  // Hooks
  const { data: students = [], isLoading, error } = useStudents();
  const { data: gradesWithSections = [] } = useGradesWithSections();

  // Get all sections with grade info
  const allSections = gradesWithSections.flatMap(grade =>
    grade.sections?.map(section => ({
      ...section,
      gradeName: grade.name,
      gradeId: grade.id
    })) || []
  );

  // Filter students based on search and section
  const filteredStudents = students.filter(student => {
    // Handle both old and new student data structures
    let fullName = '';
    if (student.fullName && Array.isArray(student.fullName)) {
      // New structure: fullName is an array
      fullName = student.fullName.join(' ').toLowerCase();
    } else if (student.firstName) {
      // Old structure: individual name fields
      fullName = `${student.firstName || ''} ${student.middleName || ''} ${student.lastName || ''}`.toLowerCase();
    }

    const matchesSearch = searchTerm === '' ||
      fullName.includes(searchTerm.toLowerCase()) ||
      student.lrn.includes(searchTerm) ||
      student.studentId?.includes(searchTerm);

    const matchesSection = selectedSection === '' || student.sectionId === selectedSection;

    return matchesSearch && matchesSection;
  });

  // Pagination calculations
  const totalStudents = filteredStudents.length;
  const totalPages = Math.ceil(totalStudents / studentsPerPage);
  const startIndex = (currentPage - 1) * studentsPerPage;
  const endIndex = startIndex + studentsPerPage;
  const currentStudents = filteredStudents.slice(startIndex, endIndex);

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedSection]);

  // Handle actions
  const handleViewDetails = (lrn: string) => {
    const student = students.find(s => s.lrn === lrn);
    setSelectedStudent(student);
    setShowViewModal(true);
  };

  const handleEditStudent = (lrn: string) => {
    const student = students.find(s => s.lrn === lrn);
    setSelectedStudent(student);
    setShowEditModal(true);
  };

  const handleViewQR = (lrn: string) => {
    const student = students.find(s => s.lrn === lrn);
    if (student) {
      setSelectedStudent(student);
      setShowQRModal(true);
    }
  };

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-red-600">Error loading students. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
      <div className="space-y-6">
        {/* Header */}
        {isLoading ? (
          <PageHeaderSkeleton />
        ) : (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <AcademicCapIcon className="w-8 h-8 text-[#84a98c]" />
              <div>
                <h2 className="text-2xl font-bold text-[#84a98c]">Student Management</h2>
                <p className="text-sm text-gray-600">Manage student accounts and information</p>
              </div>
            </div>

            <Button
              onClick={() => setShowAddModal(true)}
              variant="filled"
              theme="primary"
              size="md"
              radius="none"
              leftIcon={<PlusIcon className="w-4 h-4" />}
            >
              Add Student
            </Button>
          </div>
        )}

        {/* Filters and Search */}
        {isLoading ? (
          <FiltersSkeleton filterCount={2} />
        ) : (
          <div className="bg-white border-2 border-[#84a98c] p-6 shadow-sm">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by name or LRN..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-[#84a98c] bg-white text-gray-900 placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-[#84a98c]"
                />
              </div>

              <Select
                value={selectedSection}
                onChange={(e) => setSelectedSection(e.target.value)}
                variant="primary"
              >
                <option value="">All Sections</option>
                {allSections.map(section => (
                  <option key={section.id} value={section.id}>
                    {section.gradeName} - {section.name}
                  </option>
                ))}
              </Select>
            </div>

            <div className="mt-4 text-sm text-gray-600">
              Showing {startIndex + 1}-{Math.min(endIndex, totalStudents)} of {totalStudents} students
              {totalStudents !== students.length && ` (filtered from ${students.length} total)`}
            </div>
          </div>
        )}



        {/* Students Cards Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <CardSkeleton count={9} borderColor="student" />
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {currentStudents.map((student) => (
              <StudentCard
                key={student.id}
                student={student}
                onView={() => handleViewDetails(student.lrn)}
                onEdit={() => handleEditStudent(student.lrn)}
                onViewQR={() => handleViewQR(student.lrn)}
              />
            ))}
          </div>
        )}

        {/* Empty State */}
        {!isLoading && totalStudents === 0 && (
          <div className="text-center py-12">
            <AcademicCapIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-500 mb-2">No students found</h3>
            <p className="text-gray-400">
              {searchTerm || selectedSection
                ? "Try adjusting your search criteria."
                : "Get started by adding your first student."}
            </p>
          </div>
        )}

        {/* Pagination Controls */}
        {!isLoading && totalStudents > 0 && totalPages > 1 && (
          <div className="flex items-center justify-between bg-white border-2 border-[#84a98c] p-4">
            <div className="text-sm text-gray-600">
              Page {currentPage} of {totalPages}
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="border-[#84a98c] text-[#84a98c] hover:bg-[#84a98c] hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </Button>

              {/* Page Numbers */}
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`px-3 py-1 text-sm border ${
                        currentPage === pageNum
                          ? 'bg-[#84a98c] text-white border-[#84a98c]'
                          : 'bg-white text-[#84a98c] border-[#84a98c] hover:bg-[#84a98c] hover:text-white'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="border-[#84a98c] text-[#84a98c] hover:bg-[#84a98c] hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </Button>
            </div>
          </div>
        )}

      {/* Modals */}
      <AddStudentModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={() => setShowAddModal(false)}
      />

      <StudentViewModal
        student={selectedStudent}
        isOpen={showViewModal}
        onClose={() => setShowViewModal(false)}
      />

      <StudentEditModal
        student={selectedStudent}
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSuccess={() => setShowEditModal(false)}
      />

      <StudentQRModal
        student={selectedStudent}
        isOpen={showQRModal}
        onClose={() => setShowQRModal(false)}
      />
      </div>
  );
}
