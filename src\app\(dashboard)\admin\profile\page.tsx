"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { useAdminProfile, useUpdateAdminProfile } from '@/hooks/useAdminProfile';
import { AdminProfileUpdateData } from '@/lib/services/adminService';
import toast from 'react-hot-toast';

export default function ProfilePage() {
  const { data: adminProfile, isLoading, error } = useAdminProfile();
  const updateProfileMutation = useUpdateAdminProfile();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<AdminProfileUpdateData>({
    firstName: '',
    lastName: '',
    middleName: '',
    phoneNumber: '',
    address: '',
    position: '',
    department: ''
  });

  // Initialize form data when profile loads
  React.useEffect(() => {
    if (adminProfile) {
      setFormData({
        firstName: adminProfile.firstName || '',
        lastName: adminProfile.lastName || '',
        middleName: adminProfile.middleName || '',
        phoneNumber: adminProfile.phoneNumber || '',
        address: adminProfile.address || '',
        position: adminProfile.position || '',
        department: adminProfile.department || ''
      });
    }
  }, [adminProfile]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async () => {
    if (!adminProfile) return;

    try {
      await updateProfileMutation.mutateAsync({
        id: adminProfile.id,
        data: formData
      });
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  const handleCancel = () => {
    if (adminProfile) {
      setFormData({
        firstName: adminProfile.firstName || '',
        lastName: adminProfile.lastName || '',
        middleName: adminProfile.middleName || '',
        phoneNumber: adminProfile.phoneNumber || '',
        address: adminProfile.address || '',
        position: adminProfile.position || '',
        department: adminProfile.department || ''
      });
    }
    setIsEditing(false);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#FAFAF6] flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-[#84a98c] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-[#84a98c] font-medium">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (error || !adminProfile) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <svg className="w-12 h-12 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Profile</h3>
          <p className="text-gray-600">Unable to load your profile information. Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-none flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Profile</h2>
            <p className="text-gray-600">Manage your account information</p>
          </div>
        </div>
        
        <div className="flex space-x-2">
          {isEditing ? (
            <>
              <Button
                variant="outlined"
                theme="neutral"
                onClick={handleCancel}
                disabled={updateProfileMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                variant="filled"
                theme="success"
                onClick={handleSave}
                disabled={updateProfileMutation.isPending}
              >
                {updateProfileMutation.isPending ? 'Saving...' : 'Save Changes'}
              </Button>
            </>
          ) : (
            <Button
              variant="filled"
              theme="primary"
              onClick={() => setIsEditing(true)}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Edit Profile
            </Button>
          )}
        </div>
      </div>

      {/* Profile Information */}
      <div className="space-y-6">
        {/* Personal Information */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Personal Information</h3>
            
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  First Name
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                  />
                ) : (
                  <p className="text-gray-900 py-2">{adminProfile.firstName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Last Name
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                  />
                ) : (
                  <p className="text-gray-900 py-2">{adminProfile.lastName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Middle Name
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    name="middleName"
                    value={formData.middleName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                  />
                ) : (
                  <p className="text-gray-900 py-2">{adminProfile.middleName || 'Not specified'}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <p className="text-gray-900 py-2">{adminProfile.email}</p>
                <p className="text-xs text-gray-500">Email cannot be changed</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                {isEditing ? (
                  <input
                    type="tel"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                  />
                ) : (
                  <p className="text-gray-900 py-2">{adminProfile.phoneNumber || 'Not specified'}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Address
                </label>
                {isEditing ? (
                  <textarea
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                  />
                ) : (
                  <p className="text-gray-900 py-2">{adminProfile.address || 'Not specified'}</p>
                )}
            </div>
          </div>
        </Card>

        {/* Work Information */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Work Information</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Position
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    name="position"
                    value={formData.position}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                  />
                ) : (
                  <p className="text-gray-900 py-2">{adminProfile.position}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Department
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    name="department"
                    value={formData.department}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                  />
                ) : (
                  <p className="text-gray-900 py-2">{adminProfile.department}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Employee ID
                </label>
                <p className="text-gray-900 py-2">{adminProfile.employeeId}</p>
                <p className="text-xs text-gray-500">Employee ID cannot be changed</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Date Hired
                </label>
                <p className="text-gray-900 py-2">
                  {adminProfile.dateHired ? new Date(adminProfile.dateHired).toLocaleDateString() : 'Not specified'}
                </p>
            </div>
          </div>
        </Card>

        {/* Account Details */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Account Details</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Role
                </label>
                <p className="text-gray-900 py-2 capitalize">
                  {adminProfile.role.replace('_', ' ')}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  adminProfile.status === 'active'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {adminProfile.status}
                </span>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Permissions
                </label>
                <div className="flex flex-wrap gap-2">
                  {adminProfile.permissions.map((permission) => (
                    <span
                      key={permission}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {permission.replace('_', ' ')}
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Account Created
                </label>
                <p className="text-gray-900 py-2">
                  {adminProfile.createdAt?.toDate().toLocaleDateString()}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Last Updated
                </label>
                <p className="text-gray-900 py-2">
                  {adminProfile.updatedAt?.toDate().toLocaleDateString()}
                </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
