const fs = require('fs');
const path = require('path');

// Read the firestore data
const dataPath = path.join(__dirname, 'firestore-data.json');
const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));

console.log('🔍 ANALYZING TEACHER ASSIGNMENT COLLECTIONS\n');
console.log('=' .repeat(60));

// Analyze teacherAssignments collection
console.log('\n📋 TEACHER ASSIGNMENTS COLLECTION (24 documents):');
console.log('Structure analysis:');
if (data.teacherAssignments.length > 0) {
  const sample = data.teacherAssignments[0];
  console.log('Sample document structure:');
  console.log(JSON.stringify(sample, null, 2));
  
  console.log('\nAll fields used:');
  const allFields = new Set();
  data.teacherAssignments.forEach(doc => {
    Object.keys(doc).forEach(key => allFields.add(key));
  });
  console.log(Array.from(allFields).join(', '));
}

// Analyze teacher_subject_assignments collection
console.log('\n📚 TEACHER_SUBJECT_ASSIGNMENTS COLLECTION (7 documents):');
console.log('Structure analysis:');
if (data.teacher_subject_assignments.length > 0) {
  const sample = data.teacher_subject_assignments[0];
  console.log('Sample document structure:');
  console.log(JSON.stringify(sample, null, 2));
  
  console.log('\nAll fields used:');
  const allFields = new Set();
  data.teacher_subject_assignments.forEach(doc => {
    Object.keys(doc).forEach(key => allFields.add(key));
  });
  console.log(Array.from(allFields).join(', '));
}

// Check which collection is being used in the codebase
console.log('\n🔍 CHECKING CODEBASE USAGE...');
console.log('This requires manual inspection of the codebase.');

console.log('\n📊 RECOMMENDATION:');
console.log('Based on the data:');
console.log(`- teacherAssignments: ${data.teacherAssignments.length} documents`);
console.log(`- teacher_subject_assignments: ${data.teacher_subject_assignments.length} documents`);

if (data.teacherAssignments.length > data.teacher_subject_assignments.length) {
  console.log('\n✅ RECOMMENDATION: Keep "teacherAssignments" (more complete data)');
  console.log('❌ RECOMMENDATION: Remove "teacher_subject_assignments" (less data)');
} else {
  console.log('\n✅ RECOMMENDATION: Keep "teacher_subject_assignments" (more complete data)');
  console.log('❌ RECOMMENDATION: Remove "teacherAssignments" (less data)');
}

console.log('\n' + '='.repeat(60));
