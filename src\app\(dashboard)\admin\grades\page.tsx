"use client";
import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { useGradesWithSections } from '@/hooks/useGrades';
import { useSubjects } from '@/hooks/useSubjects';
import { PlusIcon, AcademicCapIcon, BookOpenIcon, UserGroupIcon } from '@heroicons/react/24/outline';
import GradeForm from './components/GradeForm';
import GradeEditModal from './components/GradeEditModal';
import SectionManagement from './components/SectionManagement';

interface Grade {
  id: string;
  name: string;
  level: number;
  isActive: boolean;
  sections?: Section[];
}

interface Section {
  id: string;
  name: string;
  gradeId: string;
  currentCount: number;
  isActive: boolean;
}

export default function GradeManagementPage() {
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedGrade, setSelectedGrade] = useState<Grade | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showSectionManagement, setShowSectionManagement] = useState(false);
  const [selectedGradeForSections, setSelectedGradeForSections] = useState<Grade | null>(null);

  // Hooks
  const { data: grades = [], isLoading, error } = useGradesWithSections();
  const { data: subjects = [] } = useSubjects();

  // Get subjects count per grade
  const getSubjectsCount = (gradeId: string) => {
    return subjects.filter(subject => subject.gradeId === gradeId).length;
  };

  // Get total students per grade
  const getTotalStudents = (grade: Grade) => {
    return grade.sections?.reduce((total, section) => total + section.currentCount, 0) || 0;
  };

  const handleEditGrade = (grade: Grade) => {
    setSelectedGrade(grade);
    setShowEditModal(true);
  };

  const handleManageSections = (grade: Grade) => {
    setSelectedGradeForSections(grade);
    setShowSectionManagement(true);
  };

  const handleCloseModals = () => {
    setShowAddForm(false);
    setShowEditModal(false);
    setShowSectionManagement(false);
    setSelectedGrade(null);
    setSelectedGradeForSections(null);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#FAFAF6] p-6">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-12 h-12 border-4 border-[#84a98c] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-[#84a98c] font-medium">Loading grades...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#FAFAF6] p-6">
        <div className="max-w-6xl mx-auto">
          <div className="bg-red-50 border border-red-200 p-6 rounded-lg">
            <h2 className="text-red-800 font-semibold mb-2">Error Loading Grades</h2>
            <p className="text-red-600">Failed to load grades. Please try again.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#FAFAF6] p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white border-2 border-[#84a98c] p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <AcademicCapIcon className="w-8 h-8 text-[#84a98c]" />
              <div>
                <h1 className="text-2xl font-bold text-[#84a98c]">Grade Management</h1>
                <p className="text-gray-600">Manage grade levels, sections, and academic structure</p>
              </div>
            </div>
            <Button
              onClick={() => setShowAddForm(true)}
              className="bg-[#84a98c] hover:bg-[#6b8a6e] text-white"
            >
              <PlusIcon className="w-4 h-4 mr-2" />
              Add Grade
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white border-2 border-[#84a98c] p-6">
            <div className="flex items-center space-x-3">
              <AcademicCapIcon className="w-8 h-8 text-[#84a98c]" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Total Grades</h3>
                <p className="text-2xl font-bold text-[#84a98c]">{grades.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white border-2 border-[#84a98c] p-6">
            <div className="flex items-center space-x-3">
              <UserGroupIcon className="w-8 h-8 text-[#84a98c]" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Total Sections</h3>
                <p className="text-2xl font-bold text-[#84a98c]">
                  {grades.reduce((total, grade) => total + (grade.sections?.length || 0), 0)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white border-2 border-[#84a98c] p-6">
            <div className="flex items-center space-x-3">
              <BookOpenIcon className="w-8 h-8 text-[#84a98c]" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Total Subjects</h3>
                <p className="text-2xl font-bold text-[#84a98c]">{subjects.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Grades Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {grades.map((grade) => (
            <div key={grade.id} className="bg-white border-2 border-[#84a98c] p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-bold text-[#84a98c]">{grade.name}</h3>
                <span className={`px-2 py-1 text-xs font-medium rounded ${
                  grade.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {grade.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>

              {/* Grade Statistics */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Sections:</span>
                  <span className="font-medium">{grade.sections?.length || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Subjects:</span>
                  <span className="font-medium">{getSubjectsCount(grade.id)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Students:</span>
                  <span className="font-medium">{getTotalStudents(grade)}</span>
                </div>
              </div>

              {/* Sections Preview */}
              {grade.sections && grade.sections.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Sections:</h4>
                  <div className="flex flex-wrap gap-1">
                    {grade.sections.slice(0, 4).map((section) => (
                      <span 
                        key={section.id}
                        className="px-2 py-1 bg-[#84a98c] text-white text-xs rounded"
                      >
                        {section.name}
                      </span>
                    ))}
                    {grade.sections.length > 4 && (
                      <span className="px-2 py-1 bg-gray-200 text-gray-600 text-xs rounded">
                        +{grade.sections.length - 4} more
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-2">
                <Button
                  onClick={() => handleEditGrade(grade)}
                  variant="outline"
                  className="flex-1 text-[#84a98c] border-[#84a98c]"
                >
                  Edit
                </Button>
                <Button
                  onClick={() => handleManageSections(grade)}
                  className="flex-1 bg-[#84a98c] hover:bg-[#6b8a6e] text-white"
                >
                  Sections
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {grades.length === 0 && (
          <div className="bg-white border-2 border-[#84a98c] p-12 text-center">
            <AcademicCapIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-500 mb-2">No grades found</h3>
            <p className="text-gray-400 mb-4">Get started by creating your first grade level.</p>
            <Button
              onClick={() => setShowAddForm(true)}
              className="bg-[#84a98c] hover:bg-[#6b8a6e] text-white"
            >
              <PlusIcon className="w-4 h-4 mr-2" />
              Add Grade
            </Button>
          </div>
        )}
      </div>

      {/* Modals */}
      {showAddForm && (
        <GradeForm
          onClose={handleCloseModals}
          onSuccess={handleCloseModals}
        />
      )}

      {showEditModal && selectedGrade && (
        <GradeEditModal
          grade={selectedGrade}
          onClose={handleCloseModals}
          onSuccess={handleCloseModals}
        />
      )}

      {showSectionManagement && selectedGradeForSections && (
        <SectionManagement
          grade={selectedGradeForSections}
          onClose={handleCloseModals}
        />
      )}
    </div>
  );
}
