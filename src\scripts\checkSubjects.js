const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  const serviceAccount = require('../serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

async function checkSubjects() {
  try {
    console.log('🔍 Checking Subjects Collection...\n');
    
    const snapshot = await db.collection('subjects').get();
    console.log(`📊 Total subjects: ${snapshot.size}\n`);
    
    if (snapshot.empty) {
      console.log('❌ No subjects found in collection.\n');
      return;
    }
    
    snapshot.forEach((doc, index) => {
      console.log(`📚 Subject ${index + 1} (ID: ${doc.id}):`);
      const data = doc.data();
      console.log(JSON.stringify(data, null, 2));
      console.log('─'.repeat(50));
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkSubjects().then(() => {
  console.log('✅ Check complete!');
  process.exit(0);
}).catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
