import { MagnifyingGlassIcon, BellIcon, UserCircleIcon } from "@heroicons/react/24/outline";

const navy = "#1A2340";
const lightCream = "#FAFAF6";

const Header = () => (
  <header className="flex items-center justify-between w-full px-8 py-6 border-b border-[#1A2340] bg-[#FAFAF6]" style={{ borderRadius: 0 }}>
    <div>
      <h1 className="text-2xl font-bold text-[#1A2340] tracking-tight mb-1">Dashboard</h1>
      <p className="text-sm text-[#1A2340] opacity-70">Plan, prioritize, and accomplish your tasks with ease.</p>
    </div>
    <div className="flex items-center gap-6">
      {/* Search Bar */}
      <div className="relative">
        <input
          type="text"
          placeholder="Search..."
          className="pl-10 pr-4 py-2 border border-[#1A2340] bg-[#FAFAF6] text-[#1A2340] text-sm focus:outline-none focus:ring-2 focus:ring-[#1A2340]"
          aria-label="Search"
          style={{ borderRadius: 0 }}
        />
        <MagnifyingGlassIcon className="w-5 h-5 text-[#1A2340] absolute left-2 top-2.5" aria-hidden="true" />
      </div>
      {/* Quick Actions */}
      <button className="p-2" aria-label="Notifications">
        <BellIcon className="w-6 h-6 text-[#1A2340]" aria-hidden="true" />
      </button>
      <UserCircleIcon className="w-8 h-8 text-[#1A2340]" aria-hidden="true" />
    </div>
  </header>
);

export default Header; 