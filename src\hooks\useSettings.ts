import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { settingsService, SystemSettings, UpdateSettingsData } from '@/lib/services/settingsService';
import toast from 'react-hot-toast';

// Query keys
export const settingsKeys = {
  all: ['settings'] as const,
  system: () => [...settingsKeys.all, 'system'] as const,
};

// Get system settings
export const useSettings = () => {
  return useQuery({
    queryKey: settingsKeys.system(),
    queryFn: () => settingsService.getSettings(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
  });
};

// Update settings mutation
export const useUpdateSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (updates: UpdateSettingsData) => settingsService.updateSettings(updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: settingsKeys.all });
      toast.success('Settings updated successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update settings');
    },
  });
};

// Update single setting mutation
export const useUpdateSetting = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ key, value }: { key: string; value: any }) => 
      settingsService.updateSetting(key, value),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: settingsKeys.all });
      toast.success('Setting updated successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update setting');
    },
  });
};

// Check delete permission hook
export const useDeletePermission = (entityType: 'sections' | 'subjects' | 'grades' | 'students' | 'teachers' | 'exams') => {
  const { data: settings } = useSettings();
  
  if (!settings) return false;
  
  const settingKey = `allowDelete${entityType.charAt(0).toUpperCase() + entityType.slice(1, -1)}s` as keyof SystemSettings;
  return settings[settingKey] as boolean;
};

// Get specific setting value
export const useSetting = (key: keyof SystemSettings) => {
  const { data: settings, ...rest } = useSettings();
  
  return {
    ...rest,
    data: settings ? settings[key] : undefined,
  };
};
