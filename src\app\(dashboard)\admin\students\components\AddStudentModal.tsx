"use client";
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/Button';
import Select from '@/components/ui/Select';
import { useGradesWithSections } from '@/hooks/useGrades';
import { useAddStudent } from '@/hooks/useStudents';
import { StudentFormData } from '@/lib/schemas';

interface AddStudentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
};

const modalVariants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: -50
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.3
    }
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    y: -50,
    transition: {
      duration: 0.2
    }
  }
};

export default function AddStudentModal({ isOpen, onClose, onSuccess }: AddStudentModalProps) {
  const [studentForm, setStudentForm] = useState<StudentFormData>({
    firstName: '',
    middleName: '',
    lastName: '',
    extension: '',
    lrn: '',
    gradeId: '',
    sectionId: '',
    status: 'active'
  });

  const { data: gradesWithSections = [] } = useGradesWithSections();
  const addStudentMutation = useAddStudent();

  // Get sections for selected grade
  const selectedGrade = gradesWithSections.find(g => g.id === studentForm.gradeId);
  const availableSections = selectedGrade?.sections || [];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // LRN validation - only allow numbers and limit to 12 digits
    if (name === 'lrn') {
      const numericValue = value.replace(/\D/g, ''); // Remove non-digits
      if (numericValue.length <= 12) {
        setStudentForm(prev => ({
          ...prev,
          [name]: numericValue
        }));
      }
      return;
    }

    setStudentForm(prev => ({
      ...prev,
      [name]: value,
      // Reset section when grade changes
      ...(name === 'gradeId' && { sectionId: '' })
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate LRN length
    if (studentForm.lrn.length !== 12) {
      alert('LRN must be exactly 12 digits');
      return;
    }

    try {
      await addStudentMutation.mutateAsync(studentForm);
      handleClose();
      onSuccess();
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleClose = () => {
    setStudentForm({
      firstName: '',
      middleName: '',
      lastName: '',
      extension: '',
      lrn: '',
      gradeId: '',
      sectionId: '',
      status: 'active'
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            onClick={handleClose}
          />

          {/* Modal */}
          <motion.div
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed left-[50%] top-[50%] z-50 w-full max-w-3xl max-h-[90vh] translate-x-[-50%] translate-y-[-50%] bg-white border-2 border-[#84a98c] shadow-lg p-6 overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="mb-6">
              <h2 className="text-lg font-semibold leading-none tracking-tight text-[#84a98c] mb-2">Add New Student</h2>
              <p className="text-sm text-gray-600">
                Create a new student account and assign to grade and section.
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-[#84a98c] mb-4">Student Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-[#84a98c] mb-1">First Name *</label>
                    <input
                      type="text"
                      name="firstName"
                      value={studentForm.firstName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#84a98c] mb-1">Last Name *</label>
                    <input
                      type="text"
                      name="lastName"
                      value={studentForm.lastName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#84a98c] mb-1">Middle Name</label>
                    <input
                      type="text"
                      name="middleName"
                      value={studentForm.middleName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#84a98c] mb-1">Extension</label>
                    <input
                      type="text"
                      name="extension"
                      value={studentForm.extension}
                      onChange={handleInputChange}
                      placeholder="Jr., Sr., III"
                      className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                    />
                  </div>
                </div>
              </div>

              {/* Academic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-[#84a98c] mb-4">Academic Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-[#84a98c] mb-1">LRN *</label>
                    <input
                      type="text"
                      name="lrn"
                      value={studentForm.lrn}
                      onChange={handleInputChange}
                      placeholder="12-digit LRN"
                      className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                      required
                      maxLength={12}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      {studentForm.lrn.length}/12 digits
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#84a98c] mb-1">Grade Level *</label>
                    <select
                      name="gradeId"
                      value={studentForm.gradeId}
                      onChange={handleInputChange}
                      className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                      required
                    >
                      <option value="">Select Grade Level</option>
                      {gradesWithSections.map(grade => (
                        <option key={grade.id} value={grade.id}>
                          {grade.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#84a98c] mb-1">Section *</label>
                    <select
                      name="sectionId"
                      value={studentForm.sectionId}
                      onChange={handleInputChange}
                      className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                      required
                      disabled={!studentForm.gradeId}
                    >
                      <option value="">Select Section</option>
                      {availableSections.map(section => (
                        <option key={section.id} value={section.id}>
                          {section.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  type="button"
                  onClick={handleClose}
                  variant="outlined"
                  theme="neutral"
                  disabled={addStudentMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="filled"
                  theme="success"
                  disabled={addStudentMutation.isPending}
                  loading={addStudentMutation.isPending}
                >
                  {addStudentMutation.isPending ? 'Creating...' : 'Create Student'}
                </Button>
              </div>
            </form>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
