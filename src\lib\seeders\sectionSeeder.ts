/**
 * Section Seeder
 * Creates sections for each grade following <PERSON>'s schema
 * 
 * Structure: A, B, C, D sections for each grade
 * No capacity fields - sections are simple organizational units
 */

import { db } from '@/lib/firebase';
import { collection, addDoc, getDocs, deleteDoc, writeBatch, doc, updateDoc } from 'firebase/firestore';
import { Grade, Section } from '@/lib/types/school';

/**
 * Clear all sections from the database
 */
export async function clearSections() {
  console.log('🧹 Clearing existing sections...');
  
  try {
    const sectionsSnapshot = await getDocs(collection(db, 'sections'));
    const batch = writeBatch(db);
    
    sectionsSnapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });
    
    if (sectionsSnapshot.docs.length > 0) {
      await batch.commit();
      console.log(`✅ Cleared ${sectionsSnapshot.docs.length} sections`);
    } else {
      console.log('ℹ️ No sections to clear');
    }
  } catch (error) {
    console.error('❌ Error clearing sections:', error);
    throw error;
  }
}

/**
 * Create sections for all grades
 */
export async function seedSections(grades: Grade[]): Promise<Section[]> {
  console.log('🏫 Creating sections for all grades...');
  
  const sections: Section[] = [];
  const sectionNames = ['A', 'B', 'C', 'D']; // 4 sections per grade
  const now = new Date().toISOString();
  
  try {
    for (const grade of grades) {
      console.log(`   Creating sections for ${grade.name}...`);
      
      for (const sectionName of sectionNames) {
        const sectionData = {
          name: sectionName,
          gradeId: grade.id,
          isActive: true,
          createdAt: now,
          updatedAt: now
        };
        
        const docRef = await addDoc(collection(db, 'sections'), sectionData);
        
        const section: Section = {
          id: docRef.id,
          ...sectionData
        };
        
        sections.push(section);
        console.log(`     ✅ Created Section ${sectionName} for ${grade.name}`);
      }
    }
    
    console.log(`✅ Successfully created ${sections.length} sections`);
    return sections;
    
  } catch (error) {
    console.error('❌ Error creating sections:', error);
    throw error;
  }
}

/**
 * Get sections by grade ID
 */
export async function getSectionsByGrade(gradeId: string): Promise<Section[]> {
  try {
    const sectionsSnapshot = await getDocs(collection(db, 'sections'));
    const sections: Section[] = [];
    
    sectionsSnapshot.docs.forEach((doc) => {
      const data = doc.data();
      if (data.gradeId === gradeId) {
        sections.push({
          id: doc.id,
          ...data
        } as Section);
      }
    });
    
    return sections.sort((a, b) => a.name.localeCompare(b.name));
  } catch (error) {
    console.error('❌ Error getting sections by grade:', error);
    throw error;
  }
}

/**
 * Get all sections with grade information
 */
export async function getAllSectionsWithGrades(): Promise<(Section & { gradeName: string; gradeLevel: number })[]> {
  try {
    const [sectionsSnapshot, gradesSnapshot] = await Promise.all([
      getDocs(collection(db, 'sections')),
      getDocs(collection(db, 'grades'))
    ]);
    
    // Create grade lookup map
    const gradeMap = new Map();
    gradesSnapshot.docs.forEach((doc) => {
      const data = doc.data();
      gradeMap.set(doc.id, {
        name: data.name,
        level: data.level
      });
    });
    
    const sectionsWithGrades: (Section & { gradeName: string; gradeLevel: number })[] = [];
    
    sectionsSnapshot.docs.forEach((doc) => {
      const sectionData = doc.data();
      const gradeInfo = gradeMap.get(sectionData.gradeId);
      
      if (gradeInfo) {
        sectionsWithGrades.push({
          id: doc.id,
          ...sectionData,
          gradeName: gradeInfo.name,
          gradeLevel: gradeInfo.level
        } as Section & { gradeName: string; gradeLevel: number });
      }
    });
    
    return sectionsWithGrades.sort((a, b) => {
      // Sort by grade level first, then by section name
      if (a.gradeLevel !== b.gradeLevel) {
        return a.gradeLevel - b.gradeLevel;
      }
      return a.name.localeCompare(b.name);
    });
  } catch (error) {
    console.error('❌ Error getting sections with grades:', error);
    throw error;
  }
}

/**
 * Create a single section
 */
export async function createSection(gradeId: string, sectionName: string): Promise<Section> {
  const now = new Date().toISOString();
  
  const sectionData = {
    name: sectionName,
    gradeId,
    isActive: true,
    createdAt: now,
    updatedAt: now
  };
  
  try {
    const docRef = await addDoc(collection(db, 'sections'), sectionData);
    
    return {
      id: docRef.id,
      ...sectionData
    };
  } catch (error) {
    console.error('❌ Error creating section:', error);
    throw error;
  }
}

/**
 * Update section
 */
export async function updateSection(sectionId: string, updates: Partial<Omit<Section, 'id' | 'createdAt'>>): Promise<void> {
  try {
    const sectionRef = doc(db, 'sections', sectionId);
    await updateDoc(sectionRef, {
      ...updates,
      updatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Error updating section:', error);
    throw error;
  }
}

/**
 * Delete section
 */
export async function deleteSection(sectionId: string): Promise<void> {
  try {
    await deleteDoc(doc(db, 'sections', sectionId));
  } catch (error) {
    console.error('❌ Error deleting section:', error);
    throw error;
  }
}

/**
 * Check if section has students (for deletion validation)
 */
export async function sectionHasStudents(sectionId: string): Promise<boolean> {
  try {
    const studentsSnapshot = await getDocs(collection(db, 'students'));
    
    for (const studentDoc of studentsSnapshot.docs) {
      const studentData = studentDoc.data();
      if (studentData.sectionId === sectionId) {
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error('❌ Error checking if section has students:', error);
    return true; // Err on the side of caution
  }
}
