import { NextRequest, NextResponse } from 'next/server';
import { db } from '../../../lib/firebase';
import { collection, getDocs, doc, updateDoc } from 'firebase/firestore';
import { qrService } from '../../../lib/services/qrService';

export async function POST(request: NextRequest) {
  try {
    console.log('Starting QR code generation for all students...');
    
    // Get all students from the database
    const studentsCollection = collection(db, 'students');
    const studentsSnapshot = await getDocs(studentsCollection);
    
    if (studentsSnapshot.empty) {
      return NextResponse.json({
        success: false,
        message: 'No students found in the database'
      }, { status: 404 });
    }

    const results = [];
    
    // Generate QR codes for each student
    for (const studentDoc of studentsSnapshot.docs) {
      const student = studentDoc.data();
      const studentId = studentDoc.id;
      
      try {
        console.log(`Generating QR for student: ${student.firstName} ${student.lastName} (LRN: ${student.lrn})`);
        
        // Generate hash if student doesn't have one
        let hash = student.hash;
        if (!hash) {
          hash = qrService.generateStudentHash(student.lrn);
          console.log(`Generated hash for ${student.lrn}: ${hash}`);
        }

        // Generate QR code image
        const qrUrl = await qrService.generateStudentQR(student.lrn, hash);
        console.log(`Generated QR URL for ${student.lrn}: ${qrUrl.substring(0, 50)}...`);

        // Update student record
        const studentRef = doc(db, 'students', studentId);
        await updateDoc(studentRef, {
          hash,
          qrUrl,
          qrGenerated: true,
          updatedAt: new Date().toISOString(),
        });

        results.push({
          studentId,
          lrn: student.lrn,
          name: `${student.firstName} ${student.lastName}`,
          status: 'success'
        });
        
        console.log(`Successfully updated QR for ${student.firstName} ${student.lastName}`);
      } catch (error) {
        console.error(`Error generating QR for student ${studentId}:`, error);
        results.push({
          studentId,
          lrn: student.lrn,
          name: `${student.firstName} ${student.lastName}`,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;

    console.log(`QR generation completed. Success: ${successCount}, Errors: ${errorCount}`);

    return NextResponse.json({
      success: true,
      message: `QR code generation completed. ${successCount} successful, ${errorCount} errors.`,
      results,
      summary: {
        total: results.length,
        successful: successCount,
        errors: errorCount
      }
    });
  } catch (error) {
    console.error('QR generation error:', error);
    return NextResponse.json({
      success: false,
      message: 'Error during QR code generation',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'QR Code Generation API',
    usage: 'Send a POST request to this endpoint to generate QR codes for all students',
    description: 'This endpoint will generate QR codes for all students in the database who don\'t have them yet'
  });
}
