"use client";
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/Button';
import Select from '@/components/ui/Select';
import { useUpdateGrade } from '@/hooks/useGrades';
import { Grade } from '@/lib/services/gradeService';
import { CreateGradeData } from '@/lib/types/school';

interface GradeEditModalProps {
  grade: Grade | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
};

const modalVariants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: -50
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.3
    }
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    y: -50,
    transition: {
      duration: 0.2
    }
  }
};

export default function GradeEditModal({ grade, isOpen, onClose, onSuccess }: GradeEditModalProps) {
  const [formData, setFormData] = useState<CreateGradeData>({
    name: grade?.name || '',
    level: grade?.level || 4,
    isActive: grade?.isActive ?? true
  });

  const updateGradeMutation = useUpdateGrade();

  // Update form data when grade changes
  useEffect(() => {
    if (grade) {
      setFormData({
        name: grade.name,
        level: grade.level,
        isActive: grade.isActive
      });
    }
  }, [grade]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!grade) return;
    
    try {
      await updateGradeMutation.mutateAsync({
        gradeId: grade.id,
        updates: formData
      });
      onSuccess();
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
              name === 'level' ? parseInt(value) : value
    }));
  };

  if (!grade) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed left-[50%] top-[50%] z-50 w-full max-w-2xl max-h-[90vh] translate-x-[-50%] translate-y-[-50%] bg-white border-2 border-[#84a98c] shadow-lg p-6 overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="mb-6">
              <h2 className="text-lg font-semibold leading-none tracking-tight text-[#84a98c] mb-2">Edit Grade</h2>
              <p className="text-sm text-gray-600">
                Update grade information and settings.
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {updateGradeMutation.error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3">
                  {updateGradeMutation.error.message}
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Grade Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border-2 border-gray-200 focus:border-[#84a98c] focus:outline-none"
                    placeholder="e.g., Grade 7"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Grade Level *
                  </label>
                  <Select
                    value={formData.level.toString()}
                    onChange={(value) => setFormData(prev => ({ ...prev, level: parseInt(value) }))}
                    options={[
                      { value: '1', label: 'Grade 1' },
                      { value: '2', label: 'Grade 2' },
                      { value: '3', label: 'Grade 3' },
                      { value: '4', label: 'Grade 4' },
                      { value: '5', label: 'Grade 5' },
                      { value: '6', label: 'Grade 6' },
                      { value: '7', label: 'Grade 7' },
                      { value: '8', label: 'Grade 8' },
                      { value: '9', label: 'Grade 9' },
                      { value: '10', label: 'Grade 10' },
                      { value: '11', label: 'Grade 11' },
                      { value: '12', label: 'Grade 12' },
                    ]}
                  />
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleInputChange}
                  className="w-4 h-4 text-[#84a98c] border-gray-300 focus:ring-[#84a98c]"
                />
                <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">
                  Active grade level
                </label>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <Button
                  type="button"
                  onClick={onClose}
                  variant="outlined"
                  theme="neutral"
                  size="md"
                  radius="none"
                  disabled={updateGradeMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="filled"
                  theme="primary"
                  size="md"
                  radius="none"
                  disabled={updateGradeMutation.isPending}
                >
                  {updateGradeMutation.isPending ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </form>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
