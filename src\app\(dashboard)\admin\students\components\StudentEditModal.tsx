"use client";
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/Button';
import { useGradesWithSections } from '@/hooks/useGrades';
import { useUpdateStudent } from '@/hooks/useStudents';
import { StudentFormData } from '@/lib/schemas';

interface StudentEditModalProps {
  student: any;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
};

const modalVariants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: -50
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.3
    }
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    y: -50,
    transition: {
      duration: 0.2
    }
  }
};

export default function StudentEditModal({ student, isOpen, onClose, onSuccess }: StudentEditModalProps) {
  const [studentForm, setStudentForm] = useState<StudentFormData>({
    firstName: '',
    middleName: '',
    lastName: '',
    extension: '',
    lrn: '',
    gradeId: '',
    sectionId: '',
    status: 'active'
  });

  const { data: gradesWithSections = [] } = useGradesWithSections();
  const updateStudentMutation = useUpdateStudent();

  // Get sections for selected grade
  const selectedGrade = gradesWithSections.find(g => g.id === studentForm.gradeId);
  const availableSections = selectedGrade?.sections || [];

  // Populate form when student changes
  useEffect(() => {
    if (student) {
      // Handle both old and new data structures
      let firstName = '', middleName = '', lastName = '';

      if (student.fullName && Array.isArray(student.fullName)) {
        // New structure: fullName is an array [firstName, middleName, lastName]
        firstName = student.fullName[0] || '';
        middleName = student.fullName[1] || '';
        lastName = student.fullName[2] || '';
      } else if (student.firstName && student.lastName) {
        // Old structure: individual name fields
        firstName = student.firstName || '';
        middleName = student.middleName || '';
        lastName = student.lastName || '';
      }

      setStudentForm({
        firstName,
        middleName,
        lastName,
        extension: student.extension || '',
        lrn: student.lrn || student.studentId || '',
        gradeId: student.gradeId || '',
        sectionId: student.sectionId || '',
        status: student.status || 'active'
      });
    }
  }, [student]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // LRN validation - only allow numbers and limit to 12 digits (but LRN is disabled in edit mode)
    if (name === 'lrn') {
      const numericValue = value.replace(/\D/g, ''); // Remove non-digits
      if (numericValue.length <= 12) {
        setStudentForm(prev => ({
          ...prev,
          [name]: numericValue
        }));
      }
      return;
    }

    setStudentForm(prev => ({
      ...prev,
      [name]: value,
      // Reset section when grade changes
      ...(name === 'gradeId' && { sectionId: '' })
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!student) return;

    try {
      await updateStudentMutation.mutateAsync({
        lrn: student.lrn,
        updates: {
          ...studentForm,
          status: 'active' // Always ensure student status is active
        }
      });
      onSuccess();
    } catch (error) {
      // Error handled by mutation
    }
  };

  if (!student) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed left-[50%] top-[50%] z-50 w-full max-w-3xl max-h-[90vh] translate-x-[-50%] translate-y-[-50%] bg-white border-2 border-[#84a98c] shadow-lg p-6 overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="mb-6">
              <h2 className="text-lg font-semibold leading-none tracking-tight text-[#84a98c] mb-2">Edit Student</h2>
              <p className="text-sm text-gray-600">
                Update student information and assignments.
              </p>
            </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-[#84a98c] mb-4">Student Information</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#84a98c] mb-1">First Name *</label>
                <input
                  type="text"
                  name="firstName"
                  value={studentForm.firstName}
                  onChange={handleInputChange}
                  className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-[#84a98c] mb-1">Last Name *</label>
                <input
                  type="text"
                  name="lastName"
                  value={studentForm.lastName}
                  onChange={handleInputChange}
                  className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-[#84a98c] mb-1">Middle Name</label>
                <input
                  type="text"
                  name="middleName"
                  value={studentForm.middleName}
                  onChange={handleInputChange}
                  className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-[#84a98c] mb-1">Extension</label>
                <input
                  type="text"
                  name="extension"
                  value={studentForm.extension}
                  onChange={handleInputChange}
                  placeholder="Jr., Sr., III"
                  className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Academic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-[#84a98c] mb-4">Academic Information</h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#84a98c] mb-1">LRN *</label>
                <input
                  type="text"
                  name="lrn"
                  value={studentForm.lrn}
                  onChange={handleInputChange}
                  className="w-full px-3 py-3 bg-gray-100 border border-gray-300 rounded text-gray-500 cursor-not-allowed"
                  required
                  disabled // LRN should not be editable
                />
                <p className="text-xs text-gray-500 mt-1">LRN cannot be changed</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-[#84a98c] mb-1">Grade Level *</label>
                <select
                  name="gradeId"
                  value={studentForm.gradeId}
                  onChange={handleInputChange}
                  className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent"
                  required
                >
                  <option value="">Select Grade Level</option>
                  {gradesWithSections.map(grade => (
                    <option key={grade.id} value={grade.id}>
                      {grade.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-[#84a98c] mb-1">Section *</label>
                <select
                  name="sectionId"
                  value={studentForm.sectionId}
                  onChange={handleInputChange}
                  className="w-full px-3 py-3 bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#84a98c] focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed"
                  required
                  disabled={!studentForm.gradeId}
                >
                  <option value="">Select Section</option>
                  {availableSections.map(section => (
                    <option key={section.id} value={section.id}>
                      {section.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end space-x-3 mt-6">
            <Button
              type="button"
              onClick={onClose}
              variant="outlined"
              theme="neutral"
              disabled={updateStudentMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              variant="filled"
              theme="primary"
              size="md"
              radius="none"
              loading={updateStudentMutation.isPending}
              disabled={updateStudentMutation.isPending}
            >
              {updateStudentMutation.isPending ? 'Updating...' : 'Update Student'}
            </Button>
          </div>
          </form>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
