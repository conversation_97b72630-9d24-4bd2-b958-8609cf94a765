{
  "collections": {
    "grade_levels": [
      {
        "_id": "ObjectId",
        "level": "Number", // e.g., 4, 5, or 6
        "sections": ["ObjectId"], // References to sections collection
        "subjects": ["ObjectId"], // References to subjects collection specific to this grade level
        "school_year": "String" // e.g., "2025-2026"
      }
    ],
    "subjects": [
      {
        "_id": "ObjectId",
        "name": "String", // e.g., "English", "Math", "Science", "Filipino", "AP", "ESP"
        "grade_level": "ObjectId", // Reference to grade_levels
        "curriculum_guidelines": {
          "description": "String",
          "learning_objectives": ["String"] // DepEd-aligned objectives
        }
      }
    ],
    "sections": [
      {
        "_id": "ObjectId",
        "name": "String", // e.g., "Rizal", "Bonifacio"
        "grade_level": "ObjectId", // Reference to grade_levels
        "students": ["ObjectId"], // References to students
        "subject_teacher_assignments": [
          {
            "subject_id": "ObjectId", // Reference to subjects
            "teacher_id": "ObjectId" // Reference to teachers
          }
        ]
      }
    ],
    "students": [
      {
        "_id": "ObjectId",
        "name": {
          "first": "String",
          "last": "String"
        },
        "qr_code": "String", // Unique QR code for login
        "grade_level": "ObjectId", // Reference to grade_levels
        "section_id": "ObjectId", // Reference to sections
        "created_by": "ObjectId", // Reference to admin who created the account
        "created_at": "Date"
      }
    ],
    "teachers": [
      {
        "_id": "ObjectId",
        "name": {
          "first": "String",
          "last": "String"
        },
        "email": "String", // Used for login
        "password_hash": "String", // Hashed password for secure login
        "created_by": "ObjectId", // Reference to admin
        "created_at": "Date"
      }
    ],
    "exams": [
      {
        "_id": "ObjectId",
        "title": "String", // e.g., "Grade 4 English Midterm Exam"
        "subject_id": "ObjectId", // Reference to subjects
        "grade_level": "ObjectId", // Reference to grade_levels
        "section_ids": ["ObjectId"], // References to sections where exam is assigned
        "teacher_id": "ObjectId", // Reference to teacher who created it
        "type": "String", // "Exam" or "Quiz"
        "questions": [
          {
            "question_id": "ObjectId",
            "text": "String",
            "type": "String", // "multiple_choice", "true_false", "essay"
            "points": "Number",
            "correct_answer": "String | null", // For objective questions; null for essays
            "skill_type": "String" // "HOTS" or "LOTS"
          }
        ],
        "status": "String", // "draft", "pending_approval", "approved", "rejected"
        "created_at": "Date",
        "approved_by": "ObjectId | null", // Reference to admin (principal)
        "approved_at": "Date | null"
      }
    ],
    "student_exam_submissions": [
      {
        "_id": "ObjectId",
        "exam_id": "ObjectId", // Reference to exams
        "student_id": "ObjectId", // Reference to students
        "section_id": "ObjectId", // Reference to sections
        "answers": [
          {
            "question_id": "ObjectId",
            "answer": "String", // Student's answer
            "auto_graded_score": "Number | null", // For objective questions
            "manual_graded_score": "Number | null", // For essay questions
            "is_correct": "Boolean | null" // For objective questions
          }
        ],
        "status": "String", // "in_progress", "submitted"
        "tab_switch_detected": "Boolean", // Tracks if student opened another tab
        "total_score": "Number | null", // Computed after grading
        "submitted_at": "Date | null",
        "feedback": {
          "student_comments": "String | null",
          "system_response": {
            "scores_breakdown": [
              {
                "question_id": "ObjectId",
                "score": "Number",
                "correct_answer": "String | null" // Shown for incorrect objective answers
              }
            ],
            "computed_grade": "Number" // Final grade
          }
        }
      }
    ],
    "admins": [
      {
        "_id": "ObjectId",
        "name": {
          "first": "String",
          "last": "String"
        },
        "email": "String",
        "password_hash": "String",
        "role": "String" // e.g., "principal", "admin"
      }
    ]
  }
}