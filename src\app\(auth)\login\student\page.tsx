"use client";
import { useRef, useState, useEffect } from "react";
import jsQR from "jsqr";
import { useRouter } from "next/navigation";
import { signInWithCustomToken, getAuth } from "firebase/auth";
import { ArrowLeftIcon, QrCodeIcon, CameraIcon, VideoCameraIcon } from "@heroicons/react/24/outline";
import { Button } from "../../../../components/ui/Button";

const StudentLoginPage = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [scanMode, setScanMode] = useState<'upload' | 'camera'>('upload');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [studentInfo, setStudentInfo] = useState<null | {
    firstName: string;
    middleName?: string;
    lastName: string;
    extension?: string;
    lrn: string;
  }>(null);
  const router = useRouter();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError("");
    setSuccess("");
    setStudentInfo(null);
    const file = e.target.files?.[0];
    if (!file) return;
    setSelectedFile(file);
    setPreviewUrl(URL.createObjectURL(file));
    // Auto-decode and login
    decodeAndLogin(file);
  };

  const decodeAndLogin = (file: File) => {
    const img = new window.Image();
    img.src = URL.createObjectURL(file);
    img.onload = () => {
      const canvas = document.createElement("canvas");
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext("2d");
      if (!ctx) {
        setError("Failed to read image context.");
        return;
      }
      ctx.drawImage(img, 0, 0, img.width, img.height);
      const imageData = ctx.getImageData(0, 0, img.width, img.height);
      const code = jsQR(imageData.data, imageData.width, imageData.height);
      if (code && code.data) {
        handleLogin(code.data); // code.data is the hash
      } else {
        setError("Could not decode QR code. Please try another image.");
      }
    };
    img.onerror = () => setError("Failed to load image.");
  };

  const startCamera = async () => {
    try {
      setIsScanning(true);
      setScanMode('camera');
      setError("");

      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' } // Use back camera if available
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
        scanQRFromVideo();
      }
    } catch (err) {
      setError("Unable to access camera. Please use file upload instead.");
      setIsScanning(false);
    }
  };

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setIsScanning(false);
    setScanMode('upload');
  };

  const scanQRFromVideo = () => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) return;

    const scan = () => {
      if (video.readyState === video.HAVE_ENOUGH_DATA) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const code = jsQR(imageData.data, imageData.width, imageData.height);

        if (code) {
          stopCamera();
          handleLogin(code.data);
          return;
        }
      }

      if (isScanning) {
        requestAnimationFrame(scan);
      }
    };

    scan();
  };

  const handleLogin = async (hashValue: string) => {
    setError("");
    setSuccess("");
    setStudentInfo(null);
    if (!hashValue) {
      setError("No hash found. Please upload and decode a valid QR code.");
      return;
    }
    setIsSubmitting(true);
    try {
      const res = await fetch("/api/auth/qr-login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ hash: hashValue }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data.message || "Invalid QR code");

      // Sign in with custom token
      const auth = getAuth();
      await signInWithCustomToken(auth, data.token);

      setStudentInfo(data.student);
      setSuccess("Logged in successfully!");
      if (typeof window !== "undefined") {
        sessionStorage.setItem("studentInfo", JSON.stringify(data.student));
      }
      router.push("/student/dashboard");
    } catch (err: any) {
      setError(err.message || "Login failed. Please check your QR code and try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Cleanup camera on unmount
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#f0f7ff] to-[#e0efff] flex items-center justify-center px-4 theme-student">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <QrCodeIcon className="w-16 h-16 text-[#4285f4]" />
          </div>
          <h1 className="text-3xl font-bold text-[#3558b0] mb-2">Student Login</h1>
          <p className="text-[#4285f4]">Upload or scan your QR code to access your dashboard</p>
        </div>

        {/* Login Form */}
        <div className="bg-white border-2 border-[#c7e2ff] p-8 shadow-lg">
          {/* Mode Selection */}
          <div className="mb-6">
            <div className="flex space-x-2">
              <Button
                onClick={() => setScanMode('upload')}
                variant={scanMode === 'upload' ? 'filled' : 'outlined'}
                theme="student"
                size="md"
                radius="none"
                className="flex-1"
              >
                Upload QR
              </Button>
              <Button
                onClick={() => setScanMode('camera')}
                variant={scanMode === 'camera' ? 'filled' : 'outlined'}
                theme="student"
                size="md"
                radius="none"
                className="flex-1"
              >
                Scan QR
              </Button>
            </div>
          </div>

          {scanMode === 'upload' ? (
            <>
              {/* Upload Button */}
              <Button
                onClick={() => fileInputRef.current?.click()}
                variant="outlined"
                theme="student"
                size="xl"
                fullWidth
                radius="none"
                disabled={isSubmitting}
                className="p-6 h-auto mb-6 border-dashed"
                leftIcon={<CameraIcon className="w-12 h-12" />}
              >
                <div className="flex flex-col items-center space-y-2">
                  <span className="text-lg font-semibold">Upload QR Code</span>
                  <span className="text-sm opacity-80">Click to select your QR code image</span>
                </div>
              </Button>
            </>
          ) : (
            <>
              {/* Camera Scanner */}
              <div className="mb-6">
                {!isScanning ? (
                  <Button
                    onClick={startCamera}
                    variant="filled"
                    theme="student"
                    size="xl"
                    fullWidth
                    radius="none"
                    disabled={isSubmitting}
                    className="p-6 h-auto"
                    leftIcon={<VideoCameraIcon className="w-12 h-12" />}
                  >
                    <div className="flex flex-col items-center space-y-2">
                      <span className="text-lg font-semibold">Start Camera</span>
                      <span className="text-sm opacity-80">Scan QR code with your camera</span>
                    </div>
                  </Button>
                ) : (
                  <div className="space-y-4">
                    <div className="relative">
                      <video
                        ref={videoRef}
                        className="w-full h-64 bg-black border-2 border-[#c7e2ff] object-cover"
                        playsInline
                        muted
                      />
                      <canvas ref={canvasRef} className="hidden" />
                      <div className="absolute inset-0 border-4 border-[#5a9cff] border-dashed opacity-50 pointer-events-none"></div>
                    </div>
                    <Button
                      onClick={stopCamera}
                      variant="outlined"
                      theme="student"
                      size="md"
                      fullWidth
                      radius="none"
                    >
                      Stop Camera
                    </Button>
                  </div>
                )}
              </div>
            </>
          )}

          <input
            ref={fileInputRef}
            type="file"
            accept="image/png, image/jpeg, image/jpg"
            onChange={handleFileChange}
            className="hidden"
            aria-label="QR Code File Input"
          />

          {/* QR Code Preview */}
          {previewUrl && (
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-[#3558b0] mb-2">QR Code Preview:</h3>
              <div className="flex justify-center">
                <img
                  src={previewUrl}
                  alt="QR Code Preview"
                  className="w-48 h-48 object-contain border-2 border-[#c7e2ff] bg-white shadow-md"
                />
              </div>
            </div>
          )}

          {/* Student Info Display */}
          {studentInfo && (
            <div className="mb-6 p-4 border-2 border-green-300 bg-green-50">
              <h3 className="text-sm font-semibold text-green-800 mb-2">Student Information:</h3>
              <div className="space-y-1 text-sm text-green-700">
                <div><strong>Name:</strong> {studentInfo.firstName} {studentInfo.middleName} {studentInfo.lastName} {studentInfo.extension}</div>
                <div><strong>LRN:</strong> {studentInfo.lrn}</div>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-4 border-2 border-red-300 bg-red-50">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="mb-6 p-4 border-2 border-green-300 bg-green-50">
              <p className="text-green-700 text-sm">{success}</p>
            </div>
          )}

          {/* Loading State */}
          {isSubmitting && (
            <div className="mb-6 p-4 border-2 border-[#c7e2ff] bg-[#f0f7ff]">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#4285f4]"></div>
                <p className="text-[#4285f4] text-sm">Logging you in...</p>
              </div>
            </div>
          )}
        </div>

        {/* Back Button */}
        <div className="mt-6 text-center">
          <Button
            onClick={() => router.push('/login')}
            variant="ghost"
            theme="student"
            size="sm"
            leftIcon={<ArrowLeftIcon className="w-4 h-4" />}
            href="/login"
            navigationLoading={true}
          >
            Back to Login Options
          </Button>
        </div>
      </div>
    </div>
  );
};

export default StudentLoginPage;
