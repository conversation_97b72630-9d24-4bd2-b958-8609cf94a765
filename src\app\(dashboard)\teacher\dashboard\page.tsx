"use client";

import React from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useTeacherProfile, useTeacherSectionsByGrade } from '@/hooks/useTeacherProfile';
import { 
  AcademicCapIcon, 
  BookOpenIcon, 
  ClipboardDocumentListIcon,
  UserGroupIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';

export default function TeacherDashboard() {
  const router = useRouter();
  const { data: teacherProfile, isLoading: isLoadingProfile } = useTeacherProfile();
  const { sectionsByGrade, isLoading: isLoadingSections } = useTeacherSectionsByGrade();

  // Calculate statistics
  const totalSections = sectionsByGrade.reduce((total, grade) => 
    total + Object.keys(grade.sections).length, 0
  );
  
  const totalSubjects = sectionsByGrade.reduce((total, grade) => 
    total + Object.values(grade.sections).reduce((sectionTotal, section) => 
      sectionTotal + section.subjects.length, 0
    ), 0
  );

  const handleCreateExam = () => {
    // Navigate to exam creation - we'll implement this later
    router.push('/teacher/exams/create');
  };

  const handleViewSection = (gradeId: string, sectionId: string) => {
    router.push(`/teacher/section/${gradeId}/${sectionId}`);
  };

  if (isLoadingProfile) {
    return (
      <div className="min-h-screen bg-[#FAFAF6] flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-[#84a98c] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-[#84a98c] font-medium">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-none flex items-center justify-center">
            <AcademicCapIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Teacher Dashboard</h2>
            <p className="text-gray-600">
              Welcome back, {teacherProfile?.firstName} {teacherProfile?.lastName}
            </p>
          </div>
        </div>
        
        <Button
          variant="filled"
          theme="success"
          onClick={handleCreateExam}
        >
          <PlusIcon className="w-4 h-4 mr-2" />
          Create Exam
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-none flex items-center justify-center">
              <UserGroupIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">My Sections</p>
              <p className="text-2xl font-bold text-gray-900">
                {isLoadingSections ? '...' : totalSections}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-none flex items-center justify-center">
              <BookOpenIcon className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Subjects Teaching</p>
              <p className="text-2xl font-bold text-gray-900">
                {isLoadingSections ? '...' : totalSubjects}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-none flex items-center justify-center">
              <ClipboardDocumentListIcon className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending Exams</p>
              <p className="text-2xl font-bold text-gray-900">0</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-none flex items-center justify-center">
              <ClipboardDocumentListIcon className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Exams</p>
              <p className="text-2xl font-bold text-gray-900">0</p>
            </div>
          </div>
        </Card>
      </div>

      {/* My Sections */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">My Teaching Assignments</h3>
        </div>

        {isLoadingSections ? (
          <div className="text-center py-8">
            <div className="w-8 h-8 border-2 border-[#84a98c] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-500">Loading your sections...</p>
          </div>
        ) : sectionsByGrade.length === 0 ? (
          <div className="text-center py-12">
            <BookOpenIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-semibold text-gray-900 mb-2">No Sections Assigned</h4>
            <p className="text-gray-600 mb-6">
              You don't have any teaching assignments yet. Please contact your administrator.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {sectionsByGrade.map((grade) => (
              <div key={grade.gradeId} className="border border-gray-200 rounded-none p-4">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">{grade.gradeName}</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.values(grade.sections).map((section) => (
                    <div
                      key={section.sectionId}
                      className="bg-gray-50 border border-gray-200 p-4 hover:bg-gray-100 transition-colors cursor-pointer"
                      onClick={() => handleViewSection(grade.gradeId, section.sectionId)}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <h5 className="font-semibold text-gray-900">{section.sectionName}</h5>
                        <UserGroupIcon className="w-5 h-5 text-gray-400" />
                      </div>
                      
                      <div className="space-y-2">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Subjects:</p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {section.subjects.map((subject) => (
                              <span
                                key={subject.subjectId}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                {subject.subjectName}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                      
                      <div className="mt-4 pt-3 border-t border-gray-200">
                        <Button
                          variant="outlined"
                          theme="primary"
                          size="sm"
                          className="w-full"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewSection(grade.gradeId, section.sectionId);
                          }}
                        >
                          View Section
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            variant="outlined"
            theme="primary"
            className="h-16"
            onClick={handleCreateExam}
          >
            <div className="text-center">
              <PlusIcon className="w-6 h-6 mx-auto mb-1" />
              <span>Create New Exam</span>
            </div>
          </Button>
          
          <Button
            variant="outlined"
            theme="neutral"
            className="h-16"
            onClick={() => router.push('/teacher/exams')}
          >
            <div className="text-center">
              <ClipboardDocumentListIcon className="w-6 h-6 mx-auto mb-1" />
              <span>View My Exams</span>
            </div>
          </Button>
          
          <Button
            variant="outlined"
            theme="neutral"
            className="h-16"
            onClick={() => router.push('/teacher/profile')}
          >
            <div className="text-center">
              <UserGroupIcon className="w-6 h-6 mx-auto mb-1" />
              <span>Update Profile</span>
            </div>
          </Button>
        </div>
      </Card>
    </div>
  );
}
