"use client";
import { useState } from "react";
import jsQR from "jsqr";

const cream = "bg-[#FFFDEB]";
const sage = "bg-[#C7E8CA]";

type Student = {
  id: string;
  fullName: string;
  section: string;
  yearLevel: string;
};

const TestPage = () => {
  const [scanned, setScanned] = useState<Student | null>(null);
  const [error, setError] = useState("");
  const [debugLog, setDebugLog] = useState<string[]>([]);
  const fileInputId = "qr-upload";

  const appendDebugLog = (msg: string) => setDebugLog(logs => [...logs.slice(-14), msg]);

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    appendDebugLog(`[handleImageUpload] File selected: ${file ? file.name : 'none'}`);
    if (!file) return;
    const reader = new FileReader();
    reader.onload = (event) => {
      appendDebugLog('[handleImageUpload] FileReader loaded');
      const img = new window.Image();
      img.onload = () => {
        appendDebugLog('[handleImageUpload] Image loaded');
        const canvas = document.createElement("canvas");
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext("2d");
        if (!ctx) {
          setError("Canvas error.");
          appendDebugLog('[handleImageUpload] Canvas error');
          return;
        }
        ctx.drawImage(img, 0, 0, img.width, img.height);
        const imageData = ctx.getImageData(0, 0, img.width, img.height);
        const code = jsQR(imageData.data, img.width, img.height);
        if (code && code.data) {
          appendDebugLog(`[handleImageUpload] QR code found: ${code.data}`);
          try {
            const parsed: Student = JSON.parse(code.data);
            setScanned(parsed);
            setError("");
          } catch {
            setError("Invalid QR code data.");
            appendDebugLog('[handleImageUpload] Invalid QR code data');
          }
        } else {
          setError("No QR code found in image.");
          appendDebugLog('[handleImageUpload] No QR code found in image');
        }
      };
      if (typeof event.target?.result === "string") {
        img.src = event.target.result;
        appendDebugLog('[handleImageUpload] Image src set');
      }
    };
    reader.onerror = (err) => {
      setError("File read error.");
      appendDebugLog(`[handleImageUpload] FileReader error: ${err}`);
    };
    reader.readAsDataURL(file);
    appendDebugLog('[handleImageUpload] FileReader readAsDataURL called');
  };

  return (
    <main className={`min-h-screen flex flex-col items-center justify-center ${cream}`}> 
      <h1 className="text-3xl font-bold mb-8 text-center text-[#3A4D39]">Test QR Code (Upload Only)</h1>
      <section className={`flex flex-col items-center gap-4 p-8 border border-[#3A4D39] ${sage}`}> 
        <label htmlFor={fileInputId} className="text-[#3A4D39] font-semibold mb-2" tabIndex={0} aria-label="Upload QR code image">
          Select QR code image
        </label>
        <input
          id={fileInputId}
          type="file"
          accept="image/*"
          onChange={e => {
            handleImageUpload(e);
            e.target.value = "";
          }}
          className="border border-[#3A4D39] p-2 bg-white text-[#3A4D39] focus:outline-none focus:ring-2 focus:ring-[#3A4D39] w-full max-w-xs cursor-pointer"
          tabIndex={0}
          aria-label="Upload QR code image"
        />
        {error && <div className="text-red-600 font-semibold">{error}</div>}
        {scanned && (
          <div className="mt-4 text-[#3A4D39] text-center">
            <div className="font-bold">Student Info:</div>
            <div><b>Name:</b> {scanned.fullName}</div>
            <div><b>Section:</b> {scanned.section}</div>
            <div><b>Year Level:</b> {scanned.yearLevel}</div>
          </div>
        )}
        {debugLog.length > 0 && (
          <div className="mt-4 w-full max-w-md text-xs bg-white text-[#3A4D39] border border-[#3A4D39] p-2 overflow-x-auto">
            <div className="font-bold mb-1">Debug Log:</div>
            {debugLog.map((msg, i) => (
              <div key={i}>{msg}</div>
            ))}
          </div>
        )}
      </section>
    </main>
  );
};

export default TestPage; 