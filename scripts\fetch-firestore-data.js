const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

// Initialize Firebase Admin SDK
const serviceAccount = require('../serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: 'greentech-assessment'
});

const db = admin.firestore();

async function fetchAllCollections() {
  try {
    console.log('🔥 Fetching Firestore Collections...\n');
    
    // List of actual collections found in your database
    const collections = [
      'admins',
      'exams',
      'grades',
      'quizzes',
      'results',
      'sections',
      'students',
      'subjects',
      'teacherAssignments',
      'teacher_subject_assignments',
      'teachers'
    ];

    const allData = {};

    for (const collectionName of collections) {
      try {
        console.log(`📚 Fetching ${collectionName}...`);
        const snapshot = await db.collection(collectionName).get();
        
        if (snapshot.empty) {
          console.log(`   ⚠️  Collection '${collectionName}' is empty`);
          allData[collectionName] = [];
        } else {
          const docs = [];
          snapshot.forEach(doc => {
            docs.push({
              id: doc.id,
              ...doc.data()
            });
          });
          
          console.log(`   ✅ Found ${docs.length} documents`);
          allData[collectionName] = docs;
        }
      } catch (error) {
        console.log(`   ❌ Error fetching '${collectionName}': ${error.message}`);
        allData[collectionName] = { error: error.message };
      }
    }

    // Save to file
    const outputPath = path.join(__dirname, 'firestore-data.json');
    fs.writeFileSync(outputPath, JSON.stringify(allData, null, 2));
    
    console.log(`\n💾 Data saved to: ${outputPath}`);
    console.log('\n📊 Summary:');
    
    for (const [collection, data] of Object.entries(allData)) {
      if (data.error) {
        console.log(`   ${collection}: ERROR - ${data.error}`);
      } else {
        console.log(`   ${collection}: ${data.length} documents`);
      }
    }

    return allData;
    
  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  }
}

// Run the script
fetchAllCollections()
  .then(() => {
    console.log('\n🎉 Firestore data fetch completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
