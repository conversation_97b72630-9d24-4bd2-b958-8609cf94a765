"use client";
import React from 'react';
import { cn } from '@/lib/utils';

interface CardInfoRowProps {
  icon?: React.ReactNode;
  label: string;
  value?: React.ReactNode;
  children?: React.ReactNode;
  className?: string;
  labelColor?: 'default' | 'primary' | 'student' | 'teacher' | 'admin';
}

const labelColorMap = {
  default: 'text-gray-700',
  primary: 'text-[#84a98c]',
  student: 'text-[#4285f4]',
  teacher: 'text-[#9c6bff]',
  admin: 'text-slate-800',
};

export default function CardInfoRow({ 
  icon, 
  label, 
  value, 
  children, 
  className,
  labelColor = 'primary'
}: CardInfoRowProps) {
  return (
    <div className={cn('flex items-start space-x-2', className)}>
      {icon && (
        <div className="flex-shrink-0 mt-0.5">
          {icon}
        </div>
      )}
      <div className="flex-1 min-w-0">
        <span className={cn(
          'text-xs font-medium block',
          labelColorMap[labelColor]
        )}>
          {label}:
        </span>
        <div className="mt-1">
          {value && (
            <span className="text-sm text-gray-700">{value}</span>
          )}
          {children}
        </div>
      </div>
    </div>
  );
}
