"use client";
import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { useCreateSection } from '@/hooks/useGrades';
import { useActiveGrades } from '@/hooks/useGrades';

interface SectionCreateFormProps {
  onClose: () => void;
  onSuccess: () => void;
  gradeId?: string;
}

export default function SectionCreateForm({ onClose, onSuccess, gradeId }: SectionCreateFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    gradeId: gradeId || ''
  });

  const createSectionMutation = useCreateSection();
  const { data: grades = [], isLoading: gradesLoading } = useActiveGrades();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await createSectionMutation.mutateAsync({
        name: formData.name,
        gradeId: formData.gradeId
      });
      onSuccess();
    } catch (error) {
      // Error handled by mutation
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Grade Selection - Only show if no gradeId is pre-selected */}
      {!gradeId && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Grade Level *
          </label>
          {gradesLoading ? (
            <div className="text-gray-500">Loading grades...</div>
          ) : (
            <div className="space-y-2">
              {grades.map(grade => (
                <label key={grade.id} className="flex items-center space-x-3 p-3 border-2 border-gray-200 hover:border-[#84a98c] hover:bg-[#f7faf8] transition-colors cursor-pointer">
                  <input
                    type="radio"
                    name="gradeId"
                    value={grade.id}
                    checked={formData.gradeId === grade.id}
                    onChange={(e) => setFormData(prev => ({ ...prev, gradeId: e.target.value }))}
                    className="w-4 h-4 text-[#84a98c] border-2 border-gray-300 focus:ring-[#84a98c] focus:ring-2"
                    required
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{grade.name}</div>
                    <div className="text-sm text-gray-600">Level {grade.level}</div>
                  </div>
                </label>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Section Name */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Section Name *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          className="w-full px-3 py-2 border-2 border-gray-300 focus:outline-none focus:border-[#84a98c] text-gray-900 placeholder-gray-600 bg-white transition-colors"
          placeholder="e.g., A, B, C, Rizal, Bonifacio"
          required
        />
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-3 pt-4">
        <Button
          type="button"
          onClick={onClose}
          variant="outlined"
          theme="neutral"
          size="sm"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="filled"
          theme="primary"
          size="sm"
          loading={createSectionMutation.isPending}
        >
          Create Section
        </Button>
      </div>
    </form>
  );
}
