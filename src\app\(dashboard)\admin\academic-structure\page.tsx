"use client";
import React, { useState } from 'react';
import { BuildingLibraryIcon, PlusIcon } from "@heroicons/react/24/outline";
import { Button } from '@/components/ui/Button';

// Import hooks and types
import { useGradesWithSections } from '@/hooks/useGrades';
import { useSubjects } from '@/hooks/useSubjects';
import { useSettings } from '@/hooks/useSettings';

// Import components
import GradeCardHorizontal from './components/GradeCardHorizontal';
import GradeForm from './components/GradeForm';
import SectionForm from './components/SectionForm';
import SubjectForm from './components/SubjectForm';
import SectionManagementModal from './components/SectionManagementModal';
import SubjectManagementModal from './components/SubjectManagementModal';
import { CardSkeleton, PageHeaderSkeleton } from '@/components/skeletons';

export default function ConfigurePage() {
  // Data fetching
  const { data: gradesWithSections = [], isLoading } = useGradesWithSections();
  const { data: subjects = [], isLoading: subjectsLoading } = useSubjects();
  const { data: settings } = useSettings();

  // Loading state
  if (isLoading || subjectsLoading) {
    return (
      <div className="space-y-6">
        <PageHeaderSkeleton />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <CardSkeleton key={i} />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <BuildingLibraryIcon className="w-8 h-8 text-[#84a98c]" />
        <div>
          <h2 className="text-2xl font-bold text-[#84a98c]">Configure</h2>
          <p className="text-sm text-gray-600">Manage grades, sections, and subjects</p>
        </div>
      </div>

      {/* Grade Cards - Horizontal Layout */}
      <div className="space-y-6">
        {gradesWithSections.map(grade => (
          <GradeCardHorizontal
            key={grade.id}
            grade={grade}
            subjects={subjects.filter(s => s.gradeId === grade.id)}
            settings={settings}
          />
        ))}
      </div>


    </div>
  );
}
