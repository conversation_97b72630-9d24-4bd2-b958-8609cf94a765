# GREENTECH Codebase Structure

## 📁 Current File Organization

### 🏗️ Root Level
```
GREENTECH/
├── README.md
├── DEVELOPMENT_GUIDE.md
├── CODEBASE_STRUCTURE.md
├── package.json
├── package-lock.json
├── tsconfig.json
├── next.config.ts
├── next-env.d.ts
├── postcss.config.mjs
├── components.json
├── serviceAccountKey.json
└── tsconfig.tsbuildinfo
```

### 📚 Documentation & Notes
```
DOC/
└── BUSINESSLOGIC.MD

notes/
├── architecture/
└── codebase_analysis_guidelines.md
```

### 🎨 Public Assets
```
public/
├── logo.ico
├── logo.png
├── file.svg
├── globe.svg
├── next.svg
├── vercel.svg
├── window.svg
└── logos/
```

### 🔧 Scripts & Utilities
```
scripts/
├── checkSubjects.js
├── checkTeachers.js
├── migrateTeachers.js
├── seedTeachers.js
└── seedUsers.js
```

### 📦 Library & Core Logic
```
lib/
├── firebase.ts                 # Firebase client configuration
├── firebaseAdmin.ts            # Firebase admin configuration
├── schemas.ts                  # Zod validation schemas
├── utils.ts                    # Utility functions
├── services/
│   ├── gradeService.ts         # Grade & section management
│   ├── qrService.ts           # QR code generation
│   ├── studentService.ts      # Student CRUD operations
│   └── teacherService.ts      # Teacher CRUD operations
├── seeders/
│   └── gradeSeeder.ts         # Database seeding utilities
└── types/
    └── school.ts              # TypeScript type definitions
```

### 🎯 Type Definitions
```
types/
└── auth.ts                    # Authentication types
```

### 🚀 Next.js App Directory

#### 🏠 Core App Files
```
app/
├── layout.tsx                 # Root layout
├── page.tsx                   # Landing page
├── globals.css               # Global styles
├── favicon.ico
├── styles/
│   └── colors.css            # Color definitions
├── providers/
│   ├── QueryProvider.tsx     # React Query setup
│   └── ToastProvider.tsx     # Toast notifications
├── contexts/
│   └── AuthContext.tsx       # Authentication context
└── hooks/
    ├── useGrades.ts          # Grade management hooks
    ├── useStudents.ts        # Student management hooks
    ├── useSubjects.ts        # Subject management hooks
    └── useTeachers.ts        # Teacher management hooks
```

#### 🎨 Shared Components
```
app/components/
├── index.ts                  # Component exports
├── FeatureItem.tsx          # Landing page features
├── FeatureShowcase.tsx      # Feature display
├── Footer.tsx               # Site footer
├── Header.tsx               # Site header
├── HeroSection.tsx          # Landing hero
├── LoginModal.tsx           # Login modal
├── Navigation.tsx           # Navigation component
├── QuizForm.tsx            # Quiz creation form
├── QuizModal.tsx           # Quiz modal
├── cards/
│   ├── index.tsx           # Card component exports
│   ├── BaseCard.tsx        # Base card component
│   ├── CardActions.tsx     # Card action buttons
│   ├── CardBadge.tsx       # Status badges
│   ├── CardContent.tsx     # Card content area
│   ├── CardHeader.tsx      # Card header
│   ├── CardInfoRow.tsx     # Information rows
│   ├── StudentCardTemplate.tsx  # Student card layout
│   └── TeacherCardTemplate.tsx  # Teacher card layout
├── forms/
│   └── StudentRegistrationForm.tsx  # Student registration
├── skeletons/
│   ├── index.tsx           # Skeleton exports
│   ├── CardSkeleton.tsx    # Card loading state
│   ├── FiltersSkeleton.tsx # Filter loading state
│   └── PageHeaderSkeleton.tsx  # Header loading state
└── ui/
    ├── AnimatedDialog.tsx   # Animated modal
    ├── Button.tsx          # Button component
    ├── Select.tsx          # Select dropdown
    ├── alert-dialog.tsx    # Alert dialogs
    ├── badge.tsx           # Badge component
    ├── card.tsx            # Card UI component
    ├── dialog.tsx          # Dialog component
    ├── form.tsx            # Form components
    ├── input.tsx           # Input component
    └── label.tsx           # Label component
```

#### 🔐 Authentication & User Routes
```
app/login/
├── page.tsx                 # General login page
├── admin/
│   └── page.tsx            # Admin login
├── student/
│   └── page.tsx            # Student QR login
└── teacher/
    └── page.tsx            # Teacher login
```

#### 👨‍🎓 Student Module
```
app/student/
├── dashboard/
│   └── page.tsx            # Student dashboard
└── login/
    └── page.tsx            # Student login (duplicate?)
```

#### 👨‍🏫 Teacher Module
```
app/teacher/
├── dashboard/
│   └── page.tsx            # Teacher dashboard
└── login/
    └── page.tsx            # Teacher login (duplicate?)
```

#### 🏛️ Admin Module
```
app/admin/
├── components/
│   └── AdminLayout.tsx     # Admin layout wrapper
├── dashboard/
│   ├── page.tsx           # Admin dashboard
│   └── components/
│       └── DashboardStats.tsx  # Dashboard statistics
├── students/
│   ├── page.tsx           # Student management
│   └── components/
│       ├── StudentCard.tsx      # Student card
│       ├── StudentEditModal.tsx # Edit student modal
│       ├── StudentForm.tsx      # Add student form
│       ├── StudentQRModal.tsx   # QR code modal
│       └── StudentViewModal.tsx # View student modal
├── teachers/
│   ├── page.tsx           # Teacher management
│   └── components/
│       ├── TeacherCard.tsx      # Teacher card
│       ├── TeacherEditModal.tsx # Edit teacher modal
│       ├── TeacherForm.tsx      # Add teacher form
│       └── TeacherViewModal.tsx # View teacher modal
├── setup/
│   └── page.tsx           # School setup & seeding
├── exams/                 # (Empty - Missing implementation)
└── settings/              # (Empty - Missing implementation)
```

#### 🌐 API Routes
```
app/api/
├── auth/
│   ├── signin/
│   │   └── route.ts        # Email/password login
│   ├── signup/
│   │   └── route.ts        # User registration
│   └── qr-login/
│       └── route.ts        # QR code authentication
├── admin/
│   ├── grades/
│   │   └── route.ts        # Grade management API
│   ├── sections/
│   │   └── route.ts        # Section management API
│   └── teachers/
│       └── route.ts        # Teacher management API
├── students/
│   └── register/
│       └── route.ts        # Student registration API
├── seed-users/
│   └── route.ts           # User seeding API
└── seed-auth-users/
    └── route.ts           # Auth user seeding API
```

#### 🧪 Development & Testing
```
app/dashboard/
└── page.tsx               # Legacy dashboard (to be removed?)

app/test/
└── page.tsx               # QR testing page

app/create-auth-users/
└── page.tsx               # Auth user creation utility

app/seed-users/
└── page.tsx               # User seeding utility
```

## 🚨 Current Structure Issues

### ❌ Problems Identified

1. **Route Duplication**
   - `app/login/student/page.tsx` vs `app/student/login/page.tsx`
   - `app/login/teacher/page.tsx` vs `app/teacher/login/page.tsx`

2. **Inconsistent Organization**
   - Mixed feature-based and role-based organization
   - Some components in wrong locations
   - Utility pages scattered in app directory

3. **Missing Structure**
   - No dedicated `features/` organization
   - No clear separation of concerns
   - Missing admin exam management
   - No proper error pages

4. **Legacy Files**
   - Old dashboard page
   - Duplicate login routes
   - Test utilities in production structure

### ✅ Well-Organized Areas

1. **Library Structure** - Clean separation of services, types, and utilities
2. **Component System** - Good card and UI component organization
3. **API Routes** - Logical REST-like structure
4. **Shared Hooks** - Centralized data fetching logic

## 🎯 Recommended Refactoring Structure

### 📁 Proposed New Organization

```
GREENTECH/
├── app/
│   ├── (auth)/                    # Auth route group
│   │   ├── login/
│   │   │   ├── page.tsx          # General login
│   │   │   ├── admin/page.tsx    # Admin login
│   │   │   ├── teacher/page.tsx  # Teacher login
│   │   │   └── student/page.tsx  # Student login
│   │   └── layout.tsx            # Auth layout
│   ├── (dashboard)/              # Dashboard route group
│   │   ├── admin/
│   │   │   ├── layout.tsx        # Admin layout
│   │   │   ├── page.tsx          # Admin dashboard
│   │   │   ├── students/         # Student management
│   │   │   ├── teachers/         # Teacher management
│   │   │   ├── exams/            # Exam management
│   │   │   └── settings/         # Admin settings
│   │   ├── teacher/
│   │   │   ├── layout.tsx        # Teacher layout
│   │   │   ├── page.tsx          # Teacher dashboard
│   │   │   └── exams/            # Teacher exam management
│   │   └── student/
│   │       ├── layout.tsx        # Student layout
│   │       ├── page.tsx          # Student dashboard
│   │       └── exams/            # Student exam taking
│   ├── api/                      # API routes (keep current structure)
│   ├── globals.css
│   ├── layout.tsx               # Root layout
│   └── page.tsx                 # Landing page
├── components/                   # Move from app/components
│   ├── ui/                      # Base UI components
│   ├── forms/                   # Form components
│   ├── cards/                   # Card components
│   └── layout/                  # Layout components
├── features/                     # Feature-based organization
│   ├── auth/
│   ├── students/
│   ├── teachers/
│   ├── exams/
│   └── admin/
├── lib/                         # Keep current structure
├── hooks/                       # Move from app/hooks
└── types/                       # Expand type definitions
```

### 🔄 Migration Benefits

1. **Route Groups** - Clean separation of auth vs dashboard routes
2. **Feature Organization** - Logical grouping of related functionality
3. **Component Hierarchy** - Better component organization
4. **Reduced Duplication** - Eliminate duplicate routes
5. **Scalability** - Easier to add new features and roles
