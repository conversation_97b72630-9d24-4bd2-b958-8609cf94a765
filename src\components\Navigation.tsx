'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/app/contexts/AuthContext';
import { AcademicCapIcon, ClipboardDocumentListIcon, UserPlusIcon, UserCircleIcon, CheckCircleIcon, Cog6ToothIcon, ArrowRightOnRectangleIcon } from "@heroicons/react/24/outline";
import { Button } from './ui/Button';

const navy = "#1A2340";
const lightCream = "#FAFAF6";

export type Section = 'dashboard' | 'students' | 'teachers' | 'exams' | 'activity' | 'settings';

const Sidebar = ({ activeSection, setActiveSection }: { activeSection: Section, setActiveSection: React.Dispatch<React.SetStateAction<Section>> }) => (
  <aside className={`flex flex-col justify-between h-screen w-64 border-r border-[${navy}] bg-[${lightCream}] px-6 py-8`} style={{ borderRadius: 0 }}>
    <div>
      {/* Logo */}
      <div className="flex items-center gap-2 mb-10">
        <AcademicCapIcon className={`w-8 h-8 text-[${navy}]`} aria-hidden="true" />
        <span className={`text-xl font-bold text-[${navy}] tracking-tight`}>Greentech</span>
      </div>
      {/* Navigation */}
      <nav className="flex flex-col gap-2" aria-label="Main Navigation">
        <button
          className={`flex items-center gap-3 text-base font-semibold px-3 py-2 focus:outline-none ${activeSection === 'dashboard' ? `bg-[${navy}] text-white` : `text-[${navy}]`} `}
          onClick={() => setActiveSection('dashboard')}
          aria-label="Dashboard"
        >
          <ClipboardDocumentListIcon className={`w-5 h-5 ${activeSection === 'dashboard' ? 'text-white' : `text-[${navy}]`}`} aria-hidden="true" />
          Dashboard
        </button>
        <button
          className={`flex items-center gap-3 text-base font-semibold px-3 py-2 focus:outline-none ${activeSection === 'students' ? `bg-[${navy}] text-white` : `text-[${navy}]`} `}
          onClick={() => setActiveSection('students')}
          aria-label="Student Management"
        >
          <AcademicCapIcon className={`w-5 h-5 ${activeSection === 'students' ? 'text-white' : `text-[${navy}]`}`} aria-hidden="true" />
          Students
        </button>
        <button
          className={`flex items-center gap-3 text-base font-semibold px-3 py-2 focus:outline-none ${activeSection === 'teachers' ? `bg-[${navy}] text-white` : `text-[${navy}]`} `}
          onClick={() => setActiveSection('teachers')}
          aria-label="Teacher Management"
        >
          <UserPlusIcon className={`w-5 h-5 ${activeSection === 'teachers' ? 'text-white' : `text-[${navy}]`}`} aria-hidden="true" />
          Teachers
        </button>
        <button
          className={`flex items-center gap-3 text-base font-semibold px-3 py-2 focus:outline-none ${activeSection === 'exams' ? `bg-[${navy}] text-white` : `text-[${navy}]`} `}
          onClick={() => setActiveSection('exams')}
          aria-label="Exam Review"
        >
          <ClipboardDocumentListIcon className={`w-5 h-5 ${activeSection === 'exams' ? 'text-white' : `text-[${navy}]`}`} aria-hidden="true" />
          Exams
        </button>
        <button
          className={`flex items-center gap-3 text-base font-semibold px-3 py-2 focus:outline-none ${activeSection === 'activity' ? `bg-[${navy}] text-white` : `text-[${navy}]`} `}
          onClick={() => setActiveSection('activity')}
          aria-label="Activity Log"
        >
          <CheckCircleIcon className={`w-5 h-5 ${activeSection === 'activity' ? 'text-white' : `text-[${navy}]`}`} aria-hidden="true" />
          Activity Log
        </button>
        <button
          className={`flex items-center gap-3 text-base font-semibold px-3 py-2 focus:outline-none ${activeSection === 'settings' ? `bg-[${navy}] text-white` : `text-[${navy}]`} `}
          onClick={() => setActiveSection('settings')}
          aria-label="Settings"
        >
          <Cog6ToothIcon className={`w-5 h-5 ${activeSection === 'settings' ? 'text-white' : `text-[${navy}]`}`} aria-hidden="true" />
          Settings
        </button>
      </nav>
    </div>
    {/* User Profile */}
    <div className="flex items-center gap-3 mt-10">
      <UserCircleIcon className={`w-8 h-8 text-[${navy}]`} aria-hidden="true" />
      <div>
        <div className={`text-sm font-semibold text-[${navy}]`}>Admin</div>
        <div className={`text-xs text-[${navy}] opacity-70`}><EMAIL></div>
      </div>
    </div>
  </aside>
);

const Navigation: React.FC = () => {
  const { user, signOut, loading } = useAuth();
  const router = useRouter();

  const handleLoginClick = () => {
    router.push('/login');
  };

  const handleLogout = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <>
      <nav className="fixed top-0 w-full bg-white/95 backdrop-blur-sm border-b-2 border-[#84a98c] shadow-sm z-40">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center h-18">
            <div className="flex items-center space-x-4">
              <img src="/logo.png" alt="Pinagbarilan Elementary School Logo" className="w-12 h-12 object-contain" />
              <div className="flex flex-col">
                <span className="text-xl text-[#57735d] font-semibold leading-tight">Pinagbarilan</span>
                <span className="text-sm text-[#6b8e72] font-medium">Elementary School | GreenTech Assessment</span>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-3">
              {loading ? (
                <div className="bg-gray-100 text-gray-600 px-4 py-2 flex items-center space-x-2 border border-gray-300">
                  <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span className="font-medium">Loading...</span>
                </div>
              ) : user ? (
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-[#57735d] font-medium">
                    Welcome, {user.email}
                  </span>
                  <Button
                    onClick={handleLogout}
                    variant="filled"
                    theme="primary"
                    size="md"
                    radius="none"
                    leftIcon={<ArrowRightOnRectangleIcon className="w-4 h-4" />}
                  >
                    Logout
                  </Button>
                </div>
              ) : (
                <Button
                  onClick={handleLoginClick}
                  variant="filled"
                  theme="primary"
                  size="md"
                  radius="none"
                  href="/login"
                  navigationLoading={true}
                  rightIcon={<ArrowRightOnRectangleIcon className="w-4 h-4" />}
                >
                  Login
                </Button>
              )}
            </div>
          </div>
        </div>
      </nav>
    </>
  );
};

export { Sidebar };
export default Navigation; 