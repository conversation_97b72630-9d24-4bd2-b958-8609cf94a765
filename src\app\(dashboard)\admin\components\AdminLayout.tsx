"use client";
import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '../../../contexts/AuthContext';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../../../../lib/firebase';
import {
  ShieldCheckIcon,
  AcademicCapIcon,
  UserPlusIcon,
  ClipboardDocumentListIcon,
  PowerIcon,
  Cog6ToothIcon,
  BuildingLibraryIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { useAdminDisplayName } from '../../../../hooks/useAdminProfile';

const navItems = [
  { key: "overview", label: "Dashboard", icon: ClipboardDocumentListIcon, href: "/admin/dashboard" },
  { key: "academic-structure", label: "Configure", icon: BuildingLibraryIcon, href: "/admin/academic-structure" },
  { key: "students", label: "Students", icon: AcademicCapIcon, href: "/admin/students" },
  { key: "teachers", label: "Teachers", icon: UserPlusIcon, href: "/admin/teachers" },
  { key: "exams", label: "Exams", icon: ClipboardDocumentListIcon, href: "/admin/exams" },
  { key: "settings", label: "Settings", icon: Cog6ToothIcon, href: "/admin/settings" },
];

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, loading, signOut } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const { displayName, isLoading: isLoadingProfile } = useAdminDisplayName();

  // Check if user is authorized admin
  useEffect(() => {
    const checkAdminAccess = async () => {
      if (loading) return; // Wait for auth to load

      if (!user) {
        // No user logged in, redirect to admin login
        router.push('/login/admin');
        return;
      }

      try {
        // Check if user is an admin in the admins collection
        const adminsQuery = query(
          collection(db, "admins"),
          where("email", "==", user.email),
          where("status", "==", "active")
        );
        const adminSnapshot = await getDocs(adminsQuery);

        if (!adminSnapshot.empty) {
          setIsAuthorized(true);
          setIsCheckingAuth(false);
          return;
        }

        // If not found in admins collection, check hardcoded admin emails as fallback
        const adminEmails = [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>"
        ];

        if (adminEmails.includes(user.email?.toLowerCase() || '')) {
          setIsAuthorized(true);
          setIsCheckingAuth(false);
          return;
        }

        // If no admin access found, deny access
        setIsAuthorized(false);
        setIsCheckingAuth(false);
        await signOut();
        router.push('/login/admin');
      } catch (error) {
        console.error("Error checking admin access:", error);
        setIsAuthorized(false);
        setIsCheckingAuth(false);
        router.push('/login/admin');
      }
    };

    checkAdminAccess();
  }, [user, loading, router, signOut]);

  const handleLogout = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const isActiveRoute = (href: string) => {
    if (href === '/admin/dashboard') {
      return pathname === '/admin/dashboard';
    }
    return pathname.startsWith(href);
  };

  // Show loading while checking authentication
  if (loading || isCheckingAuth) {
    return (
      <div className="min-h-screen bg-[#FAFAF6] flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-[#84a98c] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-[#84a98c] font-medium">Verifying admin access...</p>
        </div>
      </div>
    );
  }

  // Don't render anything if not authorized (will redirect)
  if (!isAuthorized) {
    return null;
  }

  return (
    <div className="min-h-screen bg-[#FAFAF6] flex flex-col theme-admin">
      {/* Header - Sticky */}
      <header className="sticky top-0 z-50 w-full border-b-2 border-[#84a98c] bg-white px-8 py-4 flex items-center justify-between">
        <div className="flex items-center">
          <ShieldCheckIcon className="w-8 h-8 text-[#84a98c] mr-3" />
          <h1 className="text-2xl font-bold text-[#84a98c]">Admin Dashboard</h1>
        </div>
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-600">
            Welcome, {isLoadingProfile ? 'Loading...' : displayName || user?.email}
          </span>
          <button
            onClick={handleLogout}
            className="flex items-center space-x-2 px-4 py-2 border-2 border-[#84a98c] text-[#84a98c] hover:bg-[#84a98c] hover:text-white transition-colors duration-200 cursor-pointer"
          >
            <PowerIcon className="w-4 h-4" />
            <span>Logout</span>
          </button>
        </div>
      </header>

      <div className="flex flex-1">
        {/* Sidebar Navigation - Sticky */}
        <nav className="sticky top-[73px] h-[calc(100vh-73px)] w-64 bg-white border-r-2 border-[#84a98c] p-6 flex flex-col">
          <div className="flex-1 space-y-2">
            {navItems.map((item) => (
              <button
                key={item.key}
                onClick={() => router.push(item.href)}
                className={`w-full flex items-center space-x-3 px-4 py-3 text-left transition-colors duration-200 cursor-pointer ${
                  isActiveRoute(item.href)
                    ? 'bg-[#84a98c] text-white shadow-md'
                    : 'text-[#84a98c] hover:bg-[#84a98c]/10 hover:text-[#6b8e73]'
                }`}
              >
                <item.icon className="w-5 h-5" />
                <span className="font-medium">{item.label}</span>
              </button>
            ))}
          </div>

          {/* Profile Button at Bottom */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <button
              onClick={() => router.push('/admin/profile')}
              className={`w-full flex items-center space-x-3 px-4 py-3 text-left transition-colors duration-200 cursor-pointer ${
                pathname === '/admin/profile'
                  ? 'bg-[#84a98c] text-white shadow-md'
                  : 'text-[#84a98c] hover:bg-[#84a98c]/10 hover:text-[#6b8e73]'
              }`}
            >
              <UserIcon className="w-5 h-5" />
              <span className="font-medium">Profile</span>
            </button>
          </div>
        </nav>

        {/* Main Content - Scrollable */}
        <main className="flex-1 p-8 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
}
