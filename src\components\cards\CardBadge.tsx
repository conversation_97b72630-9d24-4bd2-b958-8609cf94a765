"use client";
import React from 'react';
import { cn } from '@/lib/utils';

interface CardBadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'error' | 'warning' | 'info' | 'custom';
  size?: 'sm' | 'md';
  className?: string;
  style?: React.CSSProperties;
}

const variantMap = {
  default: 'bg-gray-100 text-gray-800',
  success: 'bg-green-100 text-green-800',
  error: 'bg-red-100 text-red-800',
  warning: 'bg-yellow-100 text-yellow-800',
  info: 'bg-blue-100 text-blue-800',
  custom: '', // Will use style prop or className
};

const sizeMap = {
  sm: 'px-2 py-1 text-xs',
  md: 'px-3 py-1 text-sm',
};

export default function CardBadge({ 
  children, 
  variant = 'default',
  size = 'sm',
  className,
  style
}: CardBadgeProps) {
  return (
    <span 
      className={cn(
        'inline-block font-medium rounded',
        variantMap[variant],
        sizeMap[size],
        className
      )}
      style={style}
    >
      {children}
    </span>
  );
}
