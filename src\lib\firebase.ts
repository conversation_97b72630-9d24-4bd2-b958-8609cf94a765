// Import the functions you need from the SDKs you need
import { initializeApp, getApps, getApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getAnalytics } from "firebase/analytics";
import { getFirestore } from "firebase/firestore";

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyDb8SRh31DIhYvNkFXmUvSJMlGZwycgBHg",
  authDomain: "greentech-assessment.firebaseapp.com",
  projectId: "greentech-assessment",
  storageBucket: "greentech-assessment.appspot.com",
  messagingSenderId: "759195907789",
  appId: "1:759195907789:web:4b0fc2f038a61ce1d2db84",
  measurementId: "G-7K6JSCHJ8S"
};

// Initialize Firebase only if it hasn't been initialized already
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();
const db = getFirestore(app);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Analytics (only in browser environment)
export const analytics = typeof window !== 'undefined' && typeof document !== 'undefined' ? getAnalytics(app) : null;

export { app, db }; 