import { NextRequest, NextResponse } from 'next/server';
import { getTeachersWithAssignments } from '@/lib/seeders/teacherSeeder';

export async function GET(request: NextRequest) {
  try {
    const teachers = await getTeachersWithAssignments();
    
    return NextResponse.json({
      success: true,
      data: teachers
    });

  } catch (error) {
    console.error('❌ Error fetching teachers with assignments:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}
