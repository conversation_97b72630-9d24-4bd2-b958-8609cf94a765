import { NextRequest, NextResponse } from 'next/server';
import { sectionService } from '@/lib/services/gradeService';

// GET /api/admin/sections - Get sections (optionally filtered by grade)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const gradeId = searchParams.get('gradeId');

    let sections;
    
    if (gradeId) {
      sections = await sectionService.getSectionsByGrade(gradeId);
    } else {
      sections = await sectionService.getAllSections();
    }

    return NextResponse.json({
      success: true,
      data: sections,
      message: 'Sections retrieved successfully'
    });

  } catch (error: any) {
    console.error('Error fetching sections:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch sections',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// POST /api/admin/sections - Create new section
export async function POST(request: NextRequest) {
  try {
    const sectionData = await request.json();
    const newSection = await sectionService.createSection(sectionData);

    return NextResponse.json({
      success: true,
      data: newSection,
      message: 'Section created successfully'
    }, { status: 201 });

  } catch (error: any) {
    console.error('Error creating section:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create section',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// PUT /api/admin/sections - Update section
export async function PUT(request: NextRequest) {
  try {
    const { id, data } = await request.json();

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Section ID is required'
        },
        { status: 400 }
      );
    }

    await sectionService.updateSection(id, data);

    return NextResponse.json({
      success: true,
      message: 'Section updated successfully'
    });

  } catch (error: any) {
    console.error('Error updating section:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update section',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/sections - Delete section
export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Section ID is required'
        },
        { status: 400 }
      );
    }

    await sectionService.deleteSection(id);

    return NextResponse.json({
      success: true,
      message: 'Section deleted successfully'
    });

  } catch (error: any) {
    console.error('Error deleting section:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete section',
        details: error.message
      },
      { status: 500 }
    );
  }
}
