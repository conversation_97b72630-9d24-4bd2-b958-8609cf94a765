"use client";
import React from 'react';
import { cn } from '@/lib/utils';

interface CardContentProps {
  children: React.ReactNode;
  className?: string;
  spacing?: 'sm' | 'md' | 'lg';
}

const spacingMap = {
  sm: 'space-y-1',
  md: 'space-y-2',
  lg: 'space-y-3',
};

export default function CardContent({ 
  children, 
  className,
  spacing = 'md'
}: CardContentProps) {
  return (
    <div className={cn(
      'mb-4',
      spacingMap[spacing],
      className
    )}>
      {children}
    </div>
  );
}
