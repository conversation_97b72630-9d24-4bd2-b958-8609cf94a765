// COLLECTION: sections
{
  _id: "sec_4_rizal_2024",
  gradeLevel: 4,
  sectionName: "Rizal",
  schoolYear: "2024-2025",
  subjects: ["English", "Math", "Science", "Filipino", "ESP"],
  students: ["stud_001", "stud_002", ...],
  createdAt: ISODate()
}

// COLLECTION: teachers
{
  _id: "tchr_juan_dela_cruz",
  email: "<EMAIL>",
  fullName: "Juan <PERSON>",
  assignedSections: [
    {
      sectionId: "sec_4_rizal_2024",
      subject: "English"
    },
    {
      sectionId: "sec_4_bonifacio_2024", 
      subject: "English"
    }
  ],
  createdAt: ISODate()
}

// COLLECTION: exams
{
  _id: "exam_eng4_rizal_midterm",
  teacherId: "tchr_juan_dela_cruz",
  sectionId: "sec_4_rizal_2024",
  subject: "English",
  title: "Midterm Exam - English Grade 4 Rizal",
  type: "exam", // "exam" or "quiz"
  status: "approved", // "draft", "pending", "approved", "rejected"
  questions: [...],
  totalPoints: 100,
  timeLimit: 60, // minutes
  scheduledDate: ISODate("2024-10-15T08:00:00Z"),
  createdAt: ISODate()
}

// COLLECTION: studentExams (submissions)
{
  _id: "sub_stud001_exam_eng4_rizal_midterm",
  studentId: "stud_001",
  examId: "exam_eng4_rizal_midterm",
  answers: [...],
  autoGradedScore: 75, // for objective questions
  teacherGradedScore: 20, // for essay questions (manual)
  totalScore: 95,
  submittedAt: ISODate("2024-10-15T08:45:00Z"),
  timeSpent: 45, // minutes
  feedback: "I think my essay deserved more points",
  responseToFeedback: "Essay lacked supporting details. See rubric breakdown...",
  status: "graded"
}

// COLLECTION: students
{
  _id: "stud_001",
  qrCode: "unique_qr_hash",
  fullName: "Maria Clara Santos",
  sectionId: "sec_4_rizal_2024",
  email: "<EMAIL>",
  createdAt: ISODate()
}