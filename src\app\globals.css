@import "tailwindcss";
@import './styles/colors.css';

:root {
  --background: #fdf2f8;
  --foreground: #171717;
  --font-poppins: 'Poppins', sans-serif;
  --sage-600: #84a98c;
  --sage-700: #6b8e72;
  --blush: #fdf2f8;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-poppins);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-poppins);
  font-weight: 300;
}

/* Custom font classes */
.font-poppins {
  font-family: var(--font-poppins);
  font-weight: 300;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Elevator animations */
@keyframes scroll-down {
  0% {
    transform: translateY(-50%);
  }
  100% {
    transform: translateY(0%);
  }
}

@keyframes scroll-up {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(-100%);
  }
}

.animate-scroll-down {
  animation: scroll-down 10s linear infinite;
}

.animate-scroll-up {
  animation: scroll-up 15s linear infinite;
}

.animate-scroll-down-delayed {
  animation: scroll-down 15s linear infinite;
  animation-delay: -4s;
}

/* Hover to pause animation */
.hover\:pause-animation:hover {
  animation-play-state: paused;
}

/* Custom color classes */
.bg-sage-600 {
  background-color: var(--sage-600);
}

.bg-sage-700 {
  background-color: var(--sage-700);
}

.bg-sage-50 {
  background-color: #f0f9f5;
}

.bg-sage-100 {
  background-color: #dcf0e3;
}

.text-sage-600 {
  color: var(--sage-600);
}

.text-sage-700 {
  color: var(--sage-700);
}

.text-sage-800 {
  color: #5a7c63;
}

.border-sage-600 {
  border-color: var(--sage-600);
}

.bg-blush {
  background-color: var(--blush);
}

.text-blush {
  color: var(--blush);
}

.hover\:bg-sage-700:hover {
  background-color: var(--sage-700);
}

.hover\:bg-sage-100:hover {
  background-color: #dcf0e3;
}

.hover\:text-sage-600:hover {
  color: var(--sage-600);
}

/* Fade blur overlay classes */
.fade-top {
  background: linear-gradient(180deg, rgba(253, 242, 248, 1) 0%, rgba(253, 242, 248, 0.8) 50%, rgba(253, 242, 248, 0) 100%);

}

.fade-bottom {
  background: linear-gradient(0deg, rgba(253, 242, 248, 1) 0%, rgba(253, 242, 248, 0.8) 50%, rgba(253, 242, 248, 0) 100%);

}

/* Modal backdrop fixes */
[data-radix-popper-content-wrapper] {
  z-index: 9999 !important;
}

[data-state="open"][data-radix-alert-dialog-overlay] {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  min-width: 100vw !important;
  min-height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  z-index: 9999 !important;
  margin: 0 !important;
  padding: 0 !important;
}

[data-state="open"][data-radix-dialog-overlay] {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  min-width: 100vw !important;
  min-height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  z-index: 9999 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure modal content is above backdrop */
[data-radix-alert-dialog-content] {
  z-index: 10000 !important;
}

[data-radix-dialog-content] {
  z-index: 10000 !important;
}
