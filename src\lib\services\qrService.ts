import QRCode from 'qrcode';
import { v4 as uuidv4 } from 'uuid';

export interface QRGenerationOptions {
  width?: number;
  margin?: number;
  color?: {
    dark?: string;
    light?: string;
  };
}

export const qrService = {
  /**
   * Generate a QR code as base64 data URL
   * @param data - The data to encode in the QR code
   * @param options - QR code generation options
   * @returns Promise<string> - Base64 data URL of the QR code
   */
  async generateQRCode(data: string, options: QRGenerationOptions = {}): Promise<string> {
    try {
      const defaultOptions = {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        ...options
      };

      const qrCodeDataURL = await QRCode.toDataURL(data, defaultOptions);
      return qrCodeDataURL;
    } catch (error) {
      console.error('Error generating QR code:', error);
      throw new Error('Failed to generate QR code');
    }
  },

  /**
   * Generate a unique hash for student authentication
   * @param lrn - Student's Learning Reference Number
   * @returns string - Unique hash for the student
   */
  generateStudentHash(lrn: string): string {
    // Create a unique hash using UUID and LRN
    const timestamp = Date.now().toString();
    const randomId = uuidv4();
    const hashData = `${lrn}-${timestamp}-${randomId}`;
    
    // Create a shorter, more manageable hash
    return Buffer.from(hashData).toString('base64').replace(/[+/=]/g, '').substring(0, 32);
  },

  /**
   * Generate QR code for student login
   * @param lrn - Student's Learning Reference Number
   * @param hash - Student's authentication hash
   * @param options - QR code generation options
   * @returns Promise<string> - Base64 data URL of the QR code
   */
  async generateStudentQR(lrn: string, hash: string, options: QRGenerationOptions = {}): Promise<string> {
    try {
      // The QR code contains just the hash for authentication
      const qrOptions = {
        width: 300,
        margin: 2,
        color: {
          dark: '#1A2340', // Navy blue for better branding
          light: '#FFFFFF'
        },
        ...options
      };

      return await this.generateQRCode(hash, qrOptions);
    } catch (error) {
      console.error('Error generating student QR code:', error);
      throw new Error('Failed to generate student QR code');
    }
  }
};
