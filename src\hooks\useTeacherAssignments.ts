import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { teacherAssignmentService, TeacherAssignmentData, TeacherSubjectAssignment } from '../lib/services/teacherAssignmentService';
import toast from 'react-hot-toast';

/**
 * Hook to get assignments by teacher ID
 */
export function useTeacherAssignmentsByTeacher(teacherId: string) {
  return useQuery({
    queryKey: ['teacherAssignments', teacherId],
    queryFn: () => teacherAssignmentService.getAssignmentsByTeacher(teacherId),
    enabled: !!teacherId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get assignments with details by teacher ID
 */
export function useTeacherAssignmentsWithDetailsByTeacher(teacherId: string) {
  return useQuery({
    queryKey: ['teacherAssignmentsWithDetails', teacherId],
    queryFn: () => teacherAssignmentService.getAssignmentsByTeacher(teacherId),
    enabled: !!teacherId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get all assignments
 */
export function useAllTeacherAssignments() {
  return useQuery({
    queryKey: ['allTeacherAssignments'],
    queryFn: () => teacherAssignmentService.getAllAssignments(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to create multiple assignments
 */
export function useCreateTeacherAssignments() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (assignments: TeacherAssignmentData[]) => 
      teacherAssignmentService.createMultipleAssignments(assignments),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teacherAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['teacherAssignmentsWithDetails'] });
      queryClient.invalidateQueries({ queryKey: ['allTeacherAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      toast.success('Assignments created successfully!');
    },
    onError: (error: any) => {
      console.error('Error creating assignments:', error);
      toast.error('Failed to create assignments. Please try again.');
    }
  });
}

/**
 * Hook to update teacher assignments (replace all assignments for a teacher)
 */
export function useUpdateTeacherAssignments() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ teacherId, assignments }: { teacherId: string; assignments: TeacherAssignmentData[] }) => {
      // Delete existing assignments for this teacher
      await teacherAssignmentService.deleteAssignmentsByTeacher(teacherId);
      
      // Create new assignments
      if (assignments.length > 0) {
        await teacherAssignmentService.createMultipleAssignments(assignments);
      }
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['teacherAssignments', variables.teacherId] });
      queryClient.invalidateQueries({ queryKey: ['teacherAssignmentsWithDetails', variables.teacherId] });
      queryClient.invalidateQueries({ queryKey: ['allTeacherAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      toast.success('Assignments updated successfully!');
    },
    onError: (error: any) => {
      console.error('Error updating assignments:', error);
      toast.error('Failed to update assignments. Please try again.');
    }
  });
}

/**
 * Hook to delete assignments by teacher
 */
export function useDeleteTeacherAssignments() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (teacherId: string) => teacherAssignmentService.deleteAssignmentsByTeacher(teacherId),
    onSuccess: (_, teacherId) => {
      queryClient.invalidateQueries({ queryKey: ['teacherAssignments', teacherId] });
      queryClient.invalidateQueries({ queryKey: ['teacherAssignmentsWithDetails', teacherId] });
      queryClient.invalidateQueries({ queryKey: ['allTeacherAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      toast.success('Assignments deleted successfully!');
    },
    onError: (error: any) => {
      console.error('Error deleting assignments:', error);
      toast.error('Failed to delete assignments. Please try again.');
    }
  });
}

/**
 * Hook to delete a single assignment
 */
export function useDeleteTeacherAssignment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (assignmentId: string) => teacherAssignmentService.deleteAssignment(assignmentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teacherAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['teacherAssignmentsWithDetails'] });
      queryClient.invalidateQueries({ queryKey: ['allTeacherAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      toast.success('Assignment deleted successfully!');
    },
    onError: (error: any) => {
      console.error('Error deleting assignment:', error);
      toast.error('Failed to delete assignment. Please try again.');
    }
  });
}
