"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { signInWithEmailAndPassword, getAuth } from "firebase/auth";
import { ArrowLeftIcon, UserIcon, EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";
import { collection, query, where, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Button } from "../../../../components/ui/Button";

const TeacherLoginPage = () => {
  const [formData, setFormData] = useState({
    email: "",
    password: ""
  });
  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) setError("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    
    if (!formData.email || !formData.password) {
      setError("Please fill in all fields.");
      return;
    }

    setIsSubmitting(true);
    try {
      const auth = getAuth();
      await signInWithEmailAndPassword(auth, formData.email, formData.password);

      // Check if user is a teacher in Firestore
      const teachersQuery = query(
        collection(db, "teachers"), 
        where("email", "==", formData.email),
        where("status", "==", "active")
      );
      const teacherSnapshot = await getDocs(teachersQuery);

      if (teacherSnapshot.empty) {
        setError("Access denied. This account is not authorized as a teacher.");
        await auth.signOut();
        return;
      }

      // User is a teacher, redirect to teacher dashboard
      router.push("/teacher/dashboard");
    } catch (err: any) {
      console.error("Teacher login error:", err);
      
      let errorMessage = "Login failed. Please try again.";
      switch (err.code) {
        case 'auth/user-not-found':
          errorMessage = "No account found with this email address.";
          break;
        case 'auth/wrong-password':
          errorMessage = "Incorrect password. Please try again.";
          break;
        case 'auth/invalid-email':
          errorMessage = "Please enter a valid email address.";
          break;
        case 'auth/too-many-requests':
          errorMessage = "Too many failed attempts. Please try again later.";
          break;
        case 'auth/user-disabled':
          errorMessage = "This account has been disabled. Please contact an administrator.";
          break;
        default:
          errorMessage = err.message || errorMessage;
      }
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#faf7ff] to-[#f4efff] flex items-center justify-center px-4 theme-teacher">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <UserIcon className="w-16 h-16 text-[#9c6bff]" />
          </div>
          <h1 className="text-3xl font-bold text-[#7047c2] mb-2">Teacher Login</h1>
          <p className="text-[#9c6bff]">Enter your credentials to access your dashboard</p>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="bg-white border-2 border-[#ebe2ff] p-8 shadow-lg">
          {/* Email Field */}
          <div className="mb-6">
            <label htmlFor="email" className="block text-sm font-semibold text-[#7047c2] mb-2">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full px-4 py-3 border-2 border-[#ebe2ff] focus:outline-none focus:ring-2 focus:ring-[#b084ff] focus:ring-offset-2 transition-colors duration-200 text-[#7047c2] placeholder:text-[#b084ff]/60"
              placeholder="Enter your email"
              required
              disabled={isSubmitting}
            />
          </div>

          {/* Password Field */}
          <div className="mb-6">
            <label htmlFor="password" className="block text-sm font-semibold text-[#7047c2] mb-2">
              Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border-2 text-[#7047c2] border-[#ebe2ff] focus:outline-none focus:ring-2 focus:ring-[#b084ff] focus:ring-offset-2 pr-12 transition-colors duration-200 placeholder:text-[#b084ff]/60"
                placeholder="Enter your password"
                required
                disabled={isSubmitting}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-[#9c6bff] transition-colors duration-200"
                disabled={isSubmitting}
              >
                {showPassword ? (
                  <EyeSlashIcon className="w-5 h-5" />
                ) : (
                  <EyeIcon className="w-5 h-5" />
                )}
              </button>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-4 border-2 border-red-300 bg-red-50">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={isSubmitting}
            variant="filled"
            theme="teacher"
            size="lg"
            fullWidth
            radius="none"
            loading={isSubmitting}
            loadingText="Signing In..."
          >
            Sign In
          </Button>

          {/* Additional Info */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-600">
              Don't have an account? Contact your administrator to get access.
            </p>
          </div>
        </form>

        {/* Back Button */}
        <div className="mt-6 text-center">
          <Button
            onClick={() => router.push('/login')}
            variant="ghost"
            theme="teacher"
            size="sm"
            leftIcon={<ArrowLeftIcon className="w-4 h-4" />}
            href="/login"
            navigationLoading={true}
          >
            Back to Login Options
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TeacherLoginPage;
