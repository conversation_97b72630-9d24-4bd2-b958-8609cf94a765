/**
 * Subject Seeder
 * Creates K-12 curriculum subjects for each grade following <PERSON>'s schema
 * 
 * Structure: 8 core subjects per grade (English, Math, Science, Filipino, AP, ESP, MAPEH, TLE)
 */

import { db } from '@/lib/firebase';
import { collection, addDoc, getDocs, deleteDoc, writeBatch, doc, updateDoc } from 'firebase/firestore';
import { Grade, Subject } from '@/lib/types/school';

/**
 * Clear all subjects from the database
 */
export async function clearSubjects() {
  console.log('🧹 Clearing existing subjects...');
  
  try {
    const subjectsSnapshot = await getDocs(collection(db, 'subjects'));
    const batch = writeBatch(db);
    
    subjectsSnapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });
    
    if (subjectsSnapshot.docs.length > 0) {
      await batch.commit();
      console.log(`✅ Cleared ${subjectsSnapshot.docs.length} subjects`);
    } else {
      console.log('ℹ️ No subjects to clear');
    }
  } catch (error) {
    console.error('❌ Error clearing subjects:', error);
    throw error;
  }
}

/**
 * K-12 Curriculum subjects template
 */
const getSubjectTemplates = (gradeLevel: number) => [
  {
    name: 'English',
    code: `ENG${gradeLevel}`,
    description: 'English Language and Literature',
    color: '#3b82f6',
    hoursPerWeek: 5,
  },
  {
    name: 'Mathematics',
    code: `MATH${gradeLevel}`,
    description: 'Mathematics and Problem Solving',
    color: '#ef4444',
    hoursPerWeek: 5,
  },
  {
    name: 'Science',
    code: `SCI${gradeLevel}`,
    description: 'Science and Technology',
    color: '#10b981',
    hoursPerWeek: 4,
  },
  {
    name: 'Filipino',
    code: `FIL${gradeLevel}`,
    description: 'Filipino Language and Literature',
    color: '#f59e0b',
    hoursPerWeek: 4,
  },
  {
    name: 'Araling Panlipunan',
    code: `AP${gradeLevel}`,
    description: 'Social Studies and History',
    color: '#8b5cf6',
    hoursPerWeek: 3,
  },
  {
    name: 'ESP (Edukasyon sa Pagpapakatao)',
    code: `ESP${gradeLevel}`,
    description: 'Values Education',
    color: '#06b6d4',
    hoursPerWeek: 2,
  },
  {
    name: 'MAPEH',
    code: `MAPEH${gradeLevel}`,
    description: 'Music, Arts, Physical Education, and Health',
    color: '#ec4899',
    hoursPerWeek: 4,
  },
  {
    name: 'TLE (Technology and Livelihood Education)',
    code: `TLE${gradeLevel}`,
    description: 'Technology and Livelihood Education',
    color: '#84cc16',
    hoursPerWeek: 2,
  },
];

/**
 * Create subjects for all grades
 */
export async function seedSubjects(grades: Grade[]): Promise<Subject[]> {
  console.log('📖 Creating subjects for all grades...');
  
  const subjects: Subject[] = [];
  const now = new Date().toISOString();
  
  try {
    for (const grade of grades) {
      console.log(`   Creating subjects for ${grade.name}...`);
      const subjectTemplates = getSubjectTemplates(grade.level);
      
      for (const template of subjectTemplates) {
        const subjectData = {
          name: template.name,
          code: template.code,
          gradeId: grade.id,
          description: template.description,
          color: template.color,
          hoursPerWeek: template.hoursPerWeek,
          isActive: true,
          createdAt: now,
          updatedAt: now
        };
        
        const docRef = await addDoc(collection(db, 'subjects'), subjectData);
        
        const subject: Subject = {
          id: docRef.id,
          ...subjectData
        };
        
        subjects.push(subject);
        console.log(`     ✅ Created ${template.name} (${template.code})`);
      }
    }
    
    console.log(`✅ Successfully created ${subjects.length} subjects`);
    return subjects;
    
  } catch (error) {
    console.error('❌ Error creating subjects:', error);
    throw error;
  }
}

/**
 * Get all subjects
 */
export async function getAllSubjects(): Promise<Subject[]> {
  try {
    const subjectsSnapshot = await getDocs(collection(db, 'subjects'));
    const subjects: Subject[] = [];
    
    subjectsSnapshot.docs.forEach((doc) => {
      subjects.push({
        id: doc.id,
        ...doc.data()
      } as Subject);
    });
    
    return subjects.sort((a, b) => a.name.localeCompare(b.name));
  } catch (error) {
    console.error('❌ Error getting subjects:', error);
    throw error;
  }
}

/**
 * Get subjects by grade ID
 */
export async function getSubjectsByGrade(gradeId: string): Promise<Subject[]> {
  try {
    const subjectsSnapshot = await getDocs(collection(db, 'subjects'));
    const subjects: Subject[] = [];
    
    subjectsSnapshot.docs.forEach((doc) => {
      const data = doc.data();
      if (data.gradeId === gradeId) {
        subjects.push({
          id: doc.id,
          ...data
        } as Subject);
      }
    });
    
    return subjects.sort((a, b) => a.name.localeCompare(b.name));
  } catch (error) {
    console.error('❌ Error getting subjects by grade:', error);
    throw error;
  }
}

/**
 * Create a single subject
 */
export async function createSubject(subjectData: Omit<Subject, 'id' | 'createdAt' | 'updatedAt'>): Promise<Subject> {
  const now = new Date().toISOString();
  
  const data = {
    ...subjectData,
    createdAt: now,
    updatedAt: now
  };
  
  try {
    const docRef = await addDoc(collection(db, 'subjects'), data);
    
    return {
      id: docRef.id,
      ...data
    };
  } catch (error) {
    console.error('❌ Error creating subject:', error);
    throw error;
  }
}

/**
 * Update subject
 */
export async function updateSubject(subjectId: string, updates: Partial<Omit<Subject, 'id' | 'createdAt'>>): Promise<void> {
  try {
    const subjectRef = doc(db, 'subjects', subjectId);
    await updateDoc(subjectRef, {
      ...updates,
      updatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Error updating subject:', error);
    throw error;
  }
}

/**
 * Delete subject
 */
export async function deleteSubject(subjectId: string): Promise<void> {
  try {
    await deleteDoc(doc(db, 'subjects', subjectId));
  } catch (error) {
    console.error('❌ Error deleting subject:', error);
    throw error;
  }
}

/**
 * Check if subject has teacher assignments (for deletion validation)
 */
export async function subjectHasTeacherAssignments(subjectId: string): Promise<boolean> {
  try {
    const assignmentsSnapshot = await getDocs(collection(db, 'teacherAssignments'));
    
    for (const assignmentDoc of assignmentsSnapshot.docs) {
      const assignmentData = assignmentDoc.data();
      if (assignmentData.subjectId === subjectId) {
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error('❌ Error checking if subject has teacher assignments:', error);
    return true; // Err on the side of caution
  }
}
