import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy,
  Timestamp 
} from 'firebase/firestore';
import { db } from '../firebase';

export interface Teacher {
  id: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  status: 'active' | 'inactive';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// For backward compatibility and UI display
export interface TeacherWithAssignments extends Teacher {
  gradeIds?: string[];
  sectionIds?: string[];
  subjects?: string[];
  examIds?: string[];
  assignments?: TeacherAssignmentSummary[];
}

export interface TeacherAssignmentSummary {
  gradeId: string;
  gradeName: string;
  subjectId: string;
  subjectName: string;
  sectionIds: string[];
  sectionNames: string[];
}

export interface TeacherFormData {
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  status: 'active' | 'inactive';
}

export interface TeacherAssignmentFormData {
  gradeId: string;
  subjectId: string;
  sectionIds: string[];
}

export interface TeacherFormWithAssignments extends TeacherFormData {
  assignments: TeacherAssignmentFormData[];
}

// For the new assignment-based form
export interface TeacherFormWithAssignments extends TeacherFormData {
  assignments: TeacherAssignmentFormData[];
}

export interface TeacherAssignmentFormData {
  gradeId: string;
  subjectId: string;
  sectionIds: string[];
}

class TeacherService {
  private collection = collection(db, 'teachers');

  // Get all teachers
  async getAllTeachers(): Promise<Teacher[]> {
    try {
      const snapshot = await getDocs(
        query(this.collection, orderBy('lastName', 'asc'))
      );
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Teacher[];
    } catch (error) {
      console.error('Error fetching teachers:', error);
      throw new Error('Failed to fetch teachers');
    }
  }

  // Get teacher by ID
  async getTeacherById(id: string): Promise<Teacher | null> {
    try {
      const docRef = doc(this.collection, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        } as Teacher;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching teacher:', error);
      throw new Error('Failed to fetch teacher');
    }
  }

  // Get teacher by email
  async getTeacherByEmail(email: string): Promise<Teacher | null> {
    try {
      const q = query(this.collection, where('email', '==', email));
      const snapshot = await getDocs(q);
      
      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        return {
          id: doc.id,
          ...doc.data()
        } as Teacher;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching teacher by email:', error);
      throw new Error('Failed to fetch teacher');
    }
  }

  // Get teachers by grade
  async getTeachersByGrade(gradeId: string): Promise<Teacher[]> {
    try {
      const q = query(this.collection, where('gradeIds', 'array-contains', gradeId));
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Teacher[];
    } catch (error) {
      console.error('Error fetching teachers by grade:', error);
      throw new Error('Failed to fetch teachers by grade');
    }
  }

  // Get teachers by section (using assignments)
  async getTeachersBySection(sectionId: string): Promise<Teacher[]> {
    try {
      const { teacherAssignmentService } = await import('./teacherAssignmentService');
      const assignments = await teacherAssignmentService.getAssignmentsBySection(sectionId);
      const teacherIds = [...new Set(assignments.map(a => a.teacherId))];

      const teachers = await Promise.all(
        teacherIds.map(id => this.getTeacherById(id))
      );

      return teachers.filter(teacher => teacher !== null) as Teacher[];
    } catch (error) {
      console.error('Error fetching teachers by section:', error);
      throw new Error('Failed to fetch teachers by section');
    }
  }

  // Get teachers with their assignments
  async getTeachersWithAssignments(): Promise<TeacherWithAssignments[]> {
    try {
      const { teacherAssignmentService } = await import('./teacherAssignmentService');
      const { gradeService, sectionService } = await import('./gradeService');
      const { subjectService } = await import('./subjectService');

      const teachers = await this.getAllTeachers();
      const allAssignments = await teacherAssignmentService.getAllAssignments();
      const grades = await gradeService.getAllGrades();
      const sections = await sectionService.getAllSections();
      const subjects = await subjectService.getAllSubjects();

      return teachers.map(teacher => {
        const teacherAssignments = allAssignments.filter(a => a.teacherId === teacher.id);

        // Group assignments by grade and subject
        const assignmentGroups = teacherAssignments.reduce((acc, assignment) => {
          const key = `${assignment.gradeId}-${assignment.subjectId}`;
          if (!acc[key]) {
            acc[key] = {
              gradeId: assignment.gradeId,
              subjectId: assignment.subjectId,
              sectionIds: []
            };
          }
          acc[key].sectionIds.push(assignment.sectionId);
          return acc;
        }, {} as Record<string, { gradeId: string; subjectId: string; sectionIds: string[] }>);

        const assignments = Object.values(assignmentGroups).map(group => {
          const grade = grades.find(g => g.id === group.gradeId);
          const subject = subjects.find(s => s.id === group.subjectId);
          const sectionNames = group.sectionIds.map(sId => {
            const section = sections.find(s => s.id === sId);
            return section?.name || 'Unknown';
          });

          return {
            gradeId: group.gradeId,
            gradeName: grade?.name || 'Unknown',
            subjectId: group.subjectId,
            subjectName: subject?.name || 'Unknown',
            sectionIds: group.sectionIds,
            sectionNames
          };
        });

        // For backward compatibility
        const gradeIds = [...new Set(teacherAssignments.map(a => a.gradeId))];
        const sectionIds = [...new Set(teacherAssignments.map(a => a.sectionId))];
        const subjectIds = [...new Set(teacherAssignments.map(a => a.subjectId))];
        const subjectNames = subjectIds.map(id => {
          const subject = subjects.find(s => s.id === id);
          return subject?.name || 'Unknown';
        });

        return {
          ...teacher,
          assignments,
          gradeIds,
          sectionIds,
          subjects: subjectNames
        };
      });
    } catch (error) {
      console.error('Error fetching teachers with assignments:', error);
      throw new Error('Failed to fetch teachers with assignments');
    }
  }

  // Create new teacher (basic info only)
  async createTeacher(teacherData: TeacherFormData): Promise<string> {
    try {
      // Check if email already exists
      const existingTeacher = await this.getTeacherByEmail(teacherData.email);
      if (existingTeacher) {
        throw new Error('A teacher with this email already exists');
      }

      const now = Timestamp.now();
      const newTeacher = {
        ...teacherData,
        createdAt: now,
        updatedAt: now
      };

      const docRef = await addDoc(this.collection, newTeacher);
      return docRef.id;
    } catch (error) {
      console.error('Error creating teacher:', error);
      throw error;
    }
  }

  // Create teacher with assignments (new method)
  async createTeacherWithAssignments(teacherData: TeacherFormWithAssignments): Promise<string> {
    try {
      // Import here to avoid circular dependency
      const { teacherAssignmentService } = await import('./teacherAssignmentService');

      // Check if email already exists
      const existingTeacher = await this.getTeacherByEmail(teacherData.email);
      if (existingTeacher) {
        throw new Error('A teacher with this email already exists');
      }

      const now = Timestamp.now();

      // Create teacher basic info
      const basicTeacherData: TeacherFormData = {
        firstName: teacherData.firstName,
        middleName: teacherData.middleName,
        lastName: teacherData.lastName,
        email: teacherData.email,
        status: teacherData.status
      };

      const newTeacher = {
        ...basicTeacherData,
        createdAt: now,
        updatedAt: now
      };

      const docRef = await addDoc(this.collection, newTeacher);
      const teacherId = docRef.id;

      // Create assignments
      if (teacherData.assignments && teacherData.assignments.length > 0) {
        const assignments = teacherData.assignments.flatMap(assignment =>
          assignment.sectionIds.map(sectionId => ({
            teacherId,
            gradeId: assignment.gradeId,
            subjectId: assignment.subjectId,
            sectionId
          }))
        );

        await teacherAssignmentService.createMultipleAssignments(assignments);
      }

      return teacherId;
    } catch (error) {
      console.error('Error creating teacher with assignments:', error);
      throw error;
    }
  }

  // Update teacher
  async updateTeacher(id: string, updates: Partial<TeacherFormData>): Promise<void> {
    try {
      const docRef = doc(this.collection, id);
      
      // If email is being updated, check for duplicates
      if (updates.email) {
        const existingTeacher = await this.getTeacherByEmail(updates.email);
        if (existingTeacher && existingTeacher.id !== id) {
          throw new Error('A teacher with this email already exists');
        }
      }

      await updateDoc(docRef, {
        ...updates,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating teacher:', error);
      throw error;
    }
  }

  // Delete teacher
  async deleteTeacher(id: string): Promise<void> {
    try {
      // Check if teacher has exams
      const teacher = await this.getTeacherById(id);
      if (teacher && teacher.examIds && teacher.examIds.length > 0) {
        throw new Error('Cannot delete teacher with existing exams. Please remove or reassign exams first.');
      }

      const docRef = doc(this.collection, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting teacher:', error);
      throw error;
    }
  }

  // Add exam to teacher
  async addExamToTeacher(teacherId: string, examId: string): Promise<void> {
    try {
      const teacher = await this.getTeacherById(teacherId);
      if (!teacher) {
        throw new Error('Teacher not found');
      }

      const updatedExamIds = [...(teacher.examIds || []), examId];
      await this.updateTeacher(teacherId, { examIds: updatedExamIds } as any);
    } catch (error) {
      console.error('Error adding exam to teacher:', error);
      throw error;
    }
  }

  // Remove exam from teacher
  async removeExamFromTeacher(teacherId: string, examId: string): Promise<void> {
    try {
      const teacher = await this.getTeacherById(teacherId);
      if (!teacher) {
        throw new Error('Teacher not found');
      }

      const updatedExamIds = (teacher.examIds || []).filter(id => id !== examId);
      await this.updateTeacher(teacherId, { examIds: updatedExamIds } as any);
    } catch (error) {
      console.error('Error removing exam from teacher:', error);
      throw error;
    }
  }

  // Create teacher with assignments
  async createTeacherWithAssignments(data: TeacherFormWithAssignments): Promise<string> {
    try {
      // First create the teacher
      const teacherId = await this.createTeacher(data);

      // Then create assignments if any
      if (data.assignments && data.assignments.length > 0) {
        const { teacherAssignmentService } = await import('./teacherAssignmentService');

        const assignmentData = data.assignments.flatMap(assignment =>
          assignment.sectionIds.map(sectionId => ({
            teacherId,
            gradeId: assignment.gradeId,
            subjectId: assignment.subjectId,
            sectionId,
            schoolYear: new Date().getFullYear().toString()
          }))
        );

        await teacherAssignmentService.createMultipleAssignments(assignmentData);
      }

      return teacherId;
    } catch (error) {
      console.error('Error creating teacher with assignments:', error);
      throw error;
    }
  }
}

export const teacherService = new TeacherService();
