const fs = require('fs');
const path = require('path');

// Read the firestore data
const dataPath = path.join(__dirname, 'firestore-data.json');
const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));

console.log('🔥 FIRESTORE DATABASE SUMMARY\n');
console.log('=' .repeat(50));

// Admins
console.log('\n👨‍💼 ADMINS (2 documents):');
data.admins.forEach(admin => {
  console.log(`   • ${admin.firstName} ${admin.lastName} (${admin.email})`);
  console.log(`     Role: ${admin.role} | Position: ${admin.position}`);
});

// Grades
console.log('\n📚 GRADES (3 documents):');
data.grades.forEach(grade => {
  console.log(`   • ${grade.name} (Level ${grade.level})`);
  console.log(`     Status: ${grade.isActive ? 'Active' : 'Inactive'}`);
});

// Sections
console.log('\n🏫 SECTIONS (12 documents):');
const sectionsByGrade = {};
data.sections.forEach(section => {
  const gradeName = data.grades.find(g => g.id === section.gradeId)?.name || 'Unknown Grade';
  if (!sectionsByGrade[gradeName]) sectionsByGrade[gradeName] = [];
  sectionsByGrade[gradeName].push(section);
});

Object.entries(sectionsByGrade).forEach(([gradeName, sections]) => {
  console.log(`   ${gradeName}:`);
  sections.forEach(section => {
    console.log(`     • Section ${section.name} (Capacity: ${section.maxCapacity || 0})`);
  });
});

// Subjects
console.log('\n📖 SUBJECTS (24 documents):');
const subjectsByGrade = {};
data.subjects.forEach(subject => {
  const gradeName = data.grades.find(g => g.id === subject.gradeId)?.name || 'Unknown Grade';
  if (!subjectsByGrade[gradeName]) subjectsByGrade[gradeName] = [];
  subjectsByGrade[gradeName].push(subject);
});

Object.entries(subjectsByGrade).forEach(([gradeName, subjects]) => {
  console.log(`   ${gradeName}:`);
  subjects.forEach(subject => {
    console.log(`     • ${subject.name} (${subject.code})`);
  });
});

// Teachers
console.log('\n👨‍🏫 TEACHERS (9 documents):');
data.teachers.forEach(teacher => {
  console.log(`   • ${teacher.firstName} ${teacher.lastName}`);
  console.log(`     Email: ${teacher.email} | Status: ${teacher.status}`);
});

// Students
console.log('\n👨‍🎓 STUDENTS (332 documents):');
const studentsByGrade = {};
data.students.forEach(student => {
  const gradeName = data.grades.find(g => g.id === student.gradeId)?.name || 'Unknown Grade';
  if (!studentsByGrade[gradeName]) studentsByGrade[gradeName] = 0;
  studentsByGrade[gradeName]++;
});

Object.entries(studentsByGrade).forEach(([gradeName, count]) => {
  console.log(`   ${gradeName}: ${count} students`);
});

// Exams
console.log('\n📝 EXAMS (3 documents):');
data.exams.forEach(exam => {
  const subject = data.subjects.find(s => s.id === exam.subjectId);
  console.log(`   • ${exam.title}`);
  console.log(`     Subject: ${subject?.name || 'Unknown'} | Type: ${exam.type} | Status: ${exam.status}`);
  console.log(`     Questions: ${exam.questions?.length || 0} | Sections: ${exam.visibleToSectionIds?.length || 0}`);
});

// Results
console.log('\n📊 RESULTS (4 documents):');
data.results.forEach(result => {
  const student = data.students.find(s => s.id === result.studentId);
  const exam = data.exams.find(e => e.id === result.examId);
  console.log(`   • Student: ${student?.firstName || 'Unknown'} ${student?.lastName || ''}`);
  console.log(`     Exam: ${exam?.title || 'Unknown'} | Score: ${result.score}/${result.totalQuestions}`);
});

// Teacher Assignments
console.log('\n📋 TEACHER ASSIGNMENTS (24 documents):');
const assignmentsByTeacher = {};
data.teacherAssignments.forEach(assignment => {
  const teacher = data.teachers.find(t => t.id === assignment.teacherId);
  const teacherName = teacher ? `${teacher.firstName} ${teacher.lastName}` : 'Unknown Teacher';
  
  if (!assignmentsByTeacher[teacherName]) assignmentsByTeacher[teacherName] = [];
  assignmentsByTeacher[teacherName].push(assignment);
});

Object.entries(assignmentsByTeacher).forEach(([teacherName, assignments]) => {
  console.log(`   ${teacherName}:`);
  assignments.forEach(assignment => {
    const grade = data.grades.find(g => g.id === assignment.gradeId);
    const subject = data.subjects.find(s => s.id === assignment.subjectId);
    const section = data.sections.find(s => s.id === assignment.sectionId);
    
    console.log(`     • ${grade?.name || 'Unknown Grade'} - ${subject?.name || 'Unknown Subject'} - Section ${section?.name || 'Unknown'}`);
  });
});

console.log('\n' + '='.repeat(50));
console.log('🎉 Summary completed!');
