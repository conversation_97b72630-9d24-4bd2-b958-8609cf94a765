import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  updateDoc, 
  query, 
  where, 
  Timestamp 
} from 'firebase/firestore';
import { db } from '../firebase';

export interface AdminProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  employeeId: string;
  position: string;
  department: string;
  status: 'active' | 'inactive';
  role: 'principal' | 'assistant_principal' | 'admin';
  permissions: string[];
  phoneNumber?: string;
  address?: string;
  dateHired?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface AdminProfileUpdateData {
  firstName: string;
  lastName: string;
  middleName?: string;
  phoneNumber?: string;
  address?: string;
  position: string;
  department: string;
}

class AdminService {
  private collection = collection(db, 'admins');

  /**
   * Get admin profile by email
   */
  async getAdminByEmail(email: string): Promise<AdminProfile | null> {
    try {
      const q = query(this.collection, where('email', '==', email));
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }

      const doc = querySnapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data()
      } as AdminProfile;
    } catch (error) {
      console.error('Error fetching admin by email:', error);
      throw error;
    }
  }

  /**
   * Get admin profile by ID
   */
  async getAdminById(id: string): Promise<AdminProfile | null> {
    try {
      const docRef = doc(this.collection, id);
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) {
        return null;
      }

      return {
        id: docSnap.id,
        ...docSnap.data()
      } as AdminProfile;
    } catch (error) {
      console.error('Error fetching admin by ID:', error);
      throw error;
    }
  }

  /**
   * Update admin profile
   */
  async updateAdminProfile(id: string, updateData: AdminProfileUpdateData): Promise<void> {
    try {
      const docRef = doc(this.collection, id);
      
      const dataToUpdate = {
        ...updateData,
        updatedAt: Timestamp.now()
      };

      await updateDoc(docRef, dataToUpdate);
    } catch (error) {
      console.error('Error updating admin profile:', error);
      throw error;
    }
  }

  /**
   * Get admin full name
   */
  getFullName(admin: AdminProfile): string {
    const parts = [admin.firstName];
    if (admin.middleName) {
      parts.push(admin.middleName);
    }
    parts.push(admin.lastName);
    return parts.join(' ');
  }

  /**
   * Get admin display name (First + Last)
   */
  getDisplayName(admin: AdminProfile): string {
    return `${admin.firstName} ${admin.lastName}`;
  }
}

export const adminService = new AdminService();
