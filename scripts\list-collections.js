const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
try {
  const serviceAccount = require('../serviceAccountKey.json');
  
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: 'greentech-assessment'
  });

  console.log('✅ Firebase Admin initialized successfully');
} catch (error) {
  console.error('❌ Error initializing Firebase:', error.message);
  process.exit(1);
}

const db = admin.firestore();

async function listCollections() {
  try {
    console.log('🔥 Listing Firestore Collections...\n');
    
    // Try to list collections
    const collections = await db.listCollections();
    
    console.log(`📚 Found ${collections.length} collections:`);
    
    for (const collection of collections) {
      console.log(`   - ${collection.id}`);
      
      // Get document count for each collection
      try {
        const snapshot = await collection.get();
        console.log(`     📄 ${snapshot.size} documents`);
      } catch (error) {
        console.log(`     ❌ Error counting documents: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error listing collections:', error.message);
    
    // Fallback: try known collections
    console.log('\n🔄 Trying known collections...');
    const knownCollections = ['grades', 'sections', 'subjects', 'students', 'teachers'];
    
    for (const collectionName of knownCollections) {
      try {
        const snapshot = await db.collection(collectionName).get();
        console.log(`✅ ${collectionName}: ${snapshot.size} documents`);
      } catch (error) {
        console.log(`❌ ${collectionName}: ${error.message}`);
      }
    }
  }
}

// Run the script
listCollections()
  .then(() => {
    console.log('\n🎉 Collection listing completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
