"use client";
import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '../../../contexts/AuthContext';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../../../../lib/firebase';
import {
  AcademicCapIcon,
  ClipboardDocumentListIcon,
  PowerIcon,
  UserIcon,
  BookOpenIcon
} from '@heroicons/react/24/outline';
import { useTeacherDisplayName, useTeacherSectionsByGrade } from '../../../../hooks/useTeacherProfile';

interface TeacherLayoutProps {
  children: React.ReactNode;
}

export default function TeacherLayout({ children }: TeacherLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, loading, signOut } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const { displayName, isLoading: isLoadingProfile } = useTeacherDisplayName();
  const { sectionsByGrade, isLoading: isLoadingSections } = useTeacherSectionsByGrade();

  // Check if user is authorized teacher
  useEffect(() => {
    const checkTeacherAccess = async () => {
      if (loading) return; // Wait for auth to load

      if (!user) {
        // No user logged in, redirect to teacher login
        router.push('/login/teacher');
        return;
      }

      try {
        // Check if user is a teacher in the teachers collection
        const teachersQuery = query(
          collection(db, "teachers"),
          where("email", "==", user.email),
          where("status", "==", "active")
        );
        const teacherSnapshot = await getDocs(teachersQuery);

        if (!teacherSnapshot.empty) {
          setIsAuthorized(true);
          setIsCheckingAuth(false);
          return;
        }

        // If not found in teachers collection, deny access
        setIsAuthorized(false);
        setIsCheckingAuth(false);
        await signOut();
        router.push('/login/teacher');
      } catch (error) {
        console.error("Error checking teacher access:", error);
        setIsAuthorized(false);
        setIsCheckingAuth(false);
        router.push('/login/teacher');
      }
    };

    checkTeacherAccess();
  }, [user, loading, router, signOut]);

  const handleLogout = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const isActiveRoute = (href: string) => {
    if (href === '/teacher/dashboard') {
      return pathname === '/teacher/dashboard';
    }
    return pathname.startsWith(href);
  };

  const handleSectionClick = (gradeId: string, sectionId: string) => {
    router.push(`/teacher/section/${gradeId}/${sectionId}`);
  };

  // Show loading while checking authentication
  if (loading || isCheckingAuth) {
    return (
      <div className="min-h-screen bg-[#FAFAF6] flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-[#84a98c] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-[#84a98c] font-medium">Verifying teacher access...</p>
        </div>
      </div>
    );
  }

  // Don't render anything if not authorized (will redirect)
  if (!isAuthorized) {
    return null;
  }

  return (
    <div className="min-h-screen bg-[#FAFAF6] flex flex-col theme-teacher">
      {/* Header - Sticky */}
      <header className="sticky top-0 z-50 w-full border-b-2 border-[#84a98c] bg-white px-8 py-4 flex items-center justify-between">
        <div className="flex items-center">
          <AcademicCapIcon className="w-8 h-8 text-[#84a98c] mr-3" />
          <h1 className="text-2xl font-bold text-[#84a98c]">Teacher Dashboard</h1>
        </div>
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-600">
            Welcome, {isLoadingProfile ? 'Loading...' : displayName || user?.email}
          </span>
          <button
            onClick={handleLogout}
            className="flex items-center space-x-2 px-4 py-2 border-2 border-[#84a98c] text-[#84a98c] hover:bg-[#84a98c] hover:text-white transition-colors duration-200 cursor-pointer"
          >
            <PowerIcon className="w-4 h-4" />
            <span>Logout</span>
          </button>
        </div>
      </header>

      <div className="flex flex-1">
        {/* Sidebar Navigation - Sticky */}
        <nav className="sticky top-[73px] h-[calc(100vh-73px)] w-80 bg-white border-r-2 border-[#84a98c] p-6 flex flex-col overflow-y-auto">
          {/* Main Navigation */}
          <div className="mb-6">
            <button
              onClick={() => router.push('/teacher/dashboard')}
              className={`w-full flex items-center space-x-3 px-4 py-3 text-left transition-colors duration-200 cursor-pointer mb-2 ${
                isActiveRoute('/teacher/dashboard')
                  ? 'bg-[#84a98c] text-white shadow-md'
                  : 'text-[#84a98c] hover:bg-[#84a98c]/10 hover:text-[#6b8e73]'
              }`}
            >
              <ClipboardDocumentListIcon className="w-5 h-5" />
              <span className="font-medium">Dashboard</span>
            </button>
          </div>

          {/* Sections by Grade */}
          <div className="flex-1">
            <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-3">
              My Sections
            </h3>
            
            {isLoadingSections ? (
              <div className="text-center py-4">
                <div className="w-6 h-6 border-2 border-[#84a98c] border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                <p className="text-sm text-gray-500">Loading sections...</p>
              </div>
            ) : sectionsByGrade.length === 0 ? (
              <div className="text-center py-4">
                <BookOpenIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">No sections assigned</p>
              </div>
            ) : (
              <div className="space-y-4">
                {sectionsByGrade.map((grade) => (
                  <div key={grade.gradeId} className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700 px-2">
                      {grade.gradeName}
                    </h4>
                    <div className="space-y-1">
                      {Object.values(grade.sections).map((section) => (
                        <button
                          key={section.sectionId}
                          onClick={() => handleSectionClick(grade.gradeId, section.sectionId)}
                          className={`w-full flex items-center justify-between px-3 py-2 text-left text-sm transition-colors duration-200 cursor-pointer rounded-md ${
                            pathname === `/teacher/section/${grade.gradeId}/${section.sectionId}`
                              ? 'bg-[#84a98c] text-white shadow-sm'
                              : 'text-gray-700 hover:bg-[#84a98c]/10 hover:text-[#6b8e73]'
                          }`}
                        >
                          <span>{section.sectionName}</span>
                          <span className="text-xs opacity-75">
                            {section.subjects.length} subject{section.subjects.length !== 1 ? 's' : ''}
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          {/* Profile Button at Bottom */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <button
              onClick={() => router.push('/teacher/profile')}
              className={`w-full flex items-center space-x-3 px-4 py-3 text-left transition-colors duration-200 cursor-pointer ${
                pathname === '/teacher/profile'
                  ? 'bg-[#84a98c] text-white shadow-md'
                  : 'text-[#84a98c] hover:bg-[#84a98c]/10 hover:text-[#6b8e73]'
              }`}
            >
              <UserIcon className="w-5 h-5" />
              <span className="font-medium">Profile</span>
            </button>
          </div>
        </nav>

        {/* Main Content - Scrollable */}
        <main className="flex-1 p-8 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
}
