const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
try {
  const serviceAccount = require('../serviceAccountKey.json');
  
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: 'greentech-assessment'
  });

  console.log('✅ Firebase Admin initialized successfully');
} catch (error) {
  console.error('❌ Error initializing Firebase:', error.message);
  process.exit(1);
}

const db = admin.firestore();

async function createSettingsCollection() {
  try {
    console.log('⚙️ Creating Settings collection...\n');
    
    // Create default settings document
    const defaultSettings = {
      // Delete functionality toggles
      allowDeleteSections: false,
      allowDeleteSubjects: false,
      allowDeleteGrades: false,
      allowDeleteStudents: true,
      allowDeleteTeachers: true,
      allowDeleteExams: true,
      
      // System settings
      systemName: 'GreenTech Assessment System',
      schoolName: 'Pinagbarilan Elementary School',
      schoolYear: '2025-2026',
      
      // UI settings
      itemsPerPage: 9,
      enableDarkMode: false,
      enableNotifications: true,
      
      // Exam settings
      defaultExamDuration: 60, // minutes
      allowRetakeExams: false,
      showCorrectAnswers: true,
      
      // Security settings
      sessionTimeout: 30, // minutes
      requirePasswordChange: false,
      enableTwoFactorAuth: false,
      
      // Backup settings
      autoBackup: true,
      backupFrequency: 'weekly',
      
      // Metadata
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      version: '1.0.0'
    };
    
    // Create the settings document
    const settingsRef = db.collection('settings').doc('system');
    await settingsRef.set(defaultSettings);
    
    console.log('✅ Settings collection created successfully!');
    console.log('\n📋 Default Settings:');
    console.log('   🗑️ Delete Permissions:');
    console.log('      • Sections: ❌ Disabled (Critical)');
    console.log('      • Subjects: ❌ Disabled (Critical)');
    console.log('      • Grades: ❌ Disabled (Critical)');
    console.log('      • Students: ✅ Enabled (Variable)');
    console.log('      • Teachers: ✅ Enabled (Variable)');
    console.log('      • Exams: ✅ Enabled (Variable)');
    
    console.log('\n   ⚙️ System Settings:');
    console.log(`      • School: ${defaultSettings.schoolName}`);
    console.log(`      • School Year: ${defaultSettings.schoolYear}`);
    console.log(`      • Items per page: ${defaultSettings.itemsPerPage}`);
    
    console.log('\n   📝 Exam Settings:');
    console.log(`      • Default duration: ${defaultSettings.defaultExamDuration} minutes`);
    console.log(`      • Allow retakes: ${defaultSettings.allowRetakeExams ? '✅' : '❌'}`);
    console.log(`      • Show correct answers: ${defaultSettings.showCorrectAnswers ? '✅' : '❌'}`);
    
  } catch (error) {
    console.error('❌ Error creating settings:', error);
    throw error;
  }
}

// Run the script
createSettingsCollection()
  .then(() => {
    console.log('\n🎉 Settings collection setup completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Settings setup failed:', error);
    process.exit(1);
  });
