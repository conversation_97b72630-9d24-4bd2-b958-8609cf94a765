import { NextRequest, NextResponse } from 'next/server';
import { collection, getDocs, deleteDoc, doc, addDoc, Timestamp } from 'firebase/firestore';
import { db } from '../../../lib/firebase';

export async function POST(request: NextRequest) {
  try {
    console.log('Starting student collection flush...');

    // Step 1: Delete all existing students
    const studentsCollection = collection(db, 'students');
    const studentsSnapshot = await getDocs(studentsCollection);
    
    console.log(`Found ${studentsSnapshot.size} students to delete`);
    
    const deletePromises = studentsSnapshot.docs.map(studentDoc => 
      deleteDoc(doc(db, 'students', studentDoc.id))
    );
    
    await Promise.all(deletePromises);
    console.log('All existing students deleted');

    // Step 2: Get grades and sections for reference
    const gradesSnapshot = await getDocs(collection(db, 'grades'));
    const sectionsSnapshot = await getDocs(collection(db, 'sections'));
    
    const grades = gradesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    const sections = sectionsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    
    console.log(`Found ${grades.length} grades and ${sections.length} sections`);

    // Step 3: Create sample students with correct structure
    const sampleStudents = [
      {
        fullName: ["Juan", "Carlos", "Dela Cruz"],
        extension: "",
        gradeId: grades[0]?.id || "grade1",
        sectionId: sections[0]?.id || "section1", 
        studentId: "STU001",
        lrn: "129848291239",
        qr_code: "",
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        fullName: ["Maria", "Santos", "Garcia"],
        extension: "",
        gradeId: grades[0]?.id || "grade1",
        sectionId: sections[0]?.id || "section1",
        studentId: "STU002", 
        lrn: "129848291240",
        qr_code: "",
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        fullName: ["Pedro", "Miguel", "Rodriguez"],
        extension: "Jr.",
        gradeId: grades[1]?.id || "grade2",
        sectionId: sections[1]?.id || "section2",
        studentId: "STU003",
        lrn: "129848291241", 
        qr_code: "",
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        fullName: ["Ana", "Luz", "Fernandez"],
        extension: "",
        gradeId: grades[1]?.id || "grade2", 
        sectionId: sections[1]?.id || "section2",
        studentId: "STU004",
        lrn: "129848291242",
        qr_code: "",
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        fullName: ["Carlos", "Antonio", "Mendoza"],
        extension: "",
        gradeId: grades[2]?.id || "grade3",
        sectionId: sections[2]?.id || "section3", 
        studentId: "STU005",
        lrn: "129848291243",
        qr_code: "",
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }
    ];

    // Step 4: Add new students with correct structure
    const addPromises = sampleStudents.map(student => 
      addDoc(collection(db, 'students'), student)
    );
    
    const addedStudents = await Promise.all(addPromises);
    console.log(`Created ${addedStudents.length} new students`);

    return NextResponse.json({
      success: true,
      message: `Successfully flushed and recreated student collection`,
      data: {
        deletedCount: studentsSnapshot.size,
        createdCount: addedStudents.length,
        availableGrades: grades.length,
        availableSections: sections.length
      }
    });

  } catch (error) {
    console.error('Error flushing students:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to flush student collection',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
