# 🚀 GreenTech Development Guide

## 📚 **New Libraries & Tools**

### ✅ **Installed Libraries:**
- **React Hook Form** - Advanced form management
- **Zod** - Schema validation
- **TanStack Query** - Server state management
- **React Hot Toast** - Toast notifications
- **ShadCN/UI** - Component library
- **Date-fns** - Date utilities

---

## 🔧 **React Hook Form + Zod**

### **Benefits:**
- ✅ Better performance (fewer re-renders)
- ✅ Built-in validation with TypeScript support
- ✅ Less boilerplate code
- ✅ Better error handling

### **Usage Example:**
```tsx
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { studentSchema, type StudentFormData } from "@/lib/schemas";

const form = useForm<StudentFormData>({
  resolver: zodResolver(studentSchema),
  defaultValues: {
    firstName: "",
    lastName: "",
    // ...
  },
});

const handleSubmit = async (data: StudentFormData) => {
  // Data is automatically validated and typed!
  await onSubmit(data);
};
```

### **Available Schemas:**
- `studentSchema` - Student registration/editing
- `teacherSchema` - Teacher registration/editing  
- `loginSchema` - Login forms
- `signUpSchema` - Sign up forms
- `examSchema` - Exam creation

---

## 🔄 **TanStack Query (React Query)**

### **Benefits:**
- ✅ Automatic caching and synchronization
- ✅ Background refetching
- ✅ Loading and error states
- ✅ Optimistic updates

### **Usage Example:**
```tsx
import { useStudents, useAddStudent } from "@/hooks/useStudents";

function StudentList() {
  const { data: students, isLoading, error } = useStudents();
  const addStudentMutation = useAddStudent();

  const handleAddStudent = async (data: StudentFormData) => {
    await addStudentMutation.mutateAsync(data);
    // Cache is automatically updated!
  };

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {students?.map(student => (
        <div key={student.id}>{student.firstName}</div>
      ))}
    </div>
  );
}
```

### **Available Hooks:**
- `useStudents()` - Fetch all students
- `useStudentsByGrade(gradeLevel)` - Fetch students by grade
- `useAddStudent()` - Add new student
- `useUpdateStudent()` - Update student
- `useDeleteStudent()` - Delete student
- `useGenerateStudentQR()` - Generate QR code

---

## 🎨 **ShadCN/UI Components**

### **Benefits:**
- ✅ Consistent design system
- ✅ Accessible components
- ✅ Customizable with Tailwind

### **Available Components:**
- `Form` - Form wrapper with validation
- `FormField` - Individual form fields
- `Input` - Text inputs
- `Button` - Already exists, can be replaced
- `Label` - Form labels

### **Usage Example:**
```tsx
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

<Form {...form}>
  <FormField
    control={form.control}
    name="firstName"
    render={({ field }) => (
      <FormItem>
        <FormLabel>First Name</FormLabel>
        <FormControl>
          <Input placeholder="Enter first name" {...field} />
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
</Form>
```

---

## 🔔 **Toast Notifications**

### **Usage:**
```tsx
import toast from "react-hot-toast";

// Success
toast.success("Student registered successfully!");

// Error
toast.error("Failed to register student");

// Loading
const toastId = toast.loading("Registering student...");
toast.success("Done!", { id: toastId });
```

---

## 📅 **Date Utilities**

### **Usage:**
```tsx
import { format, parseISO, isAfter } from "date-fns";

// Format dates
const formatted = format(new Date(), "PPP"); // "January 1st, 2024"

// Parse ISO strings
const date = parseISO("2024-01-01T00:00:00.000Z");

// Compare dates
const isLater = isAfter(date1, date2);
```

---

## 🔄 **Migration Strategy**

### **Phase 1: Forms (Current)**
1. ✅ Created example `StudentRegistrationForm` with React Hook Form
2. ✅ Created validation schemas in `lib/schemas.ts`
3. 🔄 **Next:** Convert existing forms one by one

### **Phase 2: Data Fetching**
1. ✅ Created example `useStudents` hook
2. 🔄 **Next:** Create hooks for teachers, exams, etc.
3. 🔄 **Next:** Replace manual Firebase calls

### **Phase 3: Components**
1. ✅ Set up ShadCN/UI foundation
2. 🔄 **Next:** Replace custom components gradually
3. 🔄 **Next:** Add more ShadCN components as needed

---

## 🎯 **Next Steps**

### **Immediate (High Priority):**
1. **Convert Student Management** - Replace current form with `StudentRegistrationForm`
2. **Convert Teacher Management** - Create `TeacherRegistrationForm`
3. **Add Teacher Hooks** - Create `useTeachers` similar to `useStudents`

### **Medium Priority:**
1. **Convert Login Forms** - Use React Hook Form + validation
2. **Add Exam Hooks** - Create `useExams` for exam management
3. **Replace Manual Toast** - Use `react-hot-toast` everywhere

### **Low Priority:**
1. **Add More ShadCN Components** - Dialog, Select, etc.
2. **Optimize Queries** - Add more specific query hooks
3. **Add Offline Support** - React Query has built-in offline support

---

## 🛠 **Development Tips**

### **Form Development:**
1. Always start with a Zod schema
2. Use TypeScript types generated from schemas
3. Handle loading states in forms
4. Use toast notifications for feedback

### **Data Fetching:**
1. Use React Query for all Firebase operations
2. Implement optimistic updates for better UX
3. Handle loading and error states
4. Use proper cache invalidation

### **Component Development:**
1. Use ShadCN components for consistency
2. Customize with Tailwind classes
3. Follow the existing design system
4. Ensure accessibility

---

## 📖 **Resources**

- [React Hook Form Docs](https://react-hook-form.com/)
- [Zod Documentation](https://zod.dev/)
- [TanStack Query Docs](https://tanstack.com/query/latest)
- [ShadCN/UI Components](https://ui.shadcn.com/)
- [React Hot Toast](https://react-hot-toast.com/)
