"use client";
import React, { useState } from 'react';
import { CogIcon, ShieldCheckIcon, TrashIcon, CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { useSettings, useUpdateSettings } from '@/hooks/useSettings';

export default function SettingsPage() {
  const { data: settings, isLoading } = useSettings();
  const updateSettingsMutation = useUpdateSettings();
  const [localSettings, setLocalSettings] = useState(settings);

  React.useEffect(() => {
    if (settings) {
      setLocalSettings(settings);
    }
  }, [settings]);

  const handleToggle = (key: string) => {
    if (!localSettings) return;

    const newSettings = {
      ...localSettings,
      [key]: !localSettings[key as keyof typeof localSettings]
    };

    setLocalSettings(newSettings);
  };

  const handleSave = async () => {
    if (!localSettings) return;
    
    try {
      await updateSettingsMutation.mutateAsync(localSettings);
    } catch (error) {
      console.error('Failed to update settings:', error);
    }
  };

  const hasChanges = JSON.stringify(settings) !== JSON.stringify(localSettings);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <CogIcon className="w-8 h-8 text-[#84a98c] animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-[#84a98c] bg-opacity-10">
          <CogIcon className="w-6 h-6 text-[#84a98c]" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600">Manage system permissions and configurations</p>
        </div>
      </div>

      {/* Delete Permissions Section */}
      <div className="bg-white border-2 border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <ShieldCheckIcon className="w-6 h-6 text-[#84a98c]" />
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Delete Permissions</h2>
            <p className="text-sm text-gray-600">
              Control which entities can be deleted from the system
            </p>
          </div>
        </div>

        <div className="space-y-4">
          {localSettings && getDeletePermissions(localSettings).map(([key, value, label]) => (
            <div key={key} className="flex items-center justify-between p-4 bg-gray-50 border border-gray-200">
              <div className="flex items-center space-x-3">
                <TrashIcon className="w-5 h-5 text-gray-400" />
                <div>
                  <h3 className="font-medium text-gray-900">
                    {label}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {getPermissionDescription(key)}
                  </p>
                </div>
              </div>

              <button
                onClick={() => handleToggle(key)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors cursor-pointer ${
                  value ? 'bg-[#84a98c]' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    value ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          ))}
        </div>

        {/* Save Button */}
        {hasChanges && (
          <div className="mt-6 flex items-center justify-between p-4 bg-blue-50 border border-blue-200">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm text-blue-700">You have unsaved changes</span>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setLocalSettings(settings)}
                disabled={updateSettingsMutation.isPending}
              >
                <XMarkIcon className="w-4 h-4 mr-1" />
                Cancel
              </Button>
              <Button
                variant="filled"
                size="sm"
                onClick={handleSave}
                loading={updateSettingsMutation.isPending}
              >
                <CheckIcon className="w-4 h-4 mr-1" />
                Save Changes
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* System Information */}
      <div className="bg-white border-2 border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">System Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-gray-50 border border-gray-200">
            <h3 className="font-medium text-gray-900">Version</h3>
            <p className="text-sm text-gray-600">GreenTech v1.0.0</p>
          </div>
          <div className="p-4 bg-gray-50 border border-gray-200">
            <h3 className="font-medium text-gray-900">Environment</h3>
            <p className="text-sm text-gray-600">Development</p>
          </div>
          <div className="p-4 bg-gray-50 border border-gray-200">
            <h3 className="font-medium text-gray-900">Database</h3>
            <p className="text-sm text-gray-600">Firebase Firestore</p>
          </div>
          <div className="p-4 bg-gray-50 border border-gray-200">
            <h3 className="font-medium text-gray-900">Last Updated</h3>
            <p className="text-sm text-gray-600">{new Date().toLocaleDateString()}</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function getDeletePermissions(settings: any): [string, boolean, string][] {
  return [
    ['allowDeleteSections', settings.allowDeleteSections || false, 'Delete Sections'],
    ['allowDeleteSubjects', settings.allowDeleteSubjects || false, 'Delete Subjects'],
    ['allowDeleteGrades', settings.allowDeleteGrades || false, 'Delete Grades'],
    ['allowDeleteStudents', settings.allowDeleteStudents || false, 'Delete Students'],
    ['allowDeleteTeachers', settings.allowDeleteTeachers || false, 'Delete Teachers'],
    ['allowDeleteExams', settings.allowDeleteExams || false, 'Delete Exams'],
  ];
}

function getPermissionDescription(key: string): string {
  const descriptions: Record<string, string> = {
    allowDeleteSections: 'Allow deletion of grade sections (affects student assignments)',
    allowDeleteSubjects: 'Allow deletion of subjects (affects teacher assignments and exams)',
    allowDeleteGrades: 'Allow deletion of entire grade levels (affects all related data)',
    allowDeleteStudents: 'Allow deletion of student records',
    allowDeleteTeachers: 'Allow deletion of teacher accounts',
    allowDeleteExams: 'Allow deletion of exam records and results'
  };

  return descriptions[key] || 'Manage deletion permissions for this entity';
}
