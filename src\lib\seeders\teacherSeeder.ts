/**
 * Teacher Seeder
 * Creates realistic teacher data with subject assignments following <PERSON>'s schema
 * 
 * Structure: 6-8 teachers with various subject specializations and assignments
 */

import { db } from '@/lib/firebase';
import { collection, addDoc, getDocs, deleteDoc, writeBatch, doc, updateDoc } from 'firebase/firestore';
import { Grade, Subject } from '@/lib/types/school';

// Realistic Filipino teacher names
const teacherNames = [
  { firstName: '<PERSON>', lastName: '<PERSON>', gender: 'female', specialization: 'English' },
  { firstName: '<PERSON>', lastName: '<PERSON>', gender: 'male', specialization: 'Mathematics' },
  { firstName: '<PERSON> <PERSON>', lastName: '<PERSON>', gender: 'female', specialization: 'Science' },
  { firstName: '<PERSON>', lastName: '<PERSON>uti<PERSON>', gender: 'male', specialization: 'Filipino' },
  { firstName: '<PERSON>', lastName: 'Ocampo', gender: 'female', specialization: 'Araling Panlipunan' },
  { firstName: '<PERSON>', lastName: '<PERSON>', gender: 'male', specialization: 'MAPEH' },
  { firstName: '<PERSON>', lastName: '<PERSON>', gender: 'female', specialization: 'ESP' },
  { firstName: '<PERSON>', lastName: '<PERSON>', gender: 'male', specialization: 'TLE' }
];

/**
 * Clear all teachers and teacher assignments from the database
 */
export async function clearTeachers() {
  console.log('🧹 Clearing existing teachers and assignments...');
  
  try {
    // Clear teacher assignments first
    const assignmentsSnapshot = await getDocs(collection(db, 'teacherAssignments'));
    const teachersSnapshot = await getDocs(collection(db, 'teachers'));
    
    const batch = writeBatch(db);
    
    assignmentsSnapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });
    
    teachersSnapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });
    
    if (assignmentsSnapshot.docs.length > 0 || teachersSnapshot.docs.length > 0) {
      await batch.commit();
      console.log(`✅ Cleared ${teachersSnapshot.docs.length} teachers and ${assignmentsSnapshot.docs.length} assignments`);
    } else {
      console.log('ℹ️ No teachers to clear');
    }
  } catch (error) {
    console.error('❌ Error clearing teachers:', error);
    throw error;
  }
}

/**
 * Generate teacher email
 */
function generateTeacherEmail(firstName: string, lastName: string): string {
  const cleanFirst = firstName.toLowerCase().replace(/\s+/g, '');
  const cleanLast = lastName.toLowerCase().replace(/\s+/g, '');
  return `${cleanFirst}.${cleanLast}@greentech.edu.ph`;
}

/**
 * Generate employee ID
 */
function generateEmployeeId(index: number): string {
  const year = new Date().getFullYear();
  const paddedNumber = (index + 1).toString().padStart(3, '0');
  return `T${year}-${paddedNumber}`;
}

/**
 * Create teachers with subject assignments
 */
export async function seedTeachers(grades: Grade[], subjects: Subject[]): Promise<{ teachers: any[]; assignments: any[] }> {
  console.log('👨‍🏫 Creating teachers and assignments...');
  
  const teachers: any[] = [];
  const assignments: any[] = [];
  const now = new Date().toISOString();
  
  try {
    // Create teachers
    console.log('   Creating teacher profiles...');
    for (let i = 0; i < teacherNames.length; i++) {
      const teacherTemplate = teacherNames[i];
      const employeeId = generateEmployeeId(i);
      const email = generateTeacherEmail(teacherTemplate.firstName, teacherTemplate.lastName);
      
      const teacherData = {
        employeeId,
        firstName: teacherTemplate.firstName,
        lastName: teacherTemplate.lastName,
        email,
        gender: teacherTemplate.gender,
        specialization: teacherTemplate.specialization,
        department: 'Elementary',
        position: 'Teacher I',
        hireDate: now,
        isActive: true,
        createdAt: now,
        updatedAt: now
      };
      
      const docRef = await addDoc(collection(db, 'teachers'), teacherData);
      
      const teacher = {
        id: docRef.id,
        ...teacherData
      };
      
      teachers.push(teacher);
      console.log(`     ✅ Created ${teacher.firstName} ${teacher.lastName} (${teacher.specialization})`);
    }
    
    // Create teacher assignments
    console.log('   Creating teacher assignments...');
    for (const teacher of teachers) {
      // Find subjects that match teacher's specialization
      const matchingSubjects = subjects.filter(subject => {
        const subjectName = subject.name.toLowerCase();
        const specialization = teacher.specialization.toLowerCase();
        
        // Match specializations to subjects
        if (specialization.includes('english') && subjectName.includes('english')) return true;
        if (specialization.includes('mathematics') && subjectName.includes('mathematics')) return true;
        if (specialization.includes('science') && subjectName.includes('science')) return true;
        if (specialization.includes('filipino') && subjectName.includes('filipino')) return true;
        if (specialization.includes('araling') && subjectName.includes('araling')) return true;
        if (specialization.includes('mapeh') && subjectName.includes('mapeh')) return true;
        if (specialization.includes('esp') && subjectName.includes('esp')) return true;
        if (specialization.includes('tle') && subjectName.includes('tle')) return true;
        
        return false;
      });
      
      // Assign teacher to their specialized subjects across all grades
      for (const subject of matchingSubjects) {
        const assignmentData = {
          teacherId: teacher.id,
          subjectId: subject.id,
          gradeId: subject.gradeId,
          schoolYear: new Date().getFullYear().toString(),
          isActive: true,
          createdAt: now,
          updatedAt: now
        };
        
        const assignmentRef = await addDoc(collection(db, 'teacherAssignments'), assignmentData);
        
        const assignment = {
          id: assignmentRef.id,
          ...assignmentData
        };
        
        assignments.push(assignment);
        
        // Get grade name for logging
        const grade = grades.find(g => g.id === subject.gradeId);
        console.log(`     ✅ Assigned ${teacher.firstName} ${teacher.lastName} to ${subject.name} (${grade?.name})`);
      }
    }
    
    console.log(`✅ Successfully created ${teachers.length} teachers with ${assignments.length} assignments`);
    return { teachers, assignments };
    
  } catch (error) {
    console.error('❌ Error creating teachers:', error);
    throw error;
  }
}

/**
 * Get all teachers
 */
export async function getAllTeachers(): Promise<any[]> {
  try {
    const teachersSnapshot = await getDocs(collection(db, 'teachers'));
    const teachers: any[] = [];
    
    teachersSnapshot.docs.forEach((doc) => {
      teachers.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    return teachers.sort((a, b) => `${a.lastName}, ${a.firstName}`.localeCompare(`${b.lastName}, ${b.firstName}`));
  } catch (error) {
    console.error('❌ Error getting teachers:', error);
    throw error;
  }
}

/**
 * Get teacher assignments
 */
export async function getTeacherAssignments(teacherId?: string): Promise<any[]> {
  try {
    const assignmentsSnapshot = await getDocs(collection(db, 'teacherAssignments'));
    const assignments: any[] = [];
    
    assignmentsSnapshot.docs.forEach((doc) => {
      const data = doc.data();
      if (!teacherId || data.teacherId === teacherId) {
        assignments.push({
          id: doc.id,
          ...data
        });
      }
    });
    
    return assignments;
  } catch (error) {
    console.error('❌ Error getting teacher assignments:', error);
    throw error;
  }
}

/**
 * Get teachers with their assignments
 */
export async function getTeachersWithAssignments(): Promise<any[]> {
  try {
    const [teachers, assignments, subjects, grades] = await Promise.all([
      getAllTeachers(),
      getTeacherAssignments(),
      getDocs(collection(db, 'subjects')),
      getDocs(collection(db, 'grades'))
    ]);
    
    // Create lookup maps
    const subjectMap = new Map();
    subjects.docs.forEach(doc => {
      subjectMap.set(doc.id, { id: doc.id, ...doc.data() });
    });
    
    const gradeMap = new Map();
    grades.docs.forEach(doc => {
      gradeMap.set(doc.id, { id: doc.id, ...doc.data() });
    });
    
    // Attach assignments to teachers
    return teachers.map(teacher => {
      const teacherAssignments = assignments
        .filter(assignment => assignment.teacherId === teacher.id)
        .map(assignment => ({
          ...assignment,
          subject: subjectMap.get(assignment.subjectId),
          grade: gradeMap.get(assignment.gradeId)
        }));
      
      return {
        ...teacher,
        assignments: teacherAssignments
      };
    });
  } catch (error) {
    console.error('❌ Error getting teachers with assignments:', error);
    throw error;
  }
}

/**
 * Create a single teacher
 */
export async function createTeacher(teacherData: any): Promise<any> {
  const now = new Date().toISOString();
  
  const data = {
    ...teacherData,
    createdAt: now,
    updatedAt: now
  };
  
  try {
    const docRef = await addDoc(collection(db, 'teachers'), data);
    
    return {
      id: docRef.id,
      ...data
    };
  } catch (error) {
    console.error('❌ Error creating teacher:', error);
    throw error;
  }
}

/**
 * Update teacher
 */
export async function updateTeacher(teacherId: string, updates: any): Promise<void> {
  try {
    const teacherRef = doc(db, 'teachers', teacherId);
    await updateDoc(teacherRef, {
      ...updates,
      updatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Error updating teacher:', error);
    throw error;
  }
}

/**
 * Delete teacher
 */
export async function deleteTeacher(teacherId: string): Promise<void> {
  try {
    // Delete teacher assignments first
    const assignments = await getTeacherAssignments(teacherId);
    const batch = writeBatch(db);
    
    assignments.forEach(assignment => {
      batch.delete(doc(db, 'teacherAssignments', assignment.id));
    });
    
    // Delete teacher
    batch.delete(doc(db, 'teachers', teacherId));
    
    await batch.commit();
  } catch (error) {
    console.error('❌ Error deleting teacher:', error);
    throw error;
  }
}
