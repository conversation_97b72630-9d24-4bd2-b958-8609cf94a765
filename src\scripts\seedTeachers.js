const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  const serviceAccount = require('../serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

// New teachers to seed (basic information only)
const newTeachers = [
  {
    firstName: '<PERSON>',
    middleName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    subjects: ['Mathematics', 'Statistics']
  },
  {
    firstName: '<PERSON>',
    middleName: '<PERSON>',
    lastName: '<PERSON>nu<PERSON>',
    email: '<EMAIL>',
    subjects: ['Physical Education', 'Health']
  },
  {
    firstName: 'Isabella',
    middleName: 'Grace',
    lastName: 'Mendoza',
    email: '<EMAIL>',
    subjects: ['Arts', 'Music', 'Creative Writing']
  }
];

async function seedTeachers() {
  try {
    console.log('🌱 Starting Teacher Seeding...\n');
    
    // Check if teachers already exist
    for (const teacherData of newTeachers) {
      const existingTeacher = await db.collection('teachers')
        .where('email', '==', teacherData.email)
        .get();
      
      if (!existingTeacher.empty) {
        console.log(`⚠️  Teacher with email ${teacherData.email} already exists. Skipping...`);
        continue;
      }
      
      // Create standardized teacher document
      const newTeacher = {
        // Basic Information (required)
        firstName: teacherData.firstName,
        middleName: teacherData.middleName,
        lastName: teacherData.lastName,
        email: teacherData.email,
        
        // Assignment fields (empty for now)
        gradeIds: [], // Will be assigned later
        sectionIds: [], // Will be assigned later
        subjects: teacherData.subjects, // Basic subjects assigned
        examIds: [], // Will be populated when exams are created
        
        // Status and metadata
        status: 'active',
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      };
      
      // Add teacher to Firestore
      const docRef = await db.collection('teachers').add(newTeacher);
      
      console.log(`✅ Created teacher: ${teacherData.firstName} ${teacherData.lastName}`);
      console.log(`   📧 Email: ${teacherData.email}`);
      console.log(`   📚 Subjects: ${teacherData.subjects.join(', ')}`);
      console.log(`   🆔 Document ID: ${docRef.id}`);
      console.log('');
    }
    
    console.log('🎉 Teacher seeding completed successfully!');
    console.log('\n📋 Seeded Teachers Summary:');
    console.log('1. Elena Rodriguez Fernandez - Mathematics, Statistics');
    console.log('2. Carlos Miguel Villanueva - Physical Education, Health');
    console.log('3. Isabella Grace Mendoza - Arts, Music, Creative Writing');
    console.log('\n📝 Note: Grade levels and sections can be assigned later through the admin interface.');
    
  } catch (error) {
    console.error('❌ Seeding failed:', error);
  }
}

seedTeachers().then(() => {
  console.log('\n✅ Seeding script completed!');
  process.exit(0);
}).catch(error => {
  console.error('❌ Seeding script failed:', error);
  process.exit(1);
});
