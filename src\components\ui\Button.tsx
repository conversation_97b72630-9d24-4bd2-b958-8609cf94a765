import React, { useState } from 'react';
import clsx from 'clsx';

type ButtonVariant = 'filled' | 'outlined' | 'ghost' | 'soft';
type ButtonSize = 'sm' | 'md' | 'lg' | 'xl';
type ButtonTheme = 'primary' | 'student' | 'teacher' | 'admin' | 'success' | 'error' | 'warning' | 'neutral';
type ButtonRadius = 'none' | 'sm' | 'md' | 'lg' | 'full';

const getButtonClasses = (
  variant: ButtonVariant = 'filled',
  size: ButtonSize = 'md',
  theme: ButtonTheme = 'primary',
  radius: ButtonRadius = 'md',
  fullWidth: boolean = false
) => {
  const baseClasses = "inline-flex items-center justify-center font-semibold transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer";

  const sizeClasses = {
    sm: "px-3 py-2 text-sm gap-1.5",
    md: "px-4 py-2.5 text-base gap-2",
    lg: "px-6 py-3 text-lg gap-2.5",
    xl: "px-8 py-4 text-xl gap-3",
  };

  const radiusClasses = {
    none: "rounded-none",
    sm: "rounded-none",
    md: "rounded-none",
    lg: "rounded-none",
    full: "rounded-none",
  };

  const widthClasses = fullWidth ? "w-full" : "";

  // Theme-specific classes
  const getThemeClasses = () => {
    const themeMap = {
      primary: {
        filled: "bg-[#84a98c] hover:bg-[#6b8e72] text-white border-2 border-[#84a98c] hover:border-[#6b8e72] focus:ring-[#84a98c]",
        outlined: "border-2 border-[#84a98c] text-[#6b8e72] hover:bg-[#f7faf8] focus:ring-[#84a98c] bg-transparent",
        ghost: "text-[#6b8e72] hover:bg-[#f7faf8] focus:ring-[#84a98c] bg-transparent",
        soft: "bg-[#f7faf8] text-[#6b8e72] hover:bg-[#eff5f1] focus:ring-[#84a98c]",
      },
      student: {
        filled: "bg-[#5a9cff] hover:bg-[#4285f4] text-white border-2 border-[#5a9cff] hover:border-[#4285f4] focus:ring-[#5a9cff]",
        outlined: "border-2 border-[#5a9cff] text-[#4285f4] hover:bg-[#f0f7ff] focus:ring-[#5a9cff] bg-transparent",
        ghost: "text-[#4285f4] hover:bg-[#f0f7ff] focus:ring-[#5a9cff] bg-transparent",
        soft: "bg-[#f0f7ff] text-[#4285f4] hover:bg-[#e0efff] focus:ring-[#5a9cff]",
      },
      teacher: {
        filled: "bg-[#b084ff] hover:bg-[#9c6bff] text-white border-2 border-[#b084ff] hover:border-[#9c6bff] focus:ring-[#b084ff]",
        outlined: "border-2 border-[#b084ff] text-[#9c6bff] hover:bg-[#faf7ff] focus:ring-[#b084ff] bg-transparent",
        ghost: "text-[#9c6bff] hover:bg-[#faf7ff] focus:ring-[#b084ff] bg-transparent",
        soft: "bg-[#faf7ff] text-[#9c6bff] hover:bg-[#f4efff] focus:ring-[#b084ff]",
      },
      admin: {
        filled: "bg-slate-800 hover:bg-slate-900 text-white border-2 border-slate-800 hover:border-slate-900 focus:ring-slate-600",
        outlined: "border-2 border-slate-800 text-slate-800 hover:bg-slate-50 focus:ring-slate-600 bg-transparent",
        ghost: "text-slate-800 hover:bg-slate-100 focus:ring-slate-600 bg-transparent",
        soft: "bg-slate-100 text-slate-800 hover:bg-slate-200 focus:ring-slate-600",
      },
      success: {
        filled: "bg-green-600 hover:bg-green-700 text-white border-2 border-green-600 hover:border-green-700 focus:ring-green-500",
        outlined: "border-2 border-green-600 text-green-700 hover:bg-green-50 focus:ring-green-500 bg-transparent",
        ghost: "text-green-700 hover:bg-green-100 focus:ring-green-500 bg-transparent",
        soft: "bg-green-100 text-green-800 hover:bg-green-200 focus:ring-green-500",
      },
      error: {
        filled: "bg-red-600 hover:bg-red-700 text-white border-2 border-red-600 hover:border-red-700 focus:ring-red-500",
        outlined: "border-2 border-red-600 text-red-700 hover:bg-red-50 focus:ring-red-500 bg-transparent",
        ghost: "text-red-700 hover:bg-red-100 focus:ring-red-500 bg-transparent",
        soft: "bg-red-100 text-red-800 hover:bg-red-200 focus:ring-red-500",
      },
      warning: {
        filled: "bg-yellow-600 hover:bg-yellow-700 text-white border-2 border-yellow-600 hover:border-yellow-700 focus:ring-yellow-500",
        outlined: "border-2 border-yellow-600 text-yellow-700 hover:bg-yellow-50 focus:ring-yellow-500 bg-transparent",
        ghost: "text-yellow-700 hover:bg-yellow-100 focus:ring-yellow-500 bg-transparent",
        soft: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200 focus:ring-yellow-500",
      },
      neutral: {
        filled: "bg-gray-600 hover:bg-gray-700 text-white border-2 border-gray-600 hover:border-gray-700 focus:ring-gray-500",
        outlined: "border-2 border-gray-600 text-gray-700 hover:bg-gray-50 focus:ring-gray-500 bg-transparent",
        ghost: "text-gray-700 hover:bg-gray-100 focus:ring-gray-500 bg-transparent",
        soft: "bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-500",
      },
    };

    return themeMap[theme][variant];
  };

  return clsx(
    baseClasses,
    sizeClasses[size],
    radiusClasses[radius],
    widthClasses,
    getThemeClasses()
  );
};

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  theme?: ButtonTheme;
  radius?: ButtonRadius;
  fullWidth?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loading?: boolean;
  loadingText?: string;
  href?: string;
  navigationLoading?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant = 'filled',
      size = 'md',
      theme = 'primary',
      radius = 'md',
      fullWidth = false,
      children,
      leftIcon,
      rightIcon,
      loading = false,
      loadingText,
      disabled,
      href,
      navigationLoading = false,
      onClick,
      ...props
    },
    ref
  ) => {
    const [isButtonLoading, setIsButtonLoading] = useState(false);
    const isLoading = loading || isButtonLoading;
    const isDisabled = disabled || isLoading;

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (href && navigationLoading && !isDisabled) {
        setIsButtonLoading(true);
        // Start loading immediately and let it persist until page loads
        if (onClick) onClick(e);
        // Don't reset loading state - let the page navigation handle it
      } else if (onClick) {
        onClick(e);
      }
    };

    return (
      <button
        className={clsx(
          getButtonClasses(variant, size, theme, radius, fullWidth),
          className
        )}
        ref={ref}
        disabled={isDisabled}
        onClick={handleClick}
        {...props}
      >
        {isLoading && (
          <svg
            className="animate-spin h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        )}
        {!isLoading && leftIcon && leftIcon}
        <span>{isLoading && loadingText ? loadingText : children}</span>
        {!isLoading && rightIcon && (isButtonLoading ? (
          <svg
            className="animate-spin h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        ) : rightIcon)}
      </button>
    );
  }
);

Button.displayName = "Button";

export { Button };
